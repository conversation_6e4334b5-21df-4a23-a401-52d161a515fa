import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/puppies - Получение списка щенков
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const gender = searchParams.get('gender');
    const status = searchParams.get('status');
    
    // Формируем условия фильтрации
    const where: any = { isPublished: true };
    
    if (gender) {
      where.gender = gender;
    }
    
    if (status) {
      where.status = status;
    }
    
    const puppies = await prisma.puppy.findMany({
      where,
      include: {
        photos: {
          where: { isMain: true },
          take: 1
        },
        breeding: {
          include: {
            mother: true,
            father: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return NextResponse.json(puppies);
  } catch (error) {
    console.error('Ошибка при получении списка щенков:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении списка щенков' },
      { status: 500 }
    );
  }
}

// POST /api/puppies - Создание нового щенка (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const data = await request.json();
    
    // Создаем нового щенка
    const puppy = await prisma.puppy.create({
      data: {
        name: data.name,
        gender: data.gender,
        birthDate: new Date(data.birthDate),
        color: data.color,
        description: data.description,
        slug: data.slug,
        status: data.status || 'AVAILABLE',
        price: data.price ? parseFloat(data.price) : null,
        isPublished: data.isPublished || true,
        breedingId: data.breedingId || null
      }
    });
    
    return NextResponse.json(puppy, { status: 201 });
  } catch (error) {
    console.error('Ошибка при создании щенка:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании щенка' },
      { status: 500 }
    );
  }
}
