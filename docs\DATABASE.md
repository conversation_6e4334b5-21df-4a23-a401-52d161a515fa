# 🗄️ Схема базы данных

## Обзор

База данных построена на PostgreSQL с использованием Prisma ORM. Схема спроектирована для эффективного хранения данных питомника с поддержкой всех основных функций сайта.

## 📊 ER-диаграмма

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      User       │    │      Dog        │    │     Puppy       │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ email           │    │ name            │    │ name            │
│ name            │    │ breed           │    │ breed           │
│ role            │    │ gender          │    │ gender          │
│ password        │    │ birthDate       │    │ birthDate       │
│ createdAt       │    │ color           │    │ color           │
│ updatedAt       │    │ weight          │    │ price           │
└─────────────────┘    │ height          │    │ isAvailable     │
                       │ description     │    │ description     │
                       │ images          │    │ images          │
                       │ isGraduate      │    │ parentMale      │
                       │ createdAt       │    │ parentFemale    │
                       │ updatedAt       │    │ createdAt       │
                       └─────────────────┘    │ updatedAt       │
                                             └─────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      News       │    │   HeroSlide     │    │    Settings     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ title           │    │ title           │    │ key (UNIQUE)    │
│ content         │    │ subtitle        │    │ value           │
│ excerpt         │    │ imageUrl        │    │ description     │
│ slug            │    │ buttonText      │    │ createdAt       │
│ featuredImage   │    │ buttonLink      │    │ updatedAt       │
│ isPublished     │    │ order           │    └─────────────────┘
│ publishedAt     │    │ isActive        │
│ authorId (FK)   │    │ createdAt       │
│ createdAt       │    │ updatedAt       │
│ updatedAt       │    └─────────────────┘
└─────────────────┘
        │
        └─────────────────┐
                         │
                    ┌─────▼─────┐
                    │   User    │
                    │ (Author)  │
                    └───────────┘
```

## 📋 Модели данных

### 👤 User (Пользователи)
Хранит информацию о пользователях системы (администраторы и обычные пользователи).

```prisma
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Связи
  news      News[]   @relation("NewsAuthor")
}

enum Role {
  USER
  ADMIN
}
```

**Поля:**
- `id` - Уникальный идентификатор (CUID)
- `email` - Email пользователя (уникальный)
- `name` - Имя пользователя
- `password` - Хэшированный пароль
- `role` - Роль пользователя (USER/ADMIN)
- `createdAt` - Дата создания
- `updatedAt` - Дата последнего обновления

### 🐕 Dog (Собаки)
Основная модель для хранения информации о собаках питомника.

```prisma
model Dog {
  id          String    @id @default(cuid())
  name        String
  breed       String    @default("Сиба-ину")
  gender      Gender
  birthDate   DateTime
  color       String
  weight      Float?
  height      Float?
  description String?
  images      String[]  @default([])
  isGraduate  Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

enum Gender {
  MALE
  FEMALE
}
```

**Поля:**
- `id` - Уникальный идентификатор
- `name` - Кличка собаки
- `breed` - Порода (по умолчанию "Сиба-ину")
- `gender` - Пол (MALE/FEMALE)
- `birthDate` - Дата рождения
- `color` - Окрас
- `weight` - Вес в кг (опционально)
- `height` - Рост в см (опционально)
- `description` - Описание и характер
- `images` - Массив URL изображений
- `isGraduate` - Флаг выпускника (для страницы выпускников)

### 🐶 Puppy (Щенки)
Модель для щенков, доступных для продажи.

```prisma
model Puppy {
  id           String    @id @default(cuid())
  name         String
  breed        String    @default("Сиба-ину")
  gender       Gender
  birthDate    DateTime
  color        String
  price        Int
  isAvailable  Boolean   @default(true)
  description  String?
  images       String[]  @default([])
  parentMale   String?
  parentFemale String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
}
```

**Поля:**
- `id` - Уникальный идентификатор
- `name` - Кличка щенка
- `breed` - Порода
- `gender` - Пол
- `birthDate` - Дата рождения
- `color` - Окрас
- `price` - Цена в рублях
- `isAvailable` - Доступность для продажи
- `description` - Описание щенка
- `images` - Массив URL изображений
- `parentMale` - Кличка отца (опционально)
- `parentFemale` - Кличка матери (опционально)

### 📰 News (Новости)
Модель для новостей и статей сайта.

```prisma
model News {
  id             String    @id @default(cuid())
  title          String
  content        String
  excerpt        String?
  slug           String    @unique
  featuredImage  String?
  isPublished    Boolean   @default(false)
  publishedAt    DateTime?
  authorId       String
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  
  // Связи
  author         User      @relation("NewsAuthor", fields: [authorId], references: [id])
}
```

**Поля:**
- `id` - Уникальный идентификатор
- `title` - Заголовок новости
- `content` - Полный текст (HTML)
- `excerpt` - Краткое описание
- `slug` - URL-friendly идентификатор
- `featuredImage` - Главное изображение
- `isPublished` - Статус публикации
- `publishedAt` - Дата публикации
- `authorId` - ID автора (связь с User)

### 🎨 HeroSlide (Слайды главной страницы)
Модель для управления слайдами на главной странице.

```prisma
model HeroSlide {
  id          String   @id @default(cuid())
  title       String?
  subtitle    String?
  imageUrl    String
  buttonText  String?
  buttonLink  String?
  order       Int      @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

**Поля:**
- `id` - Уникальный идентификатор
- `title` - Заголовок слайда (опционально)
- `subtitle` - Подзаголовок (опционально)
- `imageUrl` - URL изображения слайда
- `buttonText` - Текст кнопки (опционально)
- `buttonLink` - Ссылка кнопки (опционально)
- `order` - Порядок отображения
- `isActive` - Активность слайда

### ⚙️ Settings (Настройки)
Модель для хранения настроек сайта в формате ключ-значение.

```prisma
model Settings {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

**Поля:**
- `id` - Уникальный идентификатор
- `key` - Ключ настройки (уникальный)
- `value` - Значение настройки
- `description` - Описание настройки

**Примеры настроек:**
- `siteName` - Название сайта
- `logo` - URL логотипа
- `favicon` - URL фавикона
- `aboutImage` - Изображение для раздела "О нас"
- `contactEmail` - Email для связи
- `contactPhone` - Телефон
- `address` - Адрес питомника
- `metaTitle` - SEO заголовок
- `metaDescription` - SEO описание

## 🔗 Связи между моделями

### User ↔ News
- **Тип**: One-to-Many
- **Описание**: Один пользователь может быть автором множества новостей
- **Поля**: `News.authorId` → `User.id`

## 📝 Индексы и ограничения

### Уникальные ключи
- `User.email` - Уникальный email
- `News.slug` - Уникальный slug для SEO URL
- `Settings.key` - Уникальный ключ настройки

### Индексы для производительности
```sql
-- Автоматически создаются Prisma
CREATE INDEX idx_news_published ON "News"("isPublished", "publishedAt");
CREATE INDEX idx_puppies_available ON "Puppy"("isAvailable");
CREATE INDEX idx_dogs_graduate ON "Dog"("isGraduate");
CREATE INDEX idx_hero_slides_active ON "HeroSlide"("isActive", "order");
```

## 🚀 Миграции

### Создание миграции
```bash
npx prisma migrate dev --name migration_name
```

### Применение миграций
```bash
npx prisma migrate deploy
```

### Сброс базы данных
```bash
npx prisma migrate reset
```

## 🌱 Начальные данные (Seed)

Файл `prisma/seed.ts` содержит начальные данные:

```typescript
// Создание администратора
await prisma.user.create({
  data: {
    email: '<EMAIL>',
    name: 'Администратор',
    password: hashedPassword,
    role: 'ADMIN'
  }
})

// Базовые настройки сайта
await prisma.settings.createMany({
  data: [
    { key: 'siteName', value: 'Питомник Сиба-ину' },
    { key: 'contactEmail', value: '<EMAIL>' },
    // ...
  ]
})
```

### Запуск seed
```bash
npx prisma db seed
```

## 🔧 Полезные команды

### Просмотр данных
```bash
npx prisma studio
```

### Генерация клиента
```bash
npx prisma generate
```

### Проверка схемы
```bash
npx prisma validate
```

### Форматирование схемы
```bash
npx prisma format
```

## 📊 Статистика и аналитика

Для получения статистики можно использовать агрегатные запросы:

```typescript
// Общее количество собак
const totalDogs = await prisma.dog.count()

// Количество доступных щенков
const availablePuppies = await prisma.puppy.count({
  where: { isAvailable: true }
})

// Количество опубликованных новостей
const publishedNews = await prisma.news.count({
  where: { isPublished: true }
})
```

Эта схема обеспечивает все необходимые функции для полноценного сайта питомника с возможностью расширения в будущем.
