'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Table from '@/components/ui/Table';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { slugify } from '@/lib/utils';

interface Category {
  id: string;
  name: string;
  slug: string;
  albumCount: number;
}

const categorySchema = z.object({
  name: z.string().min(1, 'Название категории обязательно'),
});

type CategoryFormData = z.infer<typeof categorySchema>;

export default function GalleryCategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
  });

  const fetchCategories = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/gallery/categories');
      
      if (!response.ok) {
        throw new Error('Ошибка при загрузке данных');
      }
      
      const data = await response.json();
      setCategories(data);
    } catch (err) {
      setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при загрузке категорий:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const onSubmit = async (data: CategoryFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Создаем slug из названия категории
      const slug = slugify(data.name);
      
      if (editingCategory) {
        // Обновляем существующую категорию
        const response = await fetch(`/api/gallery/categories/${editingCategory.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...data, slug }),
        });

        if (!response.ok) {
          throw new Error('Ошибка при обновлении категории');
        }

        setSuccess('Категория успешно обновлена');
      } else {
        // Создаем новую категорию
        const response = await fetch('/api/gallery/categories', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...data, slug }),
        });

        if (!response.ok) {
          throw new Error('Ошибка при создании категории');
        }

        setSuccess('Категория успешно создана');
      }
      
      // Обновляем список категорий
      fetchCategories();
      
      // Сбрасываем форму
      reset();
      setEditingCategory(null);
    } catch (err) {
      console.error('Ошибка при сохранении категории:', err);
      setError('Произошла ошибка при сохранении категории. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    reset({ name: category.name });
  };

  const handleCancelEdit = () => {
    setEditingCategory(null);
    reset({ name: '' });
  };

  const handleDeleteClick = (category: Category) => {
    setCategoryToDelete(category);
  };

  const handleDeleteConfirm = async () => {
    if (!categoryToDelete) return;
    
    setIsDeleting(true);
    
    try {
      const response = await fetch(`/api/gallery/categories/${categoryToDelete.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при удалении категории');
      }
      
      setSuccess('Категория успешно удалена');
      
      // Обновляем список категорий
      fetchCategories();
      
      // Сбрасываем состояние
      setCategoryToDelete(null);
    } catch (err) {
      console.error('Ошибка при удалении категории:', err);
      setError('Произошла ошибка при удалении категории. Пожалуйста, попробуйте позже.');
    } finally {
      setIsDeleting(false);
    }
  };

  const columns = [
    {
      header: 'Название',
      accessor: 'name',
    },
    {
      header: 'Slug',
      accessor: 'slug',
    },
    {
      header: 'Количество альбомов',
      accessor: 'albumCount',
    },
    {
      header: 'Действия',
      accessor: (category: Category) => (
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleEdit(category);
            }}
          >
            Редактировать
          </Button>
          <Button
            variant="danger"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteClick(category);
            }}
            disabled={category.albumCount > 0}
          >
            Удалить
          </Button>
        </div>
      ),
    },
  ];

  return (
    <AdminLayout title="Управление категориями галереи">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            Всего категорий: {categories.length}
          </p>
        </div>
        <Button
          variant="secondary"
          onClick={() => router.push('/admin/gallery')}
        >
          Вернуться к галерее
        </Button>
      </div>

      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      {categoryToDelete && (
        <div className="mb-6">
          <Alert type="warning" title="Подтверждение удаления" onClose={() => setCategoryToDelete(null)}>
            <p className="mb-4">
              Вы уверены, что хотите удалить категорию "{categoryToDelete.name}"? Это действие нельзя отменить.
            </p>
            {categoryToDelete.albumCount > 0 && (
              <p className="mb-4 text-red-600">
                Эта категория содержит {categoryToDelete.albumCount} альбомов. Сначала переместите или удалите эти альбомы.
              </p>
            )}
            <div className="flex justify-end space-x-4">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setCategoryToDelete(null)}
              >
                Отмена
              </Button>
              <Button
                variant="danger"
                size="sm"
                isLoading={isDeleting}
                onClick={handleDeleteConfirm}
                disabled={categoryToDelete.albumCount > 0}
              >
                Удалить
              </Button>
            </div>
          </Alert>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">
              {editingCategory ? 'Редактировать категорию' : 'Добавить категорию'}
            </h2>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <Input
                label="Название категории"
                {...register('name')}
                error={errors.name?.message}
                fullWidth
              />
              <div className="flex justify-end space-x-2">
                {editingCategory && (
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={handleCancelEdit}
                  >
                    Отмена
                  </Button>
                )}
                <Button type="submit" isLoading={isSubmitting}>
                  {editingCategory ? 'Сохранить' : 'Добавить'}
                </Button>
              </div>
            </form>
          </div>
        </div>

        <div className="md:col-span-2">
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <Table
              columns={columns}
              data={categories}
              keyExtractor={(category) => category.id}
              isLoading={isLoading}
              emptyMessage="Категории не найдены"
            />
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
