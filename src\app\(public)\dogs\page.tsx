import Image from 'next/image';
import Link from 'next/link';
import Layout from '@/components/layout/Layout';
import { prisma } from '@/lib/db';
import { PawPrint, Leaf, FloatingElements } from '@/components/ui/Decorations';
import DogFilters from './DogFilters';

export const metadata = {
  title: 'Наши собаки - Питомник собак',
  description: 'Познакомьтесь с нашими породистыми собаками. Все наши питомцы имеют отличную родословную и регулярно участвуют в выставках.',
};

export default async function DogsPage({
  searchParams,
}: {
  searchParams: { filter?: string };
}) {
  const { filter: searchFilter } = await searchParams;
  const filter = searchFilter || 'all';

  // Базовый запрос
  const baseQuery: any = {
    where: {
      isPublished: true,
    },
    include: {
      photos: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  };

  // Применяем фильтр
  let filteredQuery: any = { ...baseQuery };

  if (filter === 'male') {
    filteredQuery.where = {
      ...filteredQuery.where,
      gender: 'MALE',
    };
  } else if (filter === 'female') {
    filteredQuery.where = {
      ...filteredQuery.where,
      gender: 'FEMALE',
    };
  // Убрали фильтр "Доступны для вязки"
  } else if (filter === 'sale') {
    filteredQuery.where = {
      ...filteredQuery.where,
      isForSale: true,
    };
  }

  // Получаем отфильтрованный список собак
  const dogs = await prisma.dog.findMany(filteredQuery);

  // Получаем количество собак для каждого фильтра
  const allDogsCount = await prisma.dog.count({
    where: { isPublished: true },
  });

  const maleDogsCount = await prisma.dog.count({
    where: { isPublished: true, gender: 'MALE' },
  });

  const femaleDogsCount = await prisma.dog.count({
    where: { isPublished: true, gender: 'FEMALE' },
  });

  // Убрали подсчет количества собак для фильтра "Доступны для вязки"

  const saleDogsCount = await prisma.dog.count({
    where: { isPublished: true, isForSale: true },
  });

  const filterCounts = {
    all: allDogsCount,
    male: maleDogsCount,
    female: femaleDogsCount,
    sale: saleDogsCount,
  };

  // Получаем настройки сайта
  const settingsRecords = await prisma.settings.findMany();

  // Преобразуем записи в объект для удобства использования
  const settings = settingsRecords.reduce((acc, record) => {
    acc[record.key] = record.value;
    return acc;
  }, {} as Record<string, string>);

  return (
    <Layout>
      <section className="pt-16 pb-24 bg-forest-bg relative">
        <FloatingElements count={6} type="mixed" className="absolute inset-0 z-0 pointer-events-none" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex items-center justify-center mb-6">
            <PawPrint size="sm" className="mr-3 text-shiba-orange" />
            <span className="text-shiba-orange font-medium tracking-wider uppercase text-sm">Наши питомцы</span>
          </div>

          <h1 className="text-3xl md:text-4xl font-bold text-center text-forest-dark mb-6">Наши собаки</h1>

          <div className="max-w-3xl mx-auto text-center mb-12">
            <p className="text-lg text-forest-medium">
              Познакомьтесь с нашими породистыми собаками. Все наши питомцы имеют отличную родословную и регулярно участвуют в выставках.
            </p>
          </div>

          {/* Фильтры */}
          <DogFilters
            activeFilter={filter as any}
            counts={filterCounts}
          />

          {/* Карточки собак */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {dogs.length > 0 ? (
              dogs.map((dog: any) => {
                // Находим главное фото или используем первое доступное
                const mainPhoto = dog.photos.find((photo: any) => photo.isMain) || dog.photos[0];
                const age = new Date().getFullYear() - new Date(dog.birthDate).getFullYear();

                return (
                  <Link
                    key={dog.id}
                    href={`/dogs/${dog.slug}`}
                    className="group"
                  >
                    <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform group-hover:translate-y-[-5px]">
                      <div className="relative h-72">
                        {mainPhoto ? (
                          <>
                            <Image
                              src={mainPhoto.url}
                              alt={dog.name}
                              fill
                              style={{ objectFit: 'cover' }}
                              className="transition-transform duration-500 group-hover:scale-105"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div className="absolute bottom-0 left-0 right-0 p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <div className="flex gap-2">
                                <span className="bg-white/80 text-forest-dark text-xs font-medium px-2 py-1 rounded-full">
                                  {age} {age === 1 ? 'год' : age < 5 ? 'года' : 'лет'}
                                </span>
                                <span className="bg-white/80 text-forest-dark text-xs font-medium px-2 py-1 rounded-full">
                                  {dog.gender === 'MALE' ? 'Кобель' : 'Сука'}
                                </span>
                                {dog.isForSale && (
                                  <span className="bg-shiba-orange/80 text-white text-xs font-medium px-2 py-1 rounded-full">
                                    Продается
                                  </span>
                                )}
                              </div>
                            </div>
                          </>
                        ) : (
                          <div className="w-full h-full bg-forest-bg flex items-center justify-center">
                            <div className="text-center">
                              <svg className="w-12 h-12 mx-auto text-forest-light mb-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
                              </svg>
                              <span className="text-forest-medium">Нет фото</span>
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="p-6">
                        <div className="flex justify-between items-start mb-2">
                          <h2 className="text-xl font-bold text-forest-dark">{dog.name}</h2>
                          <div className="bg-forest-bg/20 text-forest-dark text-xs font-medium px-2 py-1 rounded-full">
                            {dog.breed}
                          </div>
                        </div>

                        <div className="flex items-center text-forest-medium text-sm mb-4">
                          <svg className="w-4 h-4 mr-1 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path>
                          </svg>
                          <span>Дата рождения: {new Date(dog.birthDate).toLocaleDateString('ru-RU')}</span>
                        </div>

                        {dog.achievements && (
                          <div className="mb-4 text-forest-medium text-sm line-clamp-2">
                            {dog.achievements}
                          </div>
                        )}

                        <div className="flex justify-between items-center">
                          <div className="flex gap-2">
                            {dog.weight && (
                              <div className="bg-forest-bg/20 text-forest-dark text-xs px-2 py-1 rounded-full">
                                {dog.weight} кг
                              </div>
                            )}
                            {dog.height && (
                              <div className="bg-forest-bg/20 text-forest-dark text-xs px-2 py-1 rounded-full">
                                {dog.height} см
                              </div>
                            )}
                          </div>

                          <span className="text-forest-dark font-medium text-sm group-hover:text-shiba-orange transition-colors flex items-center">
                            Подробнее
                            <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                );
              })
            ) : (
              <div className="col-span-full bg-white rounded-xl shadow-md p-12 text-center">
                <div className="w-20 h-20 mx-auto bg-forest-bg/20 rounded-full flex items-center justify-center mb-4">
                  <PawPrint size="md" className="text-forest-light" />
                </div>
                <h3 className="text-xl font-bold text-forest-dark mb-2">Собаки не найдены</h3>
                <p className="text-forest-medium">В данный момент нет опубликованных собак</p>
              </div>
            )}
          </div>

          {/* Контактная информация */}
          <div className="mt-16 bg-white rounded-xl shadow-lg p-8 relative overflow-hidden">
            <div className="absolute top-0 right-0 w-40 h-40 bg-shiba-orange/5 rounded-bl-full"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-forest-light/5 rounded-tr-full"></div>

            <div className="text-center relative z-10 max-w-2xl mx-auto">
              <Leaf size="sm" className="mx-auto text-shiba-orange mb-4" />
              <h2 className="text-2xl font-bold text-forest-dark mb-4">Хотите узнать больше о наших собаках?</h2>
              <p className="text-forest-medium mb-6">
                Если вы заинтересованы в приобретении щенка или у вас есть вопросы о наших собаках, пожалуйста, свяжитесь с нами. Мы с радостью ответим на все ваши вопросы.
              </p>

              <div className="flex flex-wrap justify-center gap-4">
                <a
                  href={`tel:${settings?.contactPhone || '+7XXXXXXXXXX'}`}
                  className="flex items-center bg-forest-bg/10 hover:bg-forest-bg/20 px-4 py-2 rounded-lg transition-colors"
                >
                  <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                  </svg>
                  <span className="text-forest-dark">{settings?.contactPhone || '+7 (XXX) XXX-XX-XX'}</span>
                </a>

                <a
                  href={`mailto:${settings?.contactEmail || '<EMAIL>'}`}
                  className="flex items-center bg-forest-bg/10 hover:bg-forest-bg/20 px-4 py-2 rounded-lg transition-colors"
                >
                  <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  <span className="text-forest-dark">{settings?.contactEmail || '<EMAIL>'}</span>
                </a>
              </div>
            </div>
          </div>
        </div>

        <div className="absolute left-0 bottom-0 w-32 h-32 opacity-10">
          <PawPrint size="lg" />
        </div>
        <div className="absolute right-10 top-40 w-16 h-16 opacity-10">
          <Leaf size="md" />
        </div>
      </section>
    </Layout>
  );
}
