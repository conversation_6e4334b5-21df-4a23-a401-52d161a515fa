'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Table from '@/components/ui/Table';
import Pagination from '@/components/ui/Pagination';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { formatDate, truncateText } from '@/lib/utils';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface NewsItem {
  id: string;
  title: string;
  content: string;
  slug: string;
  isPublished: boolean;
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  photos: Photo[];
}

export default function NewsPage() {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const router = useRouter();

  const fetchNews = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/news', {
        headers: {
          'x-admin-request': 'true'
        }
      });

      if (!response.ok) {
        throw new Error('Ошибка при загрузке данных');
      }

      const data = await response.json();
      setNews(data.news || []);
      setTotalPages(Math.ceil((data.news?.length || 0) / 10)); // Предполагаем 10 элементов на странице
    } catch (err) {
      setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при загрузке новостей:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNews();
  }, []);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRowClick = (newsItem: NewsItem) => {
    router.push(`/admin/news/${newsItem.slug}`);
  };

  const columns = [
    {
      header: 'Изображение',
      accessor: (newsItem: NewsItem) => {
        const mainPhoto = newsItem.photos?.find(photo => photo.isMain);
        return mainPhoto ? (
          <img
            src={mainPhoto.url}
            alt={newsItem.title}
            className="h-12 w-16 object-cover rounded"
          />
        ) : (
          <div className="h-12 w-16 bg-gray-200 rounded flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        );
      },
      className: 'w-20',
    },
    {
      header: 'Заголовок',
      accessor: (newsItem: NewsItem) => truncateText(newsItem.title, 50),
    },
    {
      header: 'Дата публикации',
      accessor: (newsItem: NewsItem) =>
        newsItem.publishedAt ? formatDate(newsItem.publishedAt) : 'Не опубликовано',
    },
    {
      header: 'Статус',
      accessor: (newsItem: NewsItem) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            newsItem.isPublished
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {newsItem.isPublished ? 'Опубликовано' : 'Черновик'}
        </span>
      ),
    },
    {
      header: 'Дата создания',
      accessor: (newsItem: NewsItem) => formatDate(newsItem.createdAt),
    },
    {
      header: 'Действия',
      accessor: (newsItem: NewsItem) => (
        <div className="flex space-x-2">
          <Link href={`/admin/news/${newsItem.slug}/edit`}>
            <Button variant="secondary" size="sm">
              Редактировать
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  // Пагинация на клиентской стороне
  const paginatedNews = news.slice((currentPage - 1) * 10, currentPage * 10);

  return (
    <AdminLayout title="Управление новостями">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            Всего новостей: {news.length}
          </p>
        </div>
        <Link href="/admin/news/create">
          <Button>Добавить новость</Button>
        </Link>
      </div>

      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      <Table
        columns={columns}
        data={paginatedNews}
        keyExtractor={(newsItem) => newsItem.id}
        onRowClick={handleRowClick}
        isLoading={isLoading}
        emptyMessage="Новости не найдены"
      />

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />
    </AdminLayout>
  );
}
