'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import FileUpload from '@/components/ui/FileUpload';
import Alert from '@/components/ui/Alert';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { slugify } from '@/lib/utils';

const newsSchema = z.object({
  title: z.string().min(1, 'Заголовок обязателен'),
  content: z.string().min(1, 'Содержание обязательно'),
  isPublished: z.boolean().default(false),
  publishNow: z.boolean().default(false),
});

type NewsFormData = z.infer<typeof newsSchema>;

export default function CreateNewsPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [additionalPhotos, setAdditionalPhotos] = useState<File[]>([]);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<NewsFormData>({
    resolver: zodResolver(newsSchema),
    defaultValues: {
      isPublished: false,
      publishNow: false,
    },
  });

  const onSubmit = async (data: NewsFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Создаем slug из заголовка
      const slug = slugify(data.title);

      // Создаем объект с данными новости
      const newsData = {
        ...data,
        slug,
        publishedAt: data.publishNow ? new Date().toISOString() : null,
      };

      // Отправляем запрос на создание новости
      const response = await fetch('/api/news', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newsData),
      });

      if (!response.ok) {
        throw new Error('Ошибка при создании новости');
      }

      const createdNews = await response.json();

      // Если есть обложка, загружаем ее
      if (coverImage) {
        const formData = new FormData();
        formData.append('file', coverImage);
        formData.append('newsId', createdNews.id);
        formData.append('isMain', 'true');
        formData.append('order', '0');

        const imageResponse = await fetch('/api/news/photos', {
          method: 'POST',
          body: formData,
        });

        if (!imageResponse.ok) {
          console.error('Ошибка при загрузке обложки:', await imageResponse.text());
        }
      }

      // Если есть дополнительные фотографии, загружаем их
      if (additionalPhotos.length > 0) {
        for (let i = 0; i < additionalPhotos.length; i++) {
          const formData = new FormData();
          formData.append('file', additionalPhotos[i]);
          formData.append('newsId', createdNews.id);
          formData.append('isMain', 'false');
          formData.append('order', (i + 1).toString());

          const photoResponse = await fetch('/api/news/photos', {
            method: 'POST',
            body: formData,
          });

          if (!photoResponse.ok) {
            console.error(`Ошибка при загрузке фотографии ${i + 1}:`, await photoResponse.text());
          }
        }
      }

      setSuccess('Новость успешно создана');

      // Перенаправляем на страницу со списком новостей
      setTimeout(() => {
        router.push('/admin/news');
      }, 2000);
    } catch (err) {
      console.error('Ошибка при создании новости:', err);
      setError('Произошла ошибка при создании новости. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCoverImageChange = (files: File[]) => {
    if (files.length > 0) {
      setCoverImage(files[0]);
    } else {
      setCoverImage(null);
    }
  };

  const handleAdditionalPhotosChange = (files: File[]) => {
    setAdditionalPhotos(files);
  };

  return (
    <AdminLayout title="Добавление новой новости">
      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Основная информация</h2>
          <div className="space-y-6">
            <Input
              label="Заголовок"
              {...register('title')}
              error={errors.title?.message}
              fullWidth
            />
            <Textarea
              label="Содержание"
              {...register('content')}
              error={errors.content?.message}
              rows={10}
              fullWidth
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Изображения</h2>
          <div className="space-y-6">
            <FileUpload
              label="Загрузите изображение для обложки"
              accept="image/*"
              onChange={handleCoverImageChange}
              fullWidth
            />
            {coverImage && (
              <p className="text-sm text-green-600">
                Обложка выбрана: {coverImage.name}
              </p>
            )}

            <div className="mt-8">
              <h3 className="text-md font-medium mb-2">Дополнительные фотографии</h3>
              <FileUpload
                label="Загрузите дополнительные фотографии"
                accept="image/*"
                multiple
                onChange={handleAdditionalPhotosChange}
                fullWidth
              />
              {additionalPhotos.length > 0 && (
                <p className="text-sm text-green-600 mt-2">
                  Выбрано дополнительных фотографий: {additionalPhotos.length}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Публикация</h2>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPublished"
                {...register('isPublished')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
                Опубликовать на сайте
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="publishNow"
                {...register('publishNow')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="publishNow" className="ml-2 block text-sm text-gray-900">
                Опубликовать сейчас
              </label>
            </div>
            <p className="text-sm text-gray-500">
              Если опция "Опубликовать сейчас" не выбрана, новость будет сохранена без даты публикации.
              Вы сможете установить дату публикации позже.
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => router.push('/admin/news')}
          >
            Отмена
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            Создать
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
