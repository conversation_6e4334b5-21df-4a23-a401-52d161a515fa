const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);
const prisma = new PrismaClient();

async function createFullBackup() {
  try {
    console.log('🚀 Создание полного backup через Prisma...');

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const backupDir = path.join(process.cwd(), 'backups');
    
    // Создание папки для бэкапов
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    console.log('📋 1. Создание схемы базы данных...');
    
    // Извлечение схемы из базы данных
    try {
      await execAsync('npx prisma db pull');
      console.log('✅ Схема обновлена');
    } catch (error) {
      console.log('⚠️ Ошибка при обновлении схемы:', error.message);
    }

    // Копирование файла схемы
    const schemaSource = path.join(process.cwd(), 'prisma', 'schema.prisma');
    const schemaBackup = path.join(backupDir, `schema-${timestamp}.prisma`);
    
    if (fs.existsSync(schemaSource)) {
      fs.copyFileSync(schemaSource, schemaBackup);
      console.log(`✅ Схема сохранена: ${schemaBackup}`);
    }

    console.log('📊 2. Экспорт данных...');
    
    // Экспорт всех данных
    const data = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      schema: fs.existsSync(schemaSource) ? fs.readFileSync(schemaSource, 'utf8') : null,
      data: {}
    };

    // Получение всех данных
    const tables = [
      { name: 'users', model: 'user', include: null },
      { name: 'dogs', model: 'dog', include: { photos: true } },
      { name: 'puppies', model: 'puppy', include: { photos: true, breeding: true } },
      { name: 'news', model: 'news', include: { author: true } },
      { name: 'heroSlides', model: 'heroSlide', include: null },
      { name: 'settings', model: 'settings', include: null }
    ];

    for (const table of tables) {
      try {
        console.log(`📤 Экспорт ${table.name}...`);
        
        const query = table.include 
          ? { include: table.include }
          : {};
          
        data.data[table.name] = await prisma[table.model].findMany(query);
        console.log(`✅ ${table.name}: ${data.data[table.name].length} записей`);
      } catch (error) {
        console.log(`⚠️ Ошибка при экспорте ${table.name}:`, error.message);
        data.data[table.name] = [];
      }
    }

    // Дополнительные таблицы (если существуют)
    const optionalTables = [
      { name: 'breedings', model: 'breeding', include: { father: true, mother: true, puppies: true } },
      { name: 'gallery', model: 'gallery', include: { photos: true } },
      { name: 'inquiries', model: 'inquiry', include: null },
      { name: 'photos', model: 'photo', include: null }
    ];

    for (const table of optionalTables) {
      try {
        console.log(`📤 Экспорт ${table.name}...`);
        
        const query = table.include 
          ? { include: table.include }
          : {};
          
        data.data[table.name] = await prisma[table.model].findMany(query);
        console.log(`✅ ${table.name}: ${data.data[table.name].length} записей`);
      } catch (error) {
        console.log(`⚠️ Таблица ${table.name} не найдена, пропускаем...`);
        data.data[table.name] = [];
      }
    }

    console.log('💾 3. Сохранение данных...');
    
    // Сохранение JSON файла
    const jsonFile = path.join(backupDir, `full-backup-${timestamp}.json`);
    fs.writeFileSync(jsonFile, JSON.stringify(data, null, 2));
    
    // Создание сжатой версии
    const zlib = require('zlib');
    const gzipFile = jsonFile.replace('.json', '.json.gz');
    const compressed = zlib.gzipSync(fs.readFileSync(jsonFile));
    fs.writeFileSync(gzipFile, compressed);
    
    // Удаление несжатого файла для экономии места
    fs.unlinkSync(jsonFile);

    console.log('📁 4. Backup файлов...');
    
    // Backup загруженных файлов
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    if (fs.existsSync(uploadsDir)) {
      const tar = require('tar');
      const uploadsBackup = path.join(backupDir, `uploads-${timestamp}.tar.gz`);
      
      try {
        await tar.create(
          {
            gzip: true,
            file: uploadsBackup,
            cwd: path.join(process.cwd(), 'public')
          },
          ['uploads']
        );
        console.log(`✅ Файлы сохранены: ${uploadsBackup}`);
      } catch (error) {
        console.log('⚠️ Ошибка при архивации файлов:', error.message);
      }
    } else {
      console.log('⚠️ Папка uploads не найдена');
    }

    console.log('📋 5. Создание SQL дампа...');
    
    // Создание SQL дампа через pg_dump (если доступен)
    try {
      const databaseUrl = process.env.DATABASE_URL;
      if (databaseUrl) {
        const url = new URL(databaseUrl);
        const sqlFile = path.join(backupDir, `sql-dump-${timestamp}.sql`);
        
        const pgDumpCommand = `pg_dump "${databaseUrl}" > "${sqlFile}"`;
        await execAsync(pgDumpCommand);
        
        // Сжатие SQL файла
        const sqlGzipFile = sqlFile + '.gz';
        const sqlCompressed = zlib.gzipSync(fs.readFileSync(sqlFile));
        fs.writeFileSync(sqlGzipFile, sqlCompressed);
        fs.unlinkSync(sqlFile);
        
        console.log(`✅ SQL дамп создан: ${sqlGzipFile}`);
      }
    } catch (error) {
      console.log('⚠️ Не удалось создать SQL дамп:', error.message);
    }

    console.log('📊 6. Создание отчета...');
    
    // Создание отчета о backup
    const reportFile = path.join(backupDir, `backup-report-${timestamp}.txt`);
    const report = `
ОТЧЕТ О РЕЗЕРВНОМ КОПИРОВАНИИ
=============================

Дата создания: ${new Date().toLocaleString('ru-RU')}
Версия: ${data.version}

СТАТИСТИКА ДАННЫХ:
------------------
👥 Пользователи: ${data.data.users?.length || 0}
🐕 Собаки: ${data.data.dogs?.length || 0}
🐶 Щенки: ${data.data.puppies?.length || 0}
📰 Новости: ${data.data.news?.length || 0}
🎨 Hero-слайды: ${data.data.heroSlides?.length || 0}
⚙️ Настройки: ${data.data.settings?.length || 0}
💕 Вязки: ${data.data.breedings?.length || 0}
📸 Галерея: ${data.data.gallery?.length || 0}
📧 Заявки: ${data.data.inquiries?.length || 0}
🖼️ Фотографии: ${data.data.photos?.length || 0}

СОЗДАННЫЕ ФАЙЛЫ:
----------------
📄 Данные: full-backup-${timestamp}.json.gz
📋 Схема: schema-${timestamp}.prisma
📁 Файлы: uploads-${timestamp}.tar.gz
🗄️ SQL: sql-dump-${timestamp}.sql.gz

ИНСТРУКЦИИ ПО ВОССТАНОВЛЕНИЮ:
-----------------------------
1. Восстановление данных:
   node scripts/import-data.js backups/full-backup-${timestamp}.json.gz

2. Восстановление схемы:
   cp backups/schema-${timestamp}.prisma prisma/schema.prisma
   npx prisma migrate dev

3. Восстановление файлов:
   cd public && tar -xzf ../backups/uploads-${timestamp}.tar.gz

4. Восстановление из SQL:
   gunzip -c backups/sql-dump-${timestamp}.sql.gz | psql DATABASE_URL
`;

    fs.writeFileSync(reportFile, report);

    // Показать размеры файлов
    console.log('\n📊 РЕЗУЛЬТАТЫ BACKUP:');
    console.log('====================');
    
    const files = fs.readdirSync(backupDir).filter(f => f.includes(timestamp));
    files.forEach(file => {
      const filePath = path.join(backupDir, file);
      const stats = fs.statSync(filePath);
      const size = (stats.size / 1024 / 1024).toFixed(2);
      console.log(`📁 ${file}: ${size} MB`);
    });

    console.log('\n🎉 Полный backup создан успешно!');
    console.log(`📂 Файлы сохранены в: ${backupDir}`);
    console.log(`📋 Отчет: ${reportFile}`);

  } catch (error) {
    console.error('❌ Ошибка при создании backup:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

createFullBackup();
