import Image from "next/image";
import Link from "next/link";
import Layout from "@/components/layout/Layout";
import { prisma } from "@/lib/db";
import { formatDate } from "@/lib/utils";
import { PawPrint, Leaf, FloatingElements, SectionDivider } from "@/components/ui/Decorations";
import HeroSlider from "@/components/ui/HeroSlider";

// Делаем страницу серверным компонентом
export default async function Home() {
  // Загружаем данные для главной страницы
  const featuredDogs = await prisma.dog.findMany({
    where: {
      isPublished: true,
    },
    include: {
      photos: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
    take: 3,
  });

  const latestNews = await prisma.news.findMany({
    where: {
      isPublished: true,
    },
    include: {
      photos: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
    take: 3,
  });

  const availablePuppies = await prisma.puppy.count({
    where: {
      isPublished: true,
      status: 'AVAILABLE',
    },
  });

  // Получаем все настройки сайта
  const settingsRecords = await prisma.settings.findMany();

  // Преобразуем записи в объект для удобства использования
  const settings = settingsRecords.reduce((acc, record) => {
    acc[record.key] = record.value;
    return acc;
  }, {} as Record<string, string>);

  // Получаем hero-слайды
  const heroSlides = await prisma.heroSlide.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: 'asc',
    },
  });

  // Выводим настройки в консоль для отладки
  console.log('Настройки сайта:', settings);

  return (
    <Layout>
      {/* Hero слайдер */}
      <HeroSlider slides={heroSlides} />

      {/* О питомнике */}
      <section className="py-20 bg-forest-bg relative">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 relative z-10">
            <div className="inline-flex items-center justify-center mb-3">
              <div className="h-px w-10 bg-shiba-orange"></div>
              <Leaf size="sm" className="mx-3" color="var(--shiba-orange)" />
              <div className="h-px w-10 bg-shiba-orange"></div>
            </div>
            <h2 className="text-4xl font-bold mb-4 text-forest-dark">О нашем питомнике</h2>
            <p className="text-forest-medium max-w-2xl mx-auto">Более 10 лет опыта разведения собак породы сиба-ину</p>
          </div>

          <div className="grid md:grid-cols-2 gap-16 items-center relative z-10">
            <div className="relative">
              <div className="absolute -top-4 -left-4 w-24 h-24 bg-shiba-orange/10 rounded-lg"></div>
              <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-forest-light/10 rounded-lg"></div>
              <div className="relative h-[500px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src={settings.aboutImage || "/images/about-dogs.jpg"}
                  alt="О питомнике"
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg">
                <div className="flex items-center">
                  <PawPrint size="sm" className="mr-2" />
                  <span className="font-bold text-forest-dark">С 2010 года</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-2xl font-semibold mb-6 text-forest-dark">Наша история и ценности</h3>
              <div className="space-y-6">
                <div className="flex">
                  <div className="flex-shrink-0 mt-1">
                    <div className="w-8 h-8 rounded-full bg-shiba-orange/20 flex items-center justify-center">
                      <PawPrint size="sm" color="var(--shiba-orange)" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-lg font-medium text-forest-dark mb-2">Профессиональное разведение</h4>
                    <p className="text-forest-medium">
                      Наш питомник основан в 2010 году и специализируется на разведении
                      породистых собак. Мы гордимся нашими питомцами, которые регулярно
                      участвуют в выставках и занимают призовые места.
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0 mt-1">
                    <div className="w-8 h-8 rounded-full bg-shiba-orange/20 flex items-center justify-center">
                      <PawPrint size="sm" color="var(--shiba-orange)" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-lg font-medium text-forest-dark mb-2">Здоровье и благополучие</h4>
                    <p className="text-forest-medium">
                      Все наши собаки проходят регулярные ветеринарные осмотры, имеют
                      необходимые прививки и документы, подтверждающие их родословную.
                      Здоровье наших питомцев — наш главный приоритет.
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0 mt-1">
                    <div className="w-8 h-8 rounded-full bg-shiba-orange/20 flex items-center justify-center">
                      <PawPrint size="sm" color="var(--shiba-orange)" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-lg font-medium text-forest-dark mb-2">Индивидуальный подход</h4>
                    <p className="text-forest-medium">
                      Мы с радостью поможем вам выбрать идеального щенка, который станет
                      верным другом и членом вашей семьи на долгие годы. Наша команда всегда
                      готова ответить на все ваши вопросы.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-10">
                <Link
                  href="/contact"
                  className="btn-forest px-6 py-3 rounded-lg font-medium inline-flex items-center"
                >
                  Связаться с нами
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>

        <FloatingElements count={8} type="leaf" className="absolute inset-0 z-0 pointer-events-none" />
      </section>

      {/* Избранные собаки */}
      <section className="py-20 bg-white relative">
        <SectionDivider type="curve" position="top" color="var(--forest-bg)" className="z-10" />

        <div className="container mx-auto px-4 relative z-20">
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center mb-3">
              <div className="h-px w-10 bg-shiba-orange"></div>
              <PawPrint size="sm" className="mx-3" color="var(--shiba-orange)" />
              <div className="h-px w-10 bg-shiba-orange"></div>
            </div>
            <h2 className="text-4xl font-bold mb-4 text-forest-dark">Наши выпускники</h2>
            <p className="text-forest-medium max-w-2xl mx-auto">Познакомьтесь с нашими чемпионами и производителями</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredDogs.length > 0 ? (
              featuredDogs.map((dog) => {
                // Находим главное фото или используем первое доступное
                const mainPhoto = dog.photos.find(photo => photo.isMain) || dog.photos[0];
                const age = new Date().getFullYear() - new Date(dog.birthDate).getFullYear();

                return (
                  <div key={dog.id} className="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl group">
                    <div className="relative h-72">
                      {mainPhoto ? (
                        <>
                          <Image
                            src={mainPhoto.url}
                            alt={dog.name}
                            fill
                            style={{ objectFit: 'cover' }}
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </>
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-400">Нет фото</span>
                        </div>
                      )}
                      <div className="absolute top-4 right-4">
                        <div className="bg-shiba-orange text-white text-xs font-bold px-3 py-1 rounded-full">
                          {dog.gender === 'MALE' ? 'Кобель' : 'Сука'}
                        </div>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-xl font-bold text-forest-dark group-hover:text-shiba-orange transition-colors duration-300">{dog.name}</h3>
                        <span className="text-forest-light text-sm font-medium">{age} {age === 1 ? 'год' : age < 5 ? 'года' : 'лет'}</span>
                      </div>
                      <p className="text-forest-medium mb-4">
                        {dog.breed}
                      </p>
                      {dog.achievements && (
                        <div className="mb-4 flex items-start">
                          <svg className="w-5 h-5 text-shiba-orange mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                          </svg>
                          <p className="text-forest-medium text-sm">{dog.achievements}</p>
                        </div>
                      )}
                      <Link
                        href={`/dogs/${dog.slug}`}
                        className="inline-flex items-center text-shiba-orange hover:text-shiba-orange-dark font-medium transition-colors"
                      >
                        <span>Подробнее</span>
                        <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                      </Link>
                    </div>
                  </div>
                );
              })
            ) : (
              // Если собак нет, показываем заглушки
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden">
                  <div className="relative h-72 bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400">Нет фото</span>
                  </div>
                  <div className="p-6">
                    <div className="h-6 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  </div>
                </div>
              ))
            )}
          </div>

          <div className="text-center mt-16">
            <Link
              href="/dogs"
              className="btn-forest px-8 py-4 rounded-lg font-semibold inline-flex items-center shadow-md hover:shadow-lg transition-all"
            >
              <span>Смотреть всех собак</span>
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </Link>
          </div>
        </div>

        <div className="absolute right-0 bottom-0 w-32 h-32 opacity-10">
          <PawPrint size="lg" />
        </div>
        <div className="absolute left-10 top-40 w-16 h-16 opacity-10">
          <PawPrint size="md" />
        </div>
      </section>

      {/* Последние новости */}
      <section className="py-20 bg-forest-bg relative">
        <SectionDivider type="curve" position="top" color="white" className="z-10" />

        <div className="container mx-auto px-4 relative z-20">
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center mb-3">
              <div className="h-px w-10 bg-shiba-orange"></div>
              <Leaf size="sm" className="mx-3" color="var(--shiba-orange)" />
              <div className="h-px w-10 bg-shiba-orange"></div>
            </div>
            <h2 className="text-4xl font-bold mb-4 text-forest-dark">Последние новости</h2>
            <p className="text-forest-medium max-w-2xl mx-auto">Будьте в курсе последних событий нашего питомника</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {latestNews.length > 0 ? (
              latestNews.map((news) => (
                <div key={news.id} className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300">
                  <div className="relative h-56">
                    {news.photos && news.photos.length > 0 ? (
                      <>
                        <Image
                          src={news.photos[0].url}
                          alt={news.title}
                          fill
                          style={{ objectFit: 'cover' }}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </>
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-400">Нет изображения</span>
                      </div>
                    )}
                    <div className="absolute top-4 left-4">
                      <div className="bg-white text-forest-dark text-xs font-bold px-3 py-1 rounded-full shadow-md">
                        {formatDate(news.createdAt)}
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-3 text-forest-dark group-hover:text-shiba-orange transition-colors duration-300">{news.title}</h3>
                    <p className="text-forest-medium mb-4 line-clamp-3">
                      {news.content && news.content.length > 150
                        ? `${news.content.substring(0, 150)}...`
                        : news.content}
                    </p>
                    <Link
                      href={`/news/${news.slug}`}
                      className="inline-flex items-center text-shiba-orange hover:text-shiba-orange-dark font-medium transition-colors"
                    >
                      <span>Читать далее</span>
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </Link>
                  </div>
                </div>
              ))
            ) : (
              // Если новостей нет, показываем заглушки
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden">
                  <div className="relative h-56 bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400">Нет изображения</span>
                  </div>
                  <div className="p-6">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  </div>
                </div>
              ))
            )}
          </div>

          <div className="text-center mt-16">
            <Link
              href="/news"
              className="btn-outline-forest px-8 py-4 rounded-lg font-semibold inline-flex items-center shadow-md hover:shadow-lg transition-all"
            >
              <span>Все новости</span>
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </Link>
          </div>
        </div>

        <FloatingElements count={6} type="mixed" className="absolute inset-0 z-10 pointer-events-none" />
      </section>

      {/* Контакты */}
      <section className="py-20 bg-white relative">
        <SectionDivider type="wave" position="top" color="var(--forest-bg)" className="z-10" />

        <div className="container mx-auto px-4 relative z-20">
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center mb-3">
              <div className="h-px w-10 bg-shiba-orange"></div>
              <PawPrint size="sm" className="mx-3" color="var(--shiba-orange)" />
              <div className="h-px w-10 bg-shiba-orange"></div>
            </div>
            <h2 className="text-4xl font-bold mb-4 text-forest-dark">Свяжитесь с нами</h2>
            <p className="text-forest-medium max-w-2xl mx-auto">Мы всегда рады ответить на ваши вопросы и помочь выбрать идеального питомца</p>
          </div>

          <div className="grid md:grid-cols-2 gap-16">
            <div className="bg-forest-bg rounded-xl p-8 shadow-lg relative">
              <div className="absolute -top-6 -right-6 bg-shiba-orange/10 w-24 h-24 rounded-full"></div>

              <h3 className="text-2xl font-bold mb-6 text-forest-dark flex items-center">
                <PawPrint size="sm" className="mr-3" color="var(--shiba-orange)" />
                <span>Контактная информация</span>
              </h3>

              <ul className="space-y-6 relative z-10">
                <li className="flex items-center p-4 bg-white rounded-lg shadow-sm transition-all duration-300 hover:shadow-md">
                  <div className="w-12 h-12 rounded-full bg-shiba-orange/20 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="w-6 h-6 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-forest-light font-medium mb-1">Телефон</p>
                    <p className="text-forest-dark font-medium">{settings?.contactPhone || '+7 (XXX) XXX-XX-XX'}</p>
                  </div>
                </li>

                <li className="flex items-center p-4 bg-white rounded-lg shadow-sm transition-all duration-300 hover:shadow-md">
                  <div className="w-12 h-12 rounded-full bg-shiba-orange/20 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="w-6 h-6 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-forest-light font-medium mb-1">Email</p>
                    <p className="text-forest-dark font-medium">{settings?.contactEmail || '<EMAIL>'}</p>
                  </div>
                </li>

                <li className="flex items-center p-4 bg-white rounded-lg shadow-sm transition-all duration-300 hover:shadow-md">
                  <div className="w-12 h-12 rounded-full bg-shiba-orange/20 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="w-6 h-6 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-forest-light font-medium mb-1">Адрес</p>
                    <p className="text-forest-dark font-medium">{settings?.address || 'г. Москва, ул. Примерная, д. 123'}</p>
                  </div>
                </li>
              </ul>

              <h3 className="text-xl font-bold mt-10 mb-6 text-forest-dark">Мы в социальных сетях</h3>
              <div className="flex space-x-4">
                {settings?.instagramUrl && (
                  <a href={settings.instagramUrl} target="_blank" rel="noopener noreferrer" className="w-12 h-12 rounded-full bg-white shadow-sm flex items-center justify-center text-forest-medium hover:text-shiba-orange transition-colors">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                    </svg>
                  </a>
                )}
                {settings?.facebookUrl && (
                  <a href={settings.facebookUrl} target="_blank" rel="noopener noreferrer" className="w-12 h-12 rounded-full bg-white shadow-sm flex items-center justify-center text-forest-medium hover:text-shiba-orange transition-colors">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path d="M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z"/>
                    </svg>
                  </a>
                )}
                {settings?.vkUrl && (
                  <a href={settings.vkUrl} target="_blank" rel="noopener noreferrer" className="w-12 h-12 rounded-full bg-white shadow-sm flex items-center justify-center text-forest-medium hover:text-shiba-orange transition-colors">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21.547 7h-3.29a.743.743 0 0 0-.655.392s-1.312 2.416-1.734 3.23c-1.43 2.78-2.006 2.913-2.238 2.745-.546-.401-.41-1.618-.41-2.48V7.708c0-.655-.187-.915-.74-.915H9.161a.652.652 0 0 0-.672.601c0 .628.937.773 1.03 2.543v3.845c0 .84-.151.995-.486.995-.892 0-3.057-3.21-4.34-6.885-.252-.673-.507-.903-1.172-.903H.9c-.75 0-.9.345-.9.731 0 .682.892 4.073 4.148 8.553C6.318 20.25 9.374 22 12.153 22c1.671 0 1.875-.368 1.875-1.003v-2.313c0-.737.158-.884.687-.884.39 0 1.057.192 2.615 1.667 1.78 1.75 2.073 2.533 3.075 2.533h3.29c.75 0 1.126-.368.91-1.096-.238-.724-1.088-1.775-2.215-3.022-.612-.71-1.53-1.475-1.809-1.858-.389-.491-.278-.71 0-1.147 0 0 2.948-4.070 3.254-5.456.153-.684 0-1.18-.7-1.18z"/>
                    </svg>
                  </a>
                )}
                {settings?.youtubeUrl && (
                  <a href={settings.youtubeUrl} target="_blank" rel="noopener noreferrer" className="w-12 h-12 rounded-full bg-white shadow-sm flex items-center justify-center text-forest-medium hover:text-shiba-orange transition-colors">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                    </svg>
                  </a>
                )}
                {!settings?.instagramUrl && !settings?.facebookUrl && !settings?.vkUrl && !settings?.youtubeUrl && (
                  <p className="text-forest-medium">Социальные сети не настроены</p>
                )}
              </div>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-lg relative">
              <div className="absolute -top-6 -left-6 bg-forest-light/10 w-24 h-24 rounded-full"></div>

              <h3 className="text-2xl font-bold mb-6 text-forest-dark flex items-center">
                <Leaf size="sm" className="mr-3" color="var(--forest-medium)" />
                <span>Отправьте нам сообщение</span>
              </h3>

              <form action="/api/inquiries" method="POST" className="space-y-6 relative z-10">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-forest-dark font-medium mb-2">Ваше имя</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="w-5 h-5 text-forest-light" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                      </div>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        className="w-full pl-10 pr-4 py-3 border border-forest-pale rounded-lg focus:outline-none focus:ring-2 focus:ring-shiba-orange focus:border-transparent bg-forest-bg/30"
                        placeholder="Введите ваше имя"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-forest-dark font-medium mb-2">Телефон</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="w-5 h-5 text-forest-light" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                      </div>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        required
                        className="w-full pl-10 pr-4 py-3 border border-forest-pale rounded-lg focus:outline-none focus:ring-2 focus:ring-shiba-orange focus:border-transparent bg-forest-bg/30"
                        placeholder="+7 (___) ___-__-__"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-forest-dark font-medium mb-2">Email</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="w-5 h-5 text-forest-light" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      className="w-full pl-10 pr-4 py-3 border border-forest-pale rounded-lg focus:outline-none focus:ring-2 focus:ring-shiba-orange focus:border-transparent bg-forest-bg/30"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="message" className="block text-forest-dark font-medium mb-2">Сообщение</label>
                  <div className="relative">
                    <div className="absolute top-3 left-3 flex items-start pointer-events-none">
                      <svg className="w-5 h-5 text-forest-light" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                      </svg>
                    </div>
                    <textarea
                      id="message"
                      name="message"
                      rows={4}
                      className="w-full pl-10 pr-4 py-3 border border-forest-pale rounded-lg focus:outline-none focus:ring-2 focus:ring-shiba-orange focus:border-transparent bg-forest-bg/30"
                      placeholder="Введите ваше сообщение"
                    ></textarea>
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="submit"
                    className="btn-shiba px-8 py-4 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all flex items-center"
                  >
                    <span>Отправить сообщение</span>
                    <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div className="absolute right-10 bottom-10 w-32 h-32 opacity-10">
          <PawPrint size="lg" />
        </div>
        <div className="absolute left-10 top-40 w-16 h-16 opacity-10">
          <Leaf size="md" />
        </div>

        <FloatingElements count={6} type="mixed" className="absolute inset-0 z-10 pointer-events-none" />
      </section>
    </Layout>
  );
}
