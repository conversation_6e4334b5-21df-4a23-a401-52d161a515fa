import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import fs from 'fs';
import path from 'path';

// PUT /api/news/photos/[id] - Обновление информации о фотографии (защищенный маршрут)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const id = params.id;
    const data = await request.json();

    // Проверяем, существует ли фотография
    const photo = await prisma.photo.findUnique({
      where: { id },
      include: {
        news: true,
      },
    });

    if (!photo) {
      return NextResponse.json(
        { error: 'Фотография не найдена' },
        { status: 404 }
      );
    }

    // Если устанавливаем фото как главное, сбрасываем флаг isMain у других фотографий новости
    if (data.isMain) {
      await prisma.photo.updateMany({
        where: {
          newsId: photo.newsId,
          isMain: true,
        },
        data: {
          isMain: false,
        },
      });
    }

    // Обновляем информацию о фотографии
    const updatedPhoto = await prisma.photo.update({
      where: { id },
      data: {
        title: data.title,
        description: data.description,
        isMain: data.isMain,
        order: data.order,
      },
    });

    return NextResponse.json(updatedPhoto);
  } catch (error) {
    console.error('Ошибка при обновлении информации о фотографии:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении информации о фотографии' },
      { status: 500 }
    );
  }
}

// DELETE /api/news/photos/[id] - Удаление фотографии (защищенный маршрут)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Проверяем, существует ли фотография
    const photo = await prisma.photo.findUnique({
      where: { id }
    });

    if (!photo) {
      return NextResponse.json(
        { error: 'Фотография не найдена' },
        { status: 404 }
      );
    }

    // Удаляем файл с сервера
    try {
      const filePath = path.join(process.cwd(), 'public', photo.url);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (err) {
      console.error('Ошибка при удалении файла:', err);
      // Продолжаем выполнение даже если не удалось удалить файл
    }

    // Удаляем запись о фотографии из базы данных
    await prisma.photo.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении фотографии:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении фотографии' },
      { status: 500 }
    );
  }
}
