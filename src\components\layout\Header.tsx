'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import { PawPrint } from '@/components/ui/Decorations';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [settings, setSettings] = useState<Record<string, string>>({});
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    // Загружаем настройки сайта
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/settings');
        if (response.ok) {
          const data = await response.json();
          setSettings(data);
        }
      } catch (error) {
        console.error('Ошибка при загрузке настроек:', error);
      }
    };

    fetchSettings();
  }, []);

  // Отслеживаем скролл для изменения стиля хедера
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Закрываем мобильное меню при изменении маршрута
  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  const toggleMenu = useCallback(() => {
    setIsMenuOpen(!isMenuOpen);
  }, [isMenuOpen]);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white shadow-md py-2'
          : 'bg-white/90 backdrop-blur-sm py-4'
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              {settings.logo ? (
                <Image
                  src={settings.logo}
                  alt={settings.siteName || 'Питомник Собак'}
                  width={isScrolled ? 40 : 50}
                  height={isScrolled ? 40 : 50}
                  className="rounded-lg transition-all duration-300 group-hover:scale-105"
                />
              ) : (
                <div className="bg-forest-bg/20 rounded-lg p-2 transition-all duration-300 group-hover:scale-105">
                  <PawPrint size="sm" className="text-shiba-orange" />
                </div>
              )}
            </div>
            <div className="flex flex-col">
              <span className={`font-bold text-forest-dark transition-all duration-300 ${
                isScrolled ? 'text-xl' : 'text-2xl'
              }`}>
                {settings.siteName || ''}
              </span>
              <span className="text-xs text-forest-medium hidden sm:block">Племенной питомник породы Сиба-Ину</span>
            </div>
          </Link>

          {/* Мобильное меню */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="text-forest-dark hover:text-shiba-orange focus:outline-none transition-colors p-2"
              aria-label={isMenuOpen ? 'Закрыть меню' : 'Открыть меню'}
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>

          {/* Десктопное меню */}
          <nav className="hidden md:flex items-center space-x-1 lg:space-x-2">
            {[
              { href: '/dogs', label: 'Собаки' },
              { href: '/puppies', label: 'Щенки' },
              { href: '/graduates', label: 'Выпускники' },
              { href: '/gallery', label: 'Галерея' },
              { href: '/news', label: 'Новости' },
              { href: '/contacts', label: 'Контакты' }
            ].map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`px-3 py-2 rounded-lg transition-all relative group ${
                  pathname === item.href
                    ? 'text-shiba-orange font-medium'
                    : 'text-forest-dark hover:text-shiba-orange'
                }`}
              >
                {item.label}
                {pathname === item.href && (
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-shiba-orange rounded-full"></span>
                )}
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-shiba-orange rounded-full transition-all duration-300 group-hover:w-full"></span>
              </Link>
            ))}
          </nav>
        </div>

        {/* Мобильное меню (выпадающее) */}
        <div
          className={`md:hidden overflow-hidden transition-all duration-300 ${
            isMenuOpen
              ? 'max-h-96 opacity-100 mt-4 pb-4'
              : 'max-h-0 opacity-0'
          }`}
        >
          <nav className="flex flex-col space-y-2 pt-2">
            {[
              { href: '/dogs', label: 'Собаки' },
              { href: '/puppies', label: 'Щенки' },
              { href: '/graduates', label: 'Выпускники' },
              { href: '/gallery', label: 'Галерея' },
              { href: '/news', label: 'Новости' },
              { href: '/contacts', label: 'Контакты' }
            ].map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  pathname === item.href
                    ? 'bg-forest-bg/10 text-shiba-orange font-medium'
                    : 'text-forest-dark hover:bg-forest-bg/5'
                }`}
              >
                {item.label}
              </Link>
            ))}
          </nav>
        </div>
      </div>
    </header>
  );
}
