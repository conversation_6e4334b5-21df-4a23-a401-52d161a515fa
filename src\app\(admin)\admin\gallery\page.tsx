'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { formatDate } from '@/lib/utils';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface Album {
  id: string;
  title: string;
  description: string | null;
  slug: string;
  isPublished: boolean;
  categoryId: string | null;
  category: {
    id: string;
    name: string;
  } | null;
  photos: Photo[];
  createdAt: string;
  updatedAt: string;
}

export default function GalleryPage() {
  const [albums, setAlbums] = useState<Album[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const fetchAlbums = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/gallery');
      
      if (!response.ok) {
        throw new Error('Ошибка при загрузке данных');
      }
      
      const data = await response.json();
      setAlbums(data);
    } catch (err) {
      setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при загрузке альбомов:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAlbums();
  }, []);

  const handleAlbumClick = (album: Album) => {
    router.push(`/admin/gallery/${album.slug}`);
  };

  if (isLoading) {
    return (
      <AdminLayout title="Управление галереей">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Управление галереей">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            Всего альбомов: {albums.length}
          </p>
        </div>
        <div className="flex space-x-4">
          <Link href="/admin/gallery/categories">
            <Button variant="secondary">Категории</Button>
          </Link>
          <Link href="/admin/gallery/create">
            <Button>Создать альбом</Button>
          </Link>
        </div>
      </div>

      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {albums.length === 0 ? (
        <div className="bg-white shadow-md rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">Альбомы не найдены</p>
          <Link href="/admin/gallery/create">
            <Button>Создать первый альбом</Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {albums.map((album) => (
            <div
              key={album.id}
              className="bg-white shadow-md rounded-lg overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleAlbumClick(album)}
            >
              <div className="aspect-video relative">
                {album.photos.length > 0 ? (
                  <img
                    src={album.photos.find(photo => photo.isMain)?.url || album.photos[0].url}
                    alt={album.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-12 w-12 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                )}
                <div className="absolute top-2 right-2">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      album.isPublished
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {album.isPublished ? 'Опубликовано' : 'Черновик'}
                  </span>
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-medium text-lg mb-1">{album.title}</h3>
                {album.category && (
                  <p className="text-sm text-gray-600 mb-2">
                    Категория: {album.category.name}
                  </p>
                )}
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-500">
                    {album.photos.length} фото
                  </p>
                  <p className="text-sm text-gray-500">
                    {formatDate(album.createdAt)}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </AdminLayout>
  );
}
