'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import Select from '@/components/ui/Select';
import FileUpload from '@/components/ui/FileUpload';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface Album {
  id: string;
  title: string;
  description: string | null;
  slug: string;
  isPublished: boolean;
  categoryId: string | null;
  category: {
    id: string;
    name: string;
  } | null;
  photos: Photo[];
  createdAt: string;
  updatedAt: string;
}

const albumSchema = z.object({
  title: z.string().min(1, 'Название альбома обязательно'),
  description: z.string().optional(),
  categoryId: z.string().optional(),
  isPublished: z.boolean().default(false),
});

type AlbumFormData = z.infer<typeof albumSchema>;

export default function AlbumPage({ params }: { params: { slug: string } }) {
  const [album, setAlbum] = useState<Album | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [photos, setPhotos] = useState<File[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<AlbumFormData>({
    resolver: zodResolver(albumSchema),
  });

  useEffect(() => {
    const fetchAlbum = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/gallery/${params.slug}`);
        
        if (!response.ok) {
          throw new Error('Ошибка при загрузке данных');
        }
        
        const data: Album = await response.json();
        setAlbum(data);
        
        // Преобразуем данные для формы
        reset({
          title: data.title,
          description: data.description || '',
          categoryId: data.categoryId || '',
          isPublished: data.isPublished,
        });
      } catch (err) {
        setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
        console.error('Ошибка при загрузке альбома:', err);
      } finally {
        setIsLoading(false);
      }
    };

    const fetchCategories = async () => {
      setIsLoadingCategories(true);
      try {
        const response = await fetch('/api/gallery/categories');
        
        if (!response.ok) {
          throw new Error('Ошибка при загрузке категорий');
        }
        
        const data = await response.json();
        setCategories(data);
      } catch (err) {
        console.error('Ошибка при загрузке категорий:', err);
        setError('Произошла ошибка при загрузке списка категорий');
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchAlbum();
    fetchCategories();
  }, [params.slug, reset]);

  const onSubmit = async (data: AlbumFormData) => {
    if (!album) return;
    
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Отправляем запрос на обновление альбома
      const response = await fetch(`/api/gallery/${params.slug}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Ошибка при обновлении альбома');
      }

      // Если есть новые фотографии, загружаем их
      if (photos.length > 0) {
        for (let i = 0; i < photos.length; i++) {
          const formData = new FormData();
          formData.append('file', photos[i]);
          formData.append('albumId', album.id);
          formData.append('isMain', i === 0 && album.photos.length === 0 ? 'true' : 'false');
          formData.append('order', (album.photos.length + i).toString());

          const photoResponse = await fetch('/api/gallery/photos', {
            method: 'POST',
            body: formData,
          });

          if (!photoResponse.ok) {
            console.error('Ошибка при загрузке фотографии:', await photoResponse.text());
          }
        }
      }

      setSuccess('Альбом успешно обновлен');
      
      // Обновляем данные на странице
      const updatedAlbumResponse = await fetch(`/api/gallery/${params.slug}`);
      if (updatedAlbumResponse.ok) {
        const updatedAlbum = await updatedAlbumResponse.json();
        setAlbum(updatedAlbum);
      }
      
      // Сбрасываем состояние
      setPhotos([]);
      setIsEditMode(false);
    } catch (err) {
      console.error('Ошибка при обновлении альбома:', err);
      setError('Произошла ошибка при обновлении альбома. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePhotosChange = (files: File[]) => {
    setPhotos(files);
  };

  const handleDeletePhoto = async (photoId: string) => {
    if (!album) return;
    
    try {
      const response = await fetch(`/api/gallery/photos/${photoId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при удалении фотографии');
      }
      
      // Обновляем данные на странице
      const updatedAlbumResponse = await fetch(`/api/gallery/${params.slug}`);
      if (updatedAlbumResponse.ok) {
        const updatedAlbum = await updatedAlbumResponse.json();
        setAlbum(updatedAlbum);
      }
    } catch (err) {
      console.error('Ошибка при удалении фотографии:', err);
      setError('Произошла ошибка при удалении фотографии. Пожалуйста, попробуйте позже.');
    }
  };

  const handleSetMainPhoto = async (photoId: string) => {
    if (!album) return;
    
    try {
      const response = await fetch(`/api/gallery/photos/${photoId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isMain: true }),
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при установке главной фотографии');
      }
      
      // Обновляем данные на странице
      const updatedAlbumResponse = await fetch(`/api/gallery/${params.slug}`);
      if (updatedAlbumResponse.ok) {
        const updatedAlbum = await updatedAlbumResponse.json();
        setAlbum(updatedAlbum);
      }
    } catch (err) {
      console.error('Ошибка при установке главной фотографии:', err);
      setError('Произошла ошибка при установке главной фотографии. Пожалуйста, попробуйте позже.');
    }
  };

  const handleDelete = async () => {
    if (!album) return;
    
    setIsDeleting(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/gallery/${params.slug}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при удалении альбома');
      }
      
      router.push('/admin/gallery');
    } catch (err) {
      setError('Произошла ошибка при удалении альбома. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при удалении альбома:', err);
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error && !album) {
    return (
      <AdminLayout title="Ошибка">
        <Alert type="error">{error}</Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/gallery')}>
            Вернуться к галерее
          </Button>
        </div>
      </AdminLayout>
    );
  }

  if (!album) {
    return (
      <AdminLayout title="Альбом не найден">
        <Alert type="error">Альбом не найден</Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/gallery')}>
            Вернуться к галерее
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={isEditMode ? `Редактирование: ${album.title}` : album.title}>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            {album.category ? `Категория: ${album.category.name}` : 'Без категории'} • 
            {album.isPublished ? ' Опубликовано' : ' Черновик'}
          </p>
        </div>
        <div className="flex space-x-4">
          {!isEditMode ? (
            <>
              <Button
                variant="secondary"
                onClick={() => setIsEditMode(true)}
              >
                Редактировать
              </Button>
              <Button
                variant="danger"
                onClick={() => setShowDeleteConfirm(true)}
              >
                Удалить
              </Button>
            </>
          ) : (
            <Button
              variant="secondary"
              onClick={() => setIsEditMode(false)}
            >
              Отмена
            </Button>
          )}
        </div>
      </div>

      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      {showDeleteConfirm && (
        <div className="mb-6">
          <Alert type="warning" title="Подтверждение удаления" onClose={() => setShowDeleteConfirm(false)}>
            <p className="mb-4">
              Вы уверены, что хотите удалить альбом "{album.title}"? Это действие нельзя отменить.
            </p>
            <div className="flex justify-end space-x-4">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowDeleteConfirm(false)}
              >
                Отмена
              </Button>
              <Button
                variant="danger"
                size="sm"
                isLoading={isDeleting}
                onClick={handleDelete}
              >
                Удалить
              </Button>
            </div>
          </Alert>
        </div>
      )}

      {isEditMode ? (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Основная информация</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Название альбома"
                {...register('title')}
                error={errors.title?.message}
                fullWidth
              />
              <Controller
                name="categoryId"
                control={control}
                render={({ field }) => (
                  <Select
                    label="Категория"
                    options={[
                      { value: '', label: 'Без категории' },
                      ...categories.map((category) => ({
                        value: category.id,
                        label: category.name,
                      })),
                    ]}
                    {...field}
                    error={errors.categoryId?.message}
                    fullWidth
                    disabled={isLoadingCategories}
                  />
                )}
              />
              <div className="md:col-span-2">
                <Textarea
                  label="Описание"
                  {...register('description')}
                  error={errors.description?.message}
                  rows={3}
                  fullWidth
                />
              </div>
            </div>
          </div>

          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Фотографии</h2>
            
            {album.photos.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Текущие фотографии</h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {album.photos.map((photo) => (
                    <div key={photo.id} className="relative group">
                      <div className={`relative aspect-square rounded-md overflow-hidden ${
                        photo.isMain ? 'ring-2 ring-blue-500' : ''
                      }`}>
                        <img
                          src={photo.url}
                          alt={photo.title || album.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <div className="flex space-x-2">
                          {!photo.isMain && (
                            <button
                              type="button"
                              onClick={() => handleSetMainPhoto(photo.id)}
                              className="bg-blue-500 text-white rounded-full p-1"
                              title="Сделать обложкой"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-4"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M5 13l4 4L19 7"
                                />
                              </svg>
                            </button>
                          )}
                          <button
                            type="button"
                            onClick={() => handleDeletePhoto(photo.id)}
                            className="bg-red-500 text-white rounded-full p-1"
                            title="Удалить"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            <div className="space-y-6">
              <FileUpload
                label="Загрузите новые фотографии"
                accept="image/*"
                multiple
                onChange={handlePhotosChange}
                fullWidth
              />
              {photos.length > 0 && (
                <p className="text-sm text-gray-500">
                  Выбрано новых фотографий: {photos.length}
                </p>
              )}
            </div>
          </div>

          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Публикация</h2>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPublished"
                {...register('isPublished')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
                Опубликовать на сайте
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsEditMode(false)}
            >
              Отмена
            </Button>
            <Button type="submit" isLoading={isSubmitting}>
              Сохранить
            </Button>
          </div>
        </form>
      ) : (
        <div className="space-y-6">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Информация об альбоме</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Название</p>
                <p className="font-medium">{album.title}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Категория</p>
                <p className="font-medium">{album.category ? album.category.name : 'Без категории'}</p>
              </div>
              {album.description && (
                <div className="md:col-span-2">
                  <p className="text-sm text-gray-500">Описание</p>
                  <p className="whitespace-pre-line">{album.description}</p>
                </div>
              )}
              <div>
                <p className="text-sm text-gray-500">Статус</p>
                <p>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      album.isPublished
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {album.isPublished ? 'Опубликовано' : 'Черновик'}
                  </span>
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Количество фотографий</p>
                <p className="font-medium">{album.photos.length}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Создано</p>
                <p className="font-medium">{formatDate(album.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Обновлено</p>
                <p className="font-medium">{formatDate(album.updatedAt)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium">Фотографии ({album.photos.length})</h2>
              <Button
                size="sm"
                onClick={() => setIsEditMode(true)}
              >
                Добавить фотографии
              </Button>
            </div>
            
            {album.photos.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {album.photos.map((photo) => (
                  <div key={photo.id} className="relative group">
                    <div className={`relative aspect-square rounded-md overflow-hidden ${
                      photo.isMain ? 'ring-2 ring-blue-500' : ''
                    }`}>
                      <img
                        src={photo.url}
                        alt={photo.title || album.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {photo.isMain && (
                      <div className="absolute top-2 right-2">
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Обложка
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>В альбоме нет фотографий</p>
                <Button
                  className="mt-4"
                  onClick={() => setIsEditMode(true)}
                >
                  Добавить фотографии
                </Button>
              </div>
            )}
          </div>

          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Предпросмотр</h2>
            <div className="flex justify-center">
              <Button
                variant="secondary"
                onClick={() => window.open(`/gallery/${album.slug}`, '_blank')}
              >
                Открыть на сайте
              </Button>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
