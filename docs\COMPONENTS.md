# 🧩 Компоненты системы

## Обзор

Система компонентов построена по принципу переиспользования и модульности. Все компоненты написаны на TypeScript и используют современные паттерны React.

## 📁 Структура компонентов

```
src/components/
├── admin/          # Административные компоненты
├── layout/         # Компоненты макета
└── ui/            # Переиспользуемые UI компоненты
```

## 🔧 Административные компоненты

### AdminLayout
**Путь**: `src/components/admin/AdminLayout.tsx`
**Назначение**: Основной макет для всех административных страниц

```typescript
interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}
```

**Функции:**
- Отображение боковой панели навигации
- Шапка с информацией о пользователе
- Адаптивная мобильная навигация
- Breadcrumbs навигация

**Используется в:**
- Все страницы `/admin/*`

### AdminSidebar
**Путь**: `src/components/admin/AdminSidebar.tsx`
**Назначение**: Боковая панель навигации в админке

**Функции:**
- Навигационное меню с иконками
- Активное состояние текущей страницы
- Группировка пунктов меню
- Логотип и брендинг

**Пункты меню:**
- 📊 Дашборд (`/admin`)
- 🐕 Собаки (`/admin/dogs`)
- 🐶 Щенки (`/admin/puppies`)
- 📰 Новости (`/admin/news`)
- 👥 Пользователи (`/admin/users`)
- 🎨 Hero-слайды (`/admin/hero-slides`)
- ⚙️ Настройки (`/admin/settings`)

### AdminHeader
**Путь**: `src/components/admin/AdminHeader.tsx`
**Назначение**: Шапка административной панели

**Функции:**
- Отображение заголовка страницы
- Информация о текущем пользователе
- Кнопка выхода из системы
- Переключатель мобильного меню

### AdminMobileNav
**Путь**: `src/components/admin/AdminMobileNav.tsx`
**Назначение**: Мобильная навигация для админки

**Функции:**
- Выдвижное меню для мобильных устройств
- Те же пункты меню, что и в боковой панели
- Overlay для закрытия меню

## 🏗️ Компоненты макета

### Layout
**Путь**: `src/components/layout/Layout.tsx`
**Назначение**: Основной макет для публичных страниц

```typescript
interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}
```

**Функции:**
- SEO мета-теги
- Шапка и подвал сайта
- Основная область контента

### Header
**Путь**: `src/components/layout/Header.tsx`
**Назначение**: Шапка сайта

**Функции:**
- Логотип питомника
- Главное навигационное меню
- Адаптивная мобильная навигация
- Кнопка входа в админку

**Пункты навигации:**
- 🏠 Главная (`/`)
- 🐕 Наши собаки (`/dogs`)
- 🐶 Щенки (`/puppies`)
- 🏆 Выпускники (`/graduates`)
- 📰 Новости (`/news`)
- 📞 Контакты (`/contact`)

### Footer
**Путь**: `src/components/layout/Footer.tsx`
**Назначение**: Подвал сайта

**Функции:**
- Контактная информация
- Ссылки на социальные сети
- Дополнительная навигация
- Копирайт

### Navigation
**Путь**: `src/components/layout/Navigation.tsx`
**Назначение**: Компонент навигации

**Функции:**
- Десктопное меню
- Мобильное выдвижное меню
- Активное состояние ссылок

## 🎨 UI компоненты

### Button
**Путь**: `src/components/ui/Button.tsx`
**Назначение**: Универсальная кнопка

```typescript
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}
```

**Варианты:**
- `primary` - Основная кнопка (оранжевая)
- `secondary` - Вторичная кнопка (серая)
- `danger` - Опасное действие (красная)
- `outline` - Контурная кнопка

### Input
**Путь**: `src/components/ui/Input.tsx`
**Назначение**: Поле ввода

```typescript
interface InputProps {
  label?: string;
  error?: string;
  type?: string;
  placeholder?: string;
  fullWidth?: boolean;
  disabled?: boolean;
  required?: boolean;
}
```

**Функции:**
- Валидация с отображением ошибок
- Различные типы (text, email, password, number)
- Адаптивная ширина

### Textarea
**Путь**: `src/components/ui/Textarea.tsx`
**Назначение**: Многострочное поле ввода

**Функции:**
- Автоматическое изменение высоты
- Подсчет символов
- Валидация

### Modal
**Путь**: `src/components/ui/Modal.tsx`
**Назначение**: Модальное окно

```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}
```

**Функции:**
- Overlay с затемнением
- Закрытие по ESC или клику вне окна
- Различные размеры
- Анимации появления/исчезновения

### Alert
**Путь**: `src/components/ui/Alert.tsx`
**Назначение**: Уведомления и алерты

```typescript
interface AlertProps {
  type: 'success' | 'error' | 'warning' | 'info';
  children: React.ReactNode;
  onClose?: () => void;
  autoClose?: boolean;
  duration?: number;
}
```

**Типы:**
- `success` - Успешное действие (зеленый)
- `error` - Ошибка (красный)
- `warning` - Предупреждение (желтый)
- `info` - Информация (синий)

### FileUpload
**Путь**: `src/components/ui/FileUpload.tsx`
**Назначение**: Загрузка файлов

```typescript
interface FileUploadProps {
  label?: string;
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  onChange: (files: File[]) => void;
  fullWidth?: boolean;
}
```

**Функции:**
- Drag & Drop загрузка
- Валидация типов файлов
- Ограничение размера
- Превью изображений

### HeroSlider
**Путь**: `src/components/ui/HeroSlider.tsx`
**Назначение**: Слайдер для главной страницы

```typescript
interface HeroSliderProps {
  slides: HeroSlide[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
}
```

**Функции:**
- Автопроигрывание слайдов
- Навигация стрелками
- Индикаторы слайдов
- Плавные переходы
- Адаптивный дизайн

### Decorations
**Путь**: `src/components/ui/Decorations.tsx`
**Назначение**: Декоративные элементы

**Компоненты:**
- `PawPrint` - Иконка лапки
- `Leaf` - Иконка листика
- `FloatingElements` - Плавающие элементы
- `SectionDivider` - Разделители секций

```typescript
// PawPrint
interface PawPrintProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// FloatingElements
interface FloatingElementsProps {
  count?: number;
  type?: 'paws' | 'leaves' | 'mixed';
  className?: string;
}

// SectionDivider
interface SectionDividerProps {
  type?: 'wave' | 'curve' | 'zigzag';
  position?: 'top' | 'bottom';
  color?: string;
  className?: string;
}
```

## 🎯 Специализированные компоненты

### DogCard
**Путь**: `src/components/DogCard.tsx`
**Назначение**: Карточка собаки

**Функции:**
- Отображение фото собаки
- Основная информация (имя, возраст, пол)
- Кнопка "Подробнее"
- Адаптивная сетка

### PuppyCard
**Путь**: `src/components/PuppyCard.tsx`
**Назначение**: Карточка щенка

**Функции:**
- Фото щенка
- Информация о щенке
- Цена и доступность
- Кнопка бронирования

### NewsCard
**Путь**: `src/components/NewsCard.tsx`
**Назначение**: Карточка новости

**Функции:**
- Превью изображение
- Заголовок и краткое описание
- Дата публикации
- Ссылка на полную статью

## 🔄 Хуки и утилиты

### useLocalStorage
**Назначение**: Работа с localStorage

```typescript
const [value, setValue] = useLocalStorage('key', defaultValue);
```

### useDebounce
**Назначение**: Задержка выполнения функции

```typescript
const debouncedValue = useDebounce(value, delay);
```

### useClickOutside
**Назначение**: Обработка кликов вне элемента

```typescript
const ref = useClickOutside(() => {
  // Обработчик клика вне элемента
});
```

## 📱 Адаптивность

Все компоненты используют Tailwind CSS классы для адаптивности:

- `sm:` - >= 640px
- `md:` - >= 768px
- `lg:` - >= 1024px
- `xl:` - >= 1280px

## 🎨 Стилизация

### CSS классы
- `.btn-shiba` - Основная кнопка в стиле сайта
- `.btn-outline-shiba` - Контурная кнопка
- `.card` - Базовая карточка
- `.form-group` - Группа формы

### Цветовая палитра
```css
:root {
  --forest-dark: #2d5016;
  --forest-medium: #4a7c59;
  --forest-light: #8fbc8f;
  --forest-bg: #f0f8f0;
  --shiba-orange: #ff6b35;
  --shiba-cream: #faf0e6;
}
```

## 🔧 Использование компонентов

### Пример использования Button
```tsx
<Button 
  variant="primary" 
  size="lg" 
  isLoading={isSubmitting}
  onClick={handleSubmit}
>
  Сохранить
</Button>
```

### Пример использования Modal
```tsx
<Modal 
  isOpen={isModalOpen} 
  onClose={() => setIsModalOpen(false)}
  title="Подтверждение"
  size="md"
>
  <p>Вы уверены, что хотите удалить этот элемент?</p>
  <div className="flex justify-end space-x-2 mt-4">
    <Button variant="secondary" onClick={() => setIsModalOpen(false)}>
      Отмена
    </Button>
    <Button variant="danger" onClick={handleDelete}>
      Удалить
    </Button>
  </div>
</Modal>
```

Эта система компонентов обеспечивает консистентный дизайн и удобство разработки для всего сайта питомника.
