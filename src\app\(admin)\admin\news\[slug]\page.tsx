'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { formatDate, formatFullDate } from '@/lib/utils';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface NewsItem {
  id: string;
  title: string;
  content: string;
  slug: string;
  isPublished: boolean;
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  photos: Photo[];
}

export default function NewsDetailsPage({ params }: { params: { slug: string } }) {
  const [newsItem, setNewsItem] = useState<NewsItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchNewsItem = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/news/${params.slug}`);

        if (!response.ok) {
          throw new Error('Ошибка при загрузке данных');
        }

        const data = await response.json();
        setNewsItem(data);
      } catch (err) {
        setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
        console.error('Ошибка при загрузке новости:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNewsItem();
  }, [params.slug]);

  const handleDelete = async () => {
    setIsDeleting(true);
    setError(null);

    try {
      const response = await fetch(`/api/news/${params.slug}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Ошибка при удалении новости');
      }

      router.push('/admin/news');
    } catch (err) {
      setError('Произошла ошибка при удалении новости. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при удалении новости:', err);
      setIsDeleting(false);
    }
  };

  const handlePublish = async () => {
    if (!newsItem) return;

    try {
      const response = await fetch(`/api/news/${params.slug}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isPublished: true,
          publishedAt: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Ошибка при публикации новости');
      }

      // Обновляем данные на странице
      const updatedNewsResponse = await fetch(`/api/news/${params.slug}`);
      if (updatedNewsResponse.ok) {
        const updatedNews = await updatedNewsResponse.json();
        setNewsItem(updatedNews);
      }
    } catch (err) {
      setError('Произошла ошибка при публикации новости. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при публикации новости:', err);
    }
  };

  const handleUnpublish = async () => {
    if (!newsItem) return;

    try {
      const response = await fetch(`/api/news/${params.slug}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isPublished: false,
        }),
      });

      if (!response.ok) {
        throw new Error('Ошибка при снятии новости с публикации');
      }

      // Обновляем данные на странице
      const updatedNewsResponse = await fetch(`/api/news/${params.slug}`);
      if (updatedNewsResponse.ok) {
        const updatedNews = await updatedNewsResponse.json();
        setNewsItem(updatedNews);
      }
    } catch (err) {
      setError('Произошла ошибка при снятии новости с публикации. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при снятии новости с публикации:', err);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error || !newsItem) {
    return (
      <AdminLayout title="Ошибка">
        <Alert type="error">{error || 'Новость не найдена'}</Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/news')}>
            Вернуться к списку новостей
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={newsItem.title}>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            {newsItem.isPublished
              ? `Опубликовано: ${newsItem.publishedAt ? formatFullDate(newsItem.publishedAt) : 'Дата не указана'}`
              : 'Черновик'}
          </p>
        </div>
        <div className="flex space-x-4">
          {!newsItem.isPublished ? (
            <Button variant="success" onClick={handlePublish}>
              Опубликовать
            </Button>
          ) : (
            <Button variant="warning" onClick={handleUnpublish}>
              Снять с публикации
            </Button>
          )}
          <Link href={`/admin/news/${newsItem.slug}/edit`}>
            <Button variant="secondary">Редактировать</Button>
          </Link>
          <Button
            variant="danger"
            onClick={() => setShowDeleteConfirm(true)}
          >
            Удалить
          </Button>
        </div>
      </div>

      {showDeleteConfirm && (
        <div className="mb-6">
          <Alert type="warning" title="Подтверждение удаления" onClose={() => setShowDeleteConfirm(false)}>
            <p className="mb-4">
              Вы уверены, что хотите удалить новость "{newsItem.title}"? Это действие нельзя отменить.
            </p>
            <div className="flex justify-end space-x-4">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowDeleteConfirm(false)}
              >
                Отмена
              </Button>
              <Button
                variant="danger"
                size="sm"
                isLoading={isDeleting}
                onClick={handleDelete}
              >
                Удалить
              </Button>
            </div>
          </Alert>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            {newsItem.photos && newsItem.photos.length > 0 ? (
              <div className="relative aspect-video">
                <img
                  src={newsItem.photos.find(photo => photo.isMain)?.url || newsItem.photos[0].url}
                  alt={newsItem.title}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="aspect-video bg-gray-200 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-24 w-24 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
            )}
          </div>

          <div className="mt-6 bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Статус</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Статус</p>
                <p>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      newsItem.isPublished
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {newsItem.isPublished ? 'Опубликовано' : 'Черновик'}
                  </span>
                </p>
              </div>
              {newsItem.isPublished && newsItem.publishedAt && (
                <div>
                  <p className="text-sm text-gray-500">Дата публикации</p>
                  <p className="font-medium">{formatDate(newsItem.publishedAt)}</p>
                </div>
              )}
            </div>
          </div>

          <div className="mt-6 bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Системная информация</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">ID</p>
                <p className="font-mono text-sm">{newsItem.id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Slug</p>
                <p className="font-mono text-sm">{newsItem.slug}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Создано</p>
                <p className="font-mono text-sm">{formatDate(newsItem.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Обновлено</p>
                <p className="font-mono text-sm">{formatDate(newsItem.updatedAt)}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="md:col-span-2">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h1 className="text-2xl font-bold mb-4">{newsItem.title}</h1>
            <div className="prose max-w-none">
              <div className="whitespace-pre-line">{newsItem.content}</div>
            </div>
          </div>

          {newsItem.photos && newsItem.photos.length > 0 && (
            <div className="mt-6 bg-white shadow-md rounded-lg p-6">
              <h2 className="text-lg font-medium mb-4">Фотографии ({newsItem.photos.length})</h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                {newsItem.photos.map((photo) => (
                  <div key={photo.id} className="relative group">
                    <div className={`relative aspect-square rounded-md overflow-hidden ${
                      photo.isMain ? 'ring-2 ring-blue-500' : ''
                    }`}>
                      <img
                        src={photo.url}
                        alt={photo.title || newsItem.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {photo.isMain && (
                      <div className="absolute top-2 left-2">
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Обложка
                        </span>
                      </div>
                    )}
                    {photo.title && (
                      <div className="mt-1">
                        <p className="text-sm font-medium truncate">{photo.title}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="mt-6 bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Предпросмотр</h2>
            <div className="flex justify-center">
              <Button
                variant="secondary"
                onClick={() => window.open(`/news/${newsItem.slug}`, '_blank')}
              >
                Открыть на сайте
              </Button>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
