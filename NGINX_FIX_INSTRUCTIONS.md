# Исправление ошибки nginx "upstream directive is not allowed here"

## Проблема

Ошибка `nginx: [emerg] "upstream" directive is not allowed here` возникает потому, что директивы `upstream` и `limit_req_zone` должны быть размещены в блоке `http` основного файла nginx.conf, а не в отдельных файлах конфигурации.

## Быстрое решение

Используйте упрощенную конфигурацию без upstream:

```powershell
# Скопируйте простую конфигурацию
Copy-Item "nginx-dogs-simple.conf" "C:\nginx\conf\conf.d\ta-shiba.conf"

# Проверьте конфигурацию
C:\nginx\nginx.exe -t

# Перезагрузите nginx
C:\nginx\nginx.exe -s reload
```

## Полное решение (рекомендуется)

### 1. Найдите ваш основной nginx.conf

Обычно находится в:
- `C:\nginx\conf\nginx.conf`
- `C:\nginx-1.28.0\conf\nginx.conf`

### 2. Отредактируйте nginx.conf

Добавьте в блок `http` следующие директивы:

```nginx
http {
    # Существующие настройки...
    
    # Rate limiting для Dogs Website
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
    
    # Upstream для Dogs Website
    upstream dogs_app {
        server 127.0.0.1:3000;
        keepalive 32;
    }
    
    # Включение конфигураций сайтов
    include conf.d/*.conf;
    
    # Остальные настройки...
}
```

### 3. Используйте полную конфигурацию

После обновления nginx.conf можете использовать `nginx-dogs-site.conf` с раскомментированными директивами rate limiting.

## Пошаговая инструкция

### Шаг 1: Используйте простую конфигурацию

```powershell
# Остановите nginx (если запущен)
taskkill /f /im nginx.exe

# Скопируйте простую конфигурацию
Copy-Item "nginx-dogs-simple.conf" "C:\nginx\conf\conf.d\ta-shiba.conf"

# Проверьте конфигурацию
C:\nginx\nginx.exe -t
```

### Шаг 2: Запустите nginx

```powershell
# Запустите nginx
C:\nginx\nginx.exe

# Проверьте, что nginx запущен
Get-Process | Where-Object { $_.Name -like "*nginx*" }
```

### Шаг 3: Протестируйте

```powershell
# Проверьте локальный доступ
.\test-ta-shiba.ps1

# Проверьте в браузере
# http://localhost:8080
```

## Структура файлов nginx

```
C:\nginx\
├── conf\
│   ├── nginx.conf              # Основной файл конфигурации
│   └── conf.d\
│       └── ta-shiba.conf       # Ваша конфигурация сайта
├── logs\
├── temp\
└── nginx.exe
```

## Проверка конфигурации

```powershell
# Проверка синтаксиса
C:\nginx\nginx.exe -t

# Просмотр текущей конфигурации
C:\nginx\nginx.exe -T

# Перезагрузка без остановки
C:\nginx\nginx.exe -s reload

# Остановка nginx
C:\nginx\nginx.exe -s quit
```

## Устранение проблем

### Ошибка "bind() to 0.0.0.0:80 failed"

Порт 80 уже используется другим процессом:

```powershell
# Найдите процесс, использующий порт 80
netstat -ano | findstr :80

# Остановите процесс (замените PID)
taskkill /PID <PID> /F
```

### Ошибка "could not open error log file"

Создайте папку logs:

```powershell
New-Item -ItemType Directory -Path "C:\nginx\logs" -Force
```

### Ошибка доступа к файлам

Запустите PowerShell от имени администратора.

## Тестирование после исправления

```powershell
# Полный тест системы
.\test-ta-shiba.ps1 -Verbose

# Проверка только nginx
Test-NetConnection -ComputerName localhost -Port 8080
```

## Следующие шаги

После успешного запуска nginx:

1. Обновите DuckDNS IP адрес
2. Настройте проброс портов в роутере
3. Протестируйте внешний доступ
4. Настройте SSL сертификаты

## Полезные команды

```powershell
# Статус nginx процессов
Get-Process | Where-Object { $_.Name -like "*nginx*" }

# Проверка открытых портов
netstat -an | findstr ":80\|:8080\|:443"

# Просмотр логов nginx
Get-Content "C:\nginx\logs\error.log" -Tail 20

# Принудительная остановка всех nginx процессов
Get-Process | Where-Object { $_.Name -like "*nginx*" } | Stop-Process -Force
```
