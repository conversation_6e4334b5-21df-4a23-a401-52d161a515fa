import Image from 'next/image';
import Link from 'next/link';
import Layout from '@/components/layout/Layout';
import { prisma } from '@/lib/db';
import { formatDate } from '@/lib/utils';
import { notFound } from 'next/navigation';
import ContactButton from '@/components/ContactButton';
import PuppyGallery from './PuppyGallery';
import { PawPrint, Leaf, FloatingElements } from '@/components/ui/Decorations';

// Генерируем метаданные для страницы
export async function generateMetadata({ params }: { params: { slug: string } }) {
  const { slug } = await params;
  const puppy = await prisma.puppy.findUnique({
    where: { slug },
    include: {
      breeding: {
        include: {
          father: true,
          mother: true,
        },
      },
    },
  });

  if (!puppy) {
    return {
      title: 'Щенок не найден',
      description: 'Запрашиваемый щенок не найден',
    };
  }

  const breed = puppy.breeding?.father.breed || 'Щенок';
  const status = puppy.status === 'AVAILABLE' ? 'доступен для продажи' :
                 puppy.status === 'RESERVED' ? 'зарезервирован' : 'продан';

  return {
    title: `${puppy.name || 'Щенок'} - ${breed} - Питомник собак`,
    description: puppy.description || `${breed} ${puppy.gender === 'MALE' ? 'кобель' : 'сука'}, ${status}. Дата рождения: ${formatDate(puppy.birthDate)}.`,
  };
}

export default async function PuppyPage({ params }: { params: { slug: string } }) {
  const { slug } = await params;

  // Получаем настройки сайта
  const settingsRecords = await prisma.settings.findMany();

  // Преобразуем записи в объект для удобства использования
  const settings = settingsRecords.reduce((acc, record) => {
    acc[record.key] = record.value;
    return acc;
  }, {} as Record<string, string>);

  // Получаем информацию о щенке
  const puppy = await prisma.puppy.findUnique({
    where: {
      slug,
      isPublished: true,
    },
    include: {
      photos: {
        orderBy: {
          order: 'asc',
        },
      },
      breeding: {
        include: {
          father: {
            include: {
              photos: true,
            },
          },
          mother: {
            include: {
              photos: true,
            },
          },
        },
      },
    },
  });

  // Если щенок не найден, возвращаем 404
  if (!puppy) {
    notFound();
  }

  // Находим главное фото
  const mainPhoto = puppy.photos.find(photo => photo.isMain) || puppy.photos[0];

  // Рассчитываем возраст щенка в месяцах
  const birthDate = new Date(puppy.birthDate);
  const today = new Date();
  const ageInMonths = (today.getFullYear() - birthDate.getFullYear()) * 12 + today.getMonth() - birthDate.getMonth();

  return (
    <Layout>
      <section className="pt-16 pb-24 bg-forest-bg relative">
        <FloatingElements count={4} type="mixed" className="absolute inset-0 z-0 pointer-events-none" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="mb-8">
            <Link href="/puppies" className="text-forest-dark hover:text-shiba-orange flex items-center transition-colors">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Назад к списку щенков
            </Link>
          </div>

          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            {/* Профиль щенка в стиле соцсетей */}
            <div className="relative h-48 md:h-64 bg-forest-bg/50">
              {/* Фоновое изображение профиля */}
              {puppy.photos.length > 1 && (
                <div className="absolute inset-0 overflow-hidden">
                  <Image
                    src={puppy.photos[1].url}
                    alt={`${puppy.name || 'Щенок'} - фон`}
                    fill
                    style={{ objectFit: 'cover', objectPosition: 'center' }}
                    className="opacity-30"
                  />
                  <div className="absolute inset-0 bg-gradient-to-b from-forest-dark/30 to-forest-medium/50"></div>
                </div>
              )}

              {/* Статус щенка */}
              <div className="absolute top-4 right-4">
                <span className={`inline-block px-4 py-2 rounded-full text-sm font-bold shadow-md ${
                  puppy.status === 'AVAILABLE'
                    ? 'bg-green-100 text-green-800'
                    : puppy.status === 'RESERVED'
                    ? 'bg-orange-100 text-orange-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {puppy.status === 'AVAILABLE' ? 'Доступен для продажи' :
                   puppy.status === 'RESERVED' ? 'Зарезервирован' : 'Продан'}
                </span>
              </div>

              {/* Аватар щенка */}
              <div className="absolute -bottom-16 left-8 w-32 h-32 md:w-40 md:h-40 rounded-full border-4 border-white shadow-lg overflow-hidden bg-white">
                {mainPhoto ? (
                  <Image
                    src={mainPhoto.url}
                    alt={puppy.name || 'Щенок'}
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                ) : (
                  <div className="w-full h-full bg-forest-bg flex items-center justify-center">
                    <span className="text-forest-medium">Нет фото</span>
                  </div>
                )}
              </div>

              {/* Кнопки действий */}
              {puppy.status === 'AVAILABLE' && (
                <div className="absolute bottom-4 right-4 flex space-x-2">
                  <ContactButton
                    subjectId={puppy.id}
                    subjectType="puppy"
                    subjectName={puppy.name || 'Щенок'}
                    buttonText="Связаться"
                    className="bg-[#3e5641] text-white hover:bg-[#3e5641]/90 px-4 py-2 rounded-lg shadow-md font-medium"
                  />
                </div>
              )}
            </div>

            {/* Информация о щенке */}
            <div className="pt-20 px-8 pb-8">
              <div className="flex flex-wrap justify-between items-start mb-6">
                <div className="mb-4 md:mb-0">
                  <div className="flex items-center">
                    <h1 className="text-3xl font-bold text-forest-dark">{puppy.name || 'Щенок'}</h1>
                    <div className="ml-3 bg-forest-bg text-forest-dark text-sm font-bold px-3 py-1 rounded-full">
                      {puppy.gender === 'MALE' ? 'Кобель' : 'Сука'}
                    </div>

                  </div>
                  {puppy.breeding?.father.breed && (
                    <p className="text-lg text-forest-medium mt-1">{puppy.breeding.father.breed}</p>
                  )}
                </div>

                {/* Статистика щенка */}
                <div className="flex flex-wrap gap-3">
                  <div className="bg-forest-bg/20 rounded-lg px-4 py-2 text-center">
                    <p className="text-forest-dark font-bold text-lg">{ageInMonths}</p>
                    <p className="text-forest-medium text-xs">{ageInMonths === 1 ? 'месяц' : ageInMonths < 5 ? 'месяца' : 'месяцев'}</p>
                  </div>

                  {puppy.color && (
                    <div className="bg-forest-bg/20 rounded-lg px-4 py-2 text-center">
                      <p className="text-forest-dark font-bold text-lg">{puppy.color}</p>
                      <p className="text-forest-medium text-xs">окрас</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Основная информация и галерея */}
              <div className="grid md:grid-cols-12 gap-6 mt-8">
                <div className="md:col-span-5">
                  {/* Основная информация */}
                  <div className="bg-forest-bg/10 rounded-xl p-5 mb-6">
                    <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                      <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                      </svg>
                      <span>Информация</span>
                    </h3>

                    <div className="space-y-3">
                      <div className="flex items-center border-b border-forest-pale pb-2">
                        <span className="font-medium w-32 text-forest-dark text-sm">Дата рождения:</span>
                        <span className="text-forest-medium">{formatDate(puppy.birthDate)}</span>
                      </div>

                      <div className="flex items-center border-b border-forest-pale pb-2">
                        <span className="font-medium w-32 text-forest-dark text-sm">Пол:</span>
                        <span className="text-forest-medium">{puppy.gender === 'MALE' ? 'Кобель' : 'Сука'}</span>
                      </div>

                      {puppy.color && (
                        <div className="flex items-center border-b border-forest-pale pb-2">
                          <span className="font-medium w-32 text-forest-dark text-sm">Окрас:</span>
                          <span className="text-forest-medium">{puppy.color}</span>
                        </div>
                      )}

                      {puppy.status && (
                        <div className="flex items-center border-b border-forest-pale pb-2">
                          <span className="font-medium w-32 text-forest-dark text-sm">Статус:</span>
                          <span className="text-forest-medium">
                            {puppy.status === 'AVAILABLE' ? 'Доступен для продажи' :
                             puppy.status === 'RESERVED' ? 'Зарезервирован' : 'Продан'}
                          </span>
                        </div>
                      )}


                    </div>
                  </div>

                  {/* Родители */}
                  {puppy.breeding && (
                    <div className="bg-forest-bg/10 rounded-xl p-5">
                      <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                        <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path>
                        </svg>
                        <span>Родители</span>
                      </h3>

                      <div className="space-y-4">
                        <Link
                          href={`/dogs/${puppy.breeding.father.slug}`}
                          className="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow"
                        >
                          {puppy.breeding.father.photos && puppy.breeding.father.photos.length > 0 ? (
                            <div className="relative w-12 h-12 rounded-full overflow-hidden mr-3 border-2 border-shiba-orange/20">
                              <Image
                                src={puppy.breeding.father.photos[0].url}
                                alt={puppy.breeding.father.name}
                                fill
                                style={{ objectFit: 'cover' }}
                              />
                            </div>
                          ) : (
                            <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-3 border-2 border-shiba-orange/20">
                              <span className="text-xs text-gray-400">Нет фото</span>
                            </div>
                          )}
                          <div>
                            <p className="font-medium text-forest-dark">Отец: {puppy.breeding.father.name}</p>
                            <p className="text-xs text-forest-medium">{puppy.breeding.father.breed}</p>
                          </div>
                        </Link>

                        <Link
                          href={`/dogs/${puppy.breeding.mother.slug}`}
                          className="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow"
                        >
                          {puppy.breeding.mother.photos && puppy.breeding.mother.photos.length > 0 ? (
                            <div className="relative w-12 h-12 rounded-full overflow-hidden mr-3 border-2 border-shiba-orange/20">
                              <Image
                                src={puppy.breeding.mother.photos[0].url}
                                alt={puppy.breeding.mother.name}
                                fill
                                style={{ objectFit: 'cover' }}
                              />
                            </div>
                          ) : (
                            <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-3 border-2 border-shiba-orange/20">
                              <span className="text-xs text-gray-400">Нет фото</span>
                            </div>
                          )}
                          <div>
                            <p className="font-medium text-forest-dark">Мать: {puppy.breeding.mother.name}</p>
                            <p className="text-xs text-forest-medium">{puppy.breeding.mother.breed}</p>
                          </div>
                        </Link>
                      </div>
                    </div>
                  )}
                </div>

                <div className="md:col-span-7">
                  {/* Галерея фотографий */}
                  <div className="bg-white rounded-xl overflow-hidden">
                    <PuppyGallery photos={puppy.photos} name={puppy.name || 'Щенок'} />
                  </div>

                  {/* Описание */}
                  {puppy.description && (
                    <div className="mt-6 bg-forest-bg/10 rounded-xl p-5">
                      <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                        <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                        </svg>
                        <span>Описание</span>
                      </h3>

                      <div className="text-forest-medium whitespace-pre-line leading-relaxed">
                        {puppy.description}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {puppy.breeding && (
            <div className="mt-12">
              <div className="flex items-center mb-6">
                <div className="h-px flex-grow bg-forest-light/20"></div>
                <div className="flex items-center mx-4">
                  <PawPrint size="sm" className="mr-3 text-shiba-orange" />
                  <h2 className="text-2xl font-bold text-forest-dark">Информация о вязке</h2>
                </div>
                <div className="h-px flex-grow bg-forest-light/20"></div>
              </div>

              <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                {/* Карточка вязки в стиле соцсетей */}
                <div className="p-6 border-b border-forest-pale/30">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-12 h-12 rounded-full bg-shiba-orange/10 flex items-center justify-center mr-4">
                        <svg className="w-6 h-6 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-forest-dark">Вязка от {formatDate(puppy.breeding.date)}</h3>
                        <p className="text-forest-medium text-sm">
                          Дата рождения помета: {formatDate(puppy.birthDate)}
                        </p>
                      </div>
                    </div>

                    <span className={`inline-block px-4 py-2 rounded-full text-sm font-bold shadow-md ${
                      puppy.breeding.status === 'COMPLETED'
                        ? 'bg-green-100 text-green-800'
                        : puppy.breeding.status === 'IN_PROGRESS'
                        ? 'bg-blue-100 text-blue-800'
                        : puppy.breeding.status === 'PLANNED'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {puppy.breeding.status === 'COMPLETED' ? 'Завершена' :
                       puppy.breeding.status === 'IN_PROGRESS' ? 'В процессе' :
                       puppy.breeding.status === 'PLANNED' ? 'Запланирована' : 'Отменена'}
                    </span>
                  </div>

                  {puppy.breeding.description && (
                    <div className="mt-4 text-forest-medium">
                      <p>{puppy.breeding.description}</p>
                    </div>
                  )}
                </div>

                {/* Родители */}
                <div className="grid md:grid-cols-2 gap-0">
                  <Link
                    href={`/dogs/${puppy.breeding.father.slug}`}
                    className="p-6 hover:bg-forest-bg/10 transition-colors border-r border-b border-forest-pale/30"
                  >
                    <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                      <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path>
                      </svg>
                      <span>Отец</span>
                    </h3>

                    <div className="flex items-start">
                      {puppy.breeding.father.photos && puppy.breeding.father.photos.length > 0 ? (
                        <div className="relative w-20 h-20 rounded-full overflow-hidden mr-4 border-2 border-shiba-orange/20">
                          <Image
                            src={puppy.breeding.father.photos[0].url}
                            alt={puppy.breeding.father.name}
                            fill
                            style={{ objectFit: 'cover' }}
                          />
                        </div>
                      ) : (
                        <div className="w-20 h-20 rounded-full bg-forest-bg flex items-center justify-center mr-4 border-2 border-shiba-orange/20">
                          <span className="text-forest-medium text-xs">Нет фото</span>
                        </div>
                      )}
                      <div>
                        <h4 className="font-bold text-forest-dark">{puppy.breeding.father.name}</h4>
                        <p className="text-forest-medium text-sm">{puppy.breeding.father.breed}</p>
                        <div className="flex items-center mt-2 text-forest-medium text-xs">
                          <svg className="w-3 h-3 mr-1 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path>
                          </svg>
                          <span>
                            Возраст: {new Date().getFullYear() - new Date(puppy.breeding.father.birthDate).getFullYear()} лет
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="mt-3 text-forest-dark/70 text-xs flex items-center">
                      <span>Подробнее о собаке</span>
                      <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </div>
                  </Link>

                  <Link
                    href={`/dogs/${puppy.breeding.mother.slug}`}
                    className="p-6 hover:bg-forest-bg/10 transition-colors border-b border-forest-pale/30"
                  >
                    <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                      <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path>
                      </svg>
                      <span>Мать</span>
                    </h3>

                    <div className="flex items-start">
                      {puppy.breeding.mother.photos && puppy.breeding.mother.photos.length > 0 ? (
                        <div className="relative w-20 h-20 rounded-full overflow-hidden mr-4 border-2 border-shiba-orange/20">
                          <Image
                            src={puppy.breeding.mother.photos[0].url}
                            alt={puppy.breeding.mother.name}
                            fill
                            style={{ objectFit: 'cover' }}
                          />
                        </div>
                      ) : (
                        <div className="w-20 h-20 rounded-full bg-forest-bg flex items-center justify-center mr-4 border-2 border-shiba-orange/20">
                          <span className="text-forest-medium text-xs">Нет фото</span>
                        </div>
                      )}
                      <div>
                        <h4 className="font-bold text-forest-dark">{puppy.breeding.mother.name}</h4>
                        <p className="text-forest-medium text-sm">{puppy.breeding.mother.breed}</p>
                        <div className="flex items-center mt-2 text-forest-medium text-xs">
                          <svg className="w-3 h-3 mr-1 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path>
                          </svg>
                          <span>
                            Возраст: {new Date().getFullYear() - new Date(puppy.breeding.mother.birthDate).getFullYear()} лет
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="mt-3 text-forest-dark/70 text-xs flex items-center">
                      <span>Подробнее о собаке</span>
                      <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          )}

          {puppy.status === 'AVAILABLE' && (
            <div className="mt-12">
              <div className="flex items-center mb-6">
                <div className="h-px flex-grow bg-forest-light/20"></div>
                <div className="flex items-center mx-4">
                  <Leaf size="sm" className="mr-3 text-shiba-orange" />
                  <h2 className="text-2xl font-bold text-forest-dark">Связаться с нами</h2>
                </div>
                <div className="h-px flex-grow bg-forest-light/20"></div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-8 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-40 h-40 bg-shiba-orange/5 rounded-bl-full"></div>
                <div className="absolute bottom-0 left-0 w-32 h-32 bg-forest-light/5 rounded-tr-full"></div>

                <div className="grid md:grid-cols-2 gap-8 items-center relative z-10">
                  <div>
                    <h3 className="text-xl font-bold mb-4 text-forest-dark">Интересует {puppy.name || 'этот щенок'}?</h3>
                    <p className="text-forest-medium mb-6">
                      Если вы заинтересованы в приобретении этого щенка или у вас есть вопросы, пожалуйста, свяжитесь с нами. Мы с радостью ответим на все ваши вопросы и предоставим дополнительную информацию.
                    </p>
                    <div className="space-y-4">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-shiba-orange/10 flex items-center justify-center mr-3">
                          <svg className="w-5 h-5 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                          </svg>
                        </div>
                        <span className="text-forest-dark">{settings?.contactPhone || '+7 (XXX) XXX-XX-XX'}</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-shiba-orange/10 flex items-center justify-center mr-3">
                          <svg className="w-5 h-5 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                          </svg>
                        </div>
                        <span className="text-forest-dark">{settings?.contactEmail || '<EMAIL>'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-forest-bg/30 rounded-lg p-6">
                    <h3 className="text-xl font-bold mb-4 text-forest-dark flex items-center">
                      <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                      </svg>
                      <span>Отправить запрос</span>
                    </h3>
                    <p className="text-forest-medium mb-4">
                      Заполните форму, и мы свяжемся с вами в ближайшее время
                    </p>
                    <ContactButton
                      subjectId={puppy.id}
                      subjectType="puppy"
                      subjectName={puppy.name || 'Щенок'}
                      buttonText="Отправить запрос"
                      className="w-full justify-center bg-[#3e5641] hover:bg-[#3e5641]/90 text-white font-medium py-3 rounded-lg transition-colors"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>
    </Layout>
  );
}
