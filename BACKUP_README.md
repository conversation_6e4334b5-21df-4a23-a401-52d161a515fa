# 🗄️ Руководство по резервному копированию

## Быстрые команды

### Создание backup
```bash
# Простой экспорт данных
npm run db:backup

# Полный backup (данные + схема + файлы + SQL)
npm run db:full-backup

# Обновление схемы из базы данных
npm run db:pull
```

### Восстановление backup
```bash
# Восстановление из JSON файла
npm run db:restore backups/prisma-backup-YYYY-MM-DD.json.gz

# Восстановление из конкретного файла
node scripts/import-data.js backups/full-backup-2024-12-23T10-30-00.json.gz
```

## Типы backup

### 1. Простой экспорт данных (`npm run db:backup`)
- ✅ Экспортирует все данные в JSON формат
- ✅ Сжимает файл для экономии места
- ✅ Быстрый и простой
- ❌ Не включает схему базы данных
- ❌ Не включает загруженные файлы

**Результат**: `backups/prisma-backup-YYYY-MM-DD.json.gz`

### 2. Полный backup (`npm run db:full-backup`)
- ✅ Экспортирует все данные
- ✅ Сохраняет схему базы данных
- ✅ Архивирует загруженные файлы
- ✅ Создает SQL дамп
- ✅ Генерирует отчет

**Результат**:
- `backups/full-backup-YYYY-MM-DD.json.gz` - данные
- `backups/schema-YYYY-MM-DD.prisma` - схема
- `backups/uploads-YYYY-MM-DD.tar.gz` - файлы
- `backups/sql-dump-YYYY-MM-DD.sql.gz` - SQL дамп
- `backups/backup-report-YYYY-MM-DD.txt` - отчет

## Структура backup файлов

### JSON backup содержит:
```json
{
  "timestamp": "2024-12-23T10:30:00.000Z",
  "version": "1.0",
  "schema": "// Prisma schema content",
  "data": {
    "users": [...],
    "dogs": [...],
    "puppies": [...],
    "news": [...],
    "heroSlides": [...],
    "settings": [...],
    "breedings": [...],
    "gallery": [...],
    "inquiries": [...],
    "photos": [...]
  }
}
```

## Автоматизация backup

### Ежедневный backup (Linux/Mac)
```bash
# Добавить в crontab
crontab -e

# Добавить строку для backup каждый день в 2:00
0 2 * * * cd /path/to/your/project && npm run db:full-backup
```

### Еженедельный backup (Windows)
Создайте bat файл `weekly-backup.bat`:
```batch
@echo off
cd /d "C:\path\to\your\project"
npm run db:full-backup
```

Добавьте в планировщик задач Windows.

## Восстановление

### Полное восстановление
```bash
# 1. Восстановление схемы
cp backups/schema-YYYY-MM-DD.prisma prisma/schema.prisma
npm run db:push

# 2. Восстановление данных
npm run db:restore backups/full-backup-YYYY-MM-DD.json.gz

# 3. Восстановление файлов
cd public
tar -xzf ../backups/uploads-YYYY-MM-DD.tar.gz
```

### Частичное восстановление
```bash
# Только данные (без очистки существующих)
node scripts/import-data.js backups/backup-file.json.gz
# Ответить "N" на вопрос об очистке данных
```

### Восстановление на новом сервере
```bash
# 1. Клонирование проекта
git clone <repository>
cd project

# 2. Установка зависимостей
npm install

# 3. Настройка базы данных
# Создать базу данных и настроить DATABASE_URL в .env

# 4. Восстановление схемы
cp backups/schema-YYYY-MM-DD.prisma prisma/schema.prisma
npx prisma migrate dev

# 5. Восстановление данных
node scripts/import-data.js backups/full-backup-YYYY-MM-DD.json.gz

# 6. Восстановление файлов
cd public
tar -xzf ../backups/uploads-YYYY-MM-DD.tar.gz
```

## Проверка backup

### Проверка целостности данных
```bash
# Создание тестовой базы данных
createdb test_restore

# Восстановление в тестовую базу
DATABASE_URL="postgresql://user:pass@localhost:5432/test_restore" \
node scripts/import-data.js backups/backup-file.json.gz

# Проверка количества записей
DATABASE_URL="postgresql://user:pass@localhost:5432/test_restore" \
npx prisma studio

# Удаление тестовой базы
dropdb test_restore
```

## Миграция между серверами

### Из development в production
```bash
# 1. Создание backup на dev сервере
npm run db:full-backup

# 2. Копирование файлов на production
scp backups/* user@production-server:/path/to/project/backups/

# 3. Восстановление на production
ssh user@production-server
cd /path/to/project
node scripts/import-data.js backups/full-backup-YYYY-MM-DD.json.gz
```

### Между разными типами баз данных
```bash
# Экспорт из PostgreSQL
npm run db:backup

# Изменение DATABASE_URL на новую базу данных
# Применение схемы
npm run db:push

# Импорт данных
npm run db:restore backups/backup-file.json.gz
```

## Безопасность backup

### Шифрование backup файлов
```bash
# Шифрование backup
gpg --symmetric --cipher-algo AES256 backups/backup-file.json.gz

# Расшифровка
gpg --decrypt backups/backup-file.json.gz.gpg > backups/backup-file.json.gz
```

### Удаленное хранение
```bash
# Загрузка в облако (AWS S3)
aws s3 cp backups/ s3://my-backup-bucket/dogs-db/ --recursive

# Загрузка в облако (Google Drive с rclone)
rclone copy backups/ gdrive:backups/dogs-db/
```

## Мониторинг backup

### Проверка размера backup файлов
```bash
# Размер всех backup файлов
du -sh backups/

# Список файлов с размерами
ls -lh backups/
```

### Очистка старых backup
```bash
# Удаление backup старше 30 дней
find backups/ -name "*.gz" -mtime +30 -delete
find backups/ -name "*.txt" -mtime +30 -delete
```

## Troubleshooting

### Ошибка "Table doesn't exist"
```bash
# Обновить схему перед импортом
npm run db:pull
npm run db:push
```

### Ошибка "Unique constraint violation"
```bash
# Очистить базу данных перед импортом
npm run db:reset
# Ответить "y" на вопрос об очистке при импорте
```

### Большой размер backup файлов
```bash
# Создать backup только структуры (без данных)
npx prisma db pull
# Сохранить только schema.prisma файл
```

## Лучшие практики

1. **Регулярность**: Делайте backup ежедневно
2. **Тестирование**: Проверяйте возможность восстановления
3. **Хранение**: Храните backup в нескольких местах
4. **Документирование**: Ведите журнал backup операций
5. **Автоматизация**: Используйте cron или планировщик задач
6. **Мониторинг**: Следите за размером и успешностью backup
7. **Безопасность**: Шифруйте backup файлы при необходимости
