import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import AdminAccess from "@/components/ui/AdminAccess";
import { prisma } from '@/lib/db';

const inter = Inter({
  subsets: ["latin", "cyrillic"],
  variable: "--font-inter",
});

export async function generateMetadata(): Promise<Metadata> {
  // Получаем настройки сайта
  const settingsList = await prisma.settings.findMany();
  const settings = settingsList.reduce((acc, setting) => {
    acc[setting.key] = setting.value;
    return acc;
  }, {} as Record<string, string>);

  return {
    title: settings?.metaTitle || settings?.siteName || 'Питомник собак',
    description: settings?.metaDescription || settings?.siteDescription || 'Разведение и продажа породистых собак высшего качества',
    keywords: settings?.metaKeywords || 'питомник собак, породистые собаки, щенки, купить щенка',
    icons: settings?.favicon ? { icon: settings.favicon } : undefined,
  };
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ru">
      <body className={`${inter.variable} antialiased`}>
        {children}
        <AdminAccess />
      </body>
    </html>
  );
}
