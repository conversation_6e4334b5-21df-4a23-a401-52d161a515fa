'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { formatDate, formatFullDate, getGenderText } from '@/lib/utils';

interface Dog {
  id: string;
  name: string;
  breed: string;
  gender: 'MALE' | 'FEMALE';
  slug: string;
  photos: {
    id: string;
    url: string;
    isMain: boolean;
  }[];
}

interface Puppy {
  id: string;
  name: string;
  gender: 'MALE' | 'FEMALE';
  birthDate: string;
  status: 'AVAILABLE' | 'RESERVED' | 'SOLD';
  slug: string;
}

interface Breeding {
  id: string;
  date: string;
  description: string | null;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  mother: <PERSON>;
  father: <PERSON>;
  puppies: Puppy[];
  createdAt: string;
  updatedAt: string;
}

export default function BreedingDetailsPage({ params }: { params: { id: string } }) {
  const [breeding, setBreeding] = useState<Breeding | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchBreeding = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/breedings/${params.id}`);
        
        if (!response.ok) {
          throw new Error('Ошибка при загрузке данных');
        }
        
        const data = await response.json();
        setBreeding(data);
      } catch (err) {
        setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
        console.error('Ошибка при загрузке вязки:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBreeding();
  }, [params.id]);

  const handleDelete = async () => {
    setIsDeleting(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/breedings/${params.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при удалении вязки');
      }
      
      router.push('/admin/breedings');
    } catch (err) {
      setError('Произошла ошибка при удалении вязки. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при удалении вязки:', err);
      setIsDeleting(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return 'bg-blue-100 text-blue-800';
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return 'Запланирована';
      case 'IN_PROGRESS':
        return 'В процессе';
      case 'COMPLETED':
        return 'Завершена';
      case 'CANCELLED':
        return 'Отменена';
      default:
        return 'Неизвестно';
    }
  };

  const getPuppyStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800';
      case 'RESERVED':
        return 'bg-yellow-100 text-yellow-800';
      case 'SOLD':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPuppyStatusText = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'Доступен';
      case 'RESERVED':
        return 'Зарезервирован';
      case 'SOLD':
        return 'Продан';
      default:
        return 'Неизвестно';
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error || !breeding) {
    return (
      <AdminLayout title="Ошибка">
        <Alert type="error">{error || 'Вязка не найдена'}</Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/breedings')}>
            Вернуться к списку вязок
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={`Вязка: ${breeding.mother.name} и ${breeding.father.name}`}>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            Дата вязки: {formatFullDate(breeding.date)}
          </p>
        </div>
        <div className="flex space-x-4">
          <Link href={`/admin/breedings/${breeding.id}/edit`}>
            <Button variant="secondary">Редактировать</Button>
          </Link>
          <Button
            variant="danger"
            onClick={() => setShowDeleteConfirm(true)}
          >
            Удалить
          </Button>
        </div>
      </div>

      {showDeleteConfirm && (
        <div className="mb-6">
          <Alert type="warning" title="Подтверждение удаления" onClose={() => setShowDeleteConfirm(false)}>
            <p className="mb-4">
              Вы уверены, что хотите удалить вязку между {breeding.mother.name} и {breeding.father.name}? Это действие нельзя отменить.
            </p>
            <div className="flex justify-end space-x-4">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowDeleteConfirm(false)}
              >
                Отмена
              </Button>
              <Button
                variant="danger"
                size="sm"
                isLoading={isDeleting}
                onClick={handleDelete}
              >
                Удалить
              </Button>
            </div>
          </Alert>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Информация о вязке</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Статус</p>
                <p>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(
                      breeding.status
                    )}`}
                  >
                    {getStatusText(breeding.status)}
                  </span>
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Дата вязки</p>
                <p className="font-medium">{formatDate(breeding.date)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Количество щенков</p>
                <p className="font-medium">{breeding.puppies.length}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">ID</p>
                <p className="font-mono text-sm">{breeding.id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Создано</p>
                <p className="font-mono text-sm">{formatDate(breeding.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Обновлено</p>
                <p className="font-mono text-sm">{formatDate(breeding.updatedAt)}</p>
              </div>
            </div>
          </div>

          {breeding.description && (
            <div className="mt-6 bg-white shadow-md rounded-lg p-6">
              <h2 className="text-lg font-medium mb-4">Описание</h2>
              <p className="whitespace-pre-line">{breeding.description}</p>
            </div>
          )}
        </div>

        <div className="md:col-span-2">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Родители</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Мать</h3>
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="aspect-square relative">
                    {breeding.mother.photos && breeding.mother.photos.length > 0 ? (
                      <img
                        src={breeding.mother.photos.find(photo => photo.isMain)?.url || breeding.mother.photos[0].url}
                        alt={breeding.mother.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-12 w-12 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h4 className="font-medium text-lg">{breeding.mother.name}</h4>
                    <p className="text-gray-600">{breeding.mother.breed}</p>
                    <p className="text-gray-600">{getGenderText(breeding.mother.gender)}</p>
                    <Link 
                      href={`/admin/dogs/${breeding.mother.slug}`}
                      className="mt-2 inline-flex items-center text-blue-600 hover:text-blue-800"
                    >
                      Просмотреть профиль
                      <svg
                        className="ml-1 h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Отец</h3>
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="aspect-square relative">
                    {breeding.father.photos && breeding.father.photos.length > 0 ? (
                      <img
                        src={breeding.father.photos.find(photo => photo.isMain)?.url || breeding.father.photos[0].url}
                        alt={breeding.father.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-12 w-12 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h4 className="font-medium text-lg">{breeding.father.name}</h4>
                    <p className="text-gray-600">{breeding.father.breed}</p>
                    <p className="text-gray-600">{getGenderText(breeding.father.gender)}</p>
                    <Link 
                      href={`/admin/dogs/${breeding.father.slug}`}
                      className="mt-2 inline-flex items-center text-blue-600 hover:text-blue-800"
                    >
                      Просмотреть профиль
                      <svg
                        className="ml-1 h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 bg-white shadow-md rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium">Щенки ({breeding.puppies.length})</h2>
              <Link href={`/admin/puppies/create?breedingId=${breeding.id}`}>
                <Button size="sm">Добавить щенка</Button>
              </Link>
            </div>
            
            {breeding.puppies.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {breeding.puppies.map((puppy) => (
                  <div key={puppy.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium">{puppy.name}</h3>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPuppyStatusBadgeClass(
                          puppy.status
                        )}`}
                      >
                        {getPuppyStatusText(puppy.status)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{getGenderText(puppy.gender)}</p>
                    <p className="text-sm text-gray-600">Дата рождения: {formatDate(puppy.birthDate)}</p>
                    <div className="mt-2">
                      <Link 
                        href={`/admin/puppies/${puppy.slug}`}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        Просмотреть
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Щенки еще не добавлены</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
