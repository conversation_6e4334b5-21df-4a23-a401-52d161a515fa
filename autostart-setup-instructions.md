# Инструкция по настройке автозапуска Dogs Website

## Вариант 1: Windows Service (Рекомендуется)

### Преимущества:
- Запускается до входа пользователя в систему
- Автоматический перезапуск при сбоях
- Лучшая стабильность и безопасность
- Управление через Services.msc

### Установка:

1. **Установите NSSM (Non-Sucking Service Manager):**
   - Скачайте с [https://nssm.cc/download](https://nssm.cc/download)
   - Распакуйте в папку `C:\nssm\`
   - Или установите через Chocolatey: `choco install nssm`

2. **Создайте службу:**
   ```powershell
   # Запустите PowerShell от имени администратора
   .\create-service.ps1
   ```

3. **Управление службой:**
   ```powershell
   # Запуск
   Start-Service -Name DogsWebsite
   
   # Остановка
   Stop-Service -Name DogsWebsite
   
   # Перезапуск
   Restart-Service -Name DogsWebsite
   
   # Удаление службы
   C:\nssm\nssm.exe remove DogsWebsite confirm
   ```

## Вариант 2: Автозагрузка Windows

### Преимущества:
- Простая настройка
- Не требует дополнительных инструментов
- Легко включить/отключить

### Установка:

1. **Настройте автозапуск:**
   ```powershell
   .\setup-autostart.ps1
   ```

2. **Управление:**
   - Запуск: `start-dogs-website.bat`
   - Остановка: `stop-dogs-website.bat`
   - Отключение автозапуска: удалите ярлык из папки автозагрузки

## Вариант 3: Планировщик задач

### Создание задачи:

1. Откройте "Планировщик задач" (Task Scheduler)
2. Создайте новую задачу:
   - **Имя**: "Dogs Website Startup"
   - **Безопасность**: Выполнять независимо от входа пользователя
   - **Триггер**: При запуске системы
   - **Действие**: Запуск программы
   - **Программа**: `npm`
   - **Аргументы**: `run start`
   - **Рабочая папка**: путь к вашему проекту

## Проверка автозапуска

1. **Перезагрузите компьютер**
2. **Проверьте, что приложение запустилось:**
   ```cmd
   netstat -an | findstr :3000
   ```
3. **Откройте браузер и перейдите на:**
   - `http://localhost:3000` (локально)
   - `https://yourdomain.duckdns.org` (из интернета)

## Логи и мониторинг

### Для Windows Service:
- Логи находятся в папке `logs/`
- `service-output.log` - стандартный вывод
- `service-error.log` - ошибки

### Для автозагрузки:
- Логи отображаются в консольном окне
- Для скрытого запуска логи не сохраняются

## Устранение проблем

### Приложение не запускается:
1. Проверьте, что Node.js установлен и доступен в PATH
2. Убедитесь, что все зависимости установлены: `npm install`
3. Проверьте переменные окружения в `.env.production`
4. Убедитесь, что база данных доступна

### Порт уже используется:
1. Найдите процесс, использующий порт 3000:
   ```cmd
   netstat -ano | findstr :3000
   ```
2. Завершите процесс:
   ```cmd
   taskkill /PID <PID> /F
   ```

### Служба не запускается:
1. Проверьте логи службы в папке `logs/`
2. Убедитесь, что NSSM установлен корректно
3. Проверьте права доступа к папке проекта

## Рекомендации

- **Для продакшена**: используйте Windows Service (Вариант 1)
- **Для разработки**: используйте автозагрузку (Вариант 2)
- **Регулярно проверяйте логи** на наличие ошибок
- **Настройте мониторинг** доступности сайта
