# Быстрое исправление firewall - запустите от имени администратора
# Скопируйте эти команды в PowerShell (администратор)

Write-Host "🔥 Настройка Windows Firewall..." -ForegroundColor Yellow

# Добавляем правило для порта 8080
netsh advfirewall firewall add rule name="Dogs Website HTTP" dir=in action=allow protocol=TCP localport=8080

# Проверяем результат
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Firewall настроен успешно!" -ForegroundColor Green
} else {
    Write-Host "❌ Ошибка настройки firewall" -ForegroundColor Red
}

# Показываем созданное правило
Write-Host "`n📋 Проверка правила:" -ForegroundColor Cyan
netsh advfirewall firewall show rule name="Dogs Website HTTP"

Write-Host "`n🎯 Теперь проверьте доступность сайта:" -ForegroundColor Yellow
Write-Host "http://ta-shiba.duckdns.org:8080" -ForegroundColor White
