import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { writeFile } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// POST /api/settings/upload - Загрузка логотипа или фавикона (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string;

    if (!file) {
      return NextResponse.json(
        { error: 'Файл не найден' },
        { status: 400 }
      );
    }

    if (!type || !['logo', 'favicon', 'aboutImage'].includes(type)) {
      return NextResponse.json(
        { error: 'Неверный тип файла' },
        { status: 400 }
      );
    }

    // Получаем расширение файла
    const fileExtension = path.extname(file.name);

    // Создаем уникальное имя файла
    const fileName = `${type}_${uuidv4()}${fileExtension}`;

    // Путь для сохранения файла
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');
    const filePath = path.join(uploadDir, fileName);

    // Получаем содержимое файла
    const fileBuffer = await file.arrayBuffer();

    // Сохраняем файл
    await writeFile(filePath, Buffer.from(fileBuffer));

    // URL для доступа к файлу
    const fileUrl = `/uploads/${fileName}`;

    // Получаем ключ настройки в зависимости от типа
    let key = '';
    let description = '';

    switch (type) {
      case 'logo':
        key = 'logo';
        description = 'Логотип сайта';
        break;
      case 'favicon':
        key = 'favicon';
        description = 'Фавикон сайта';
        break;
      case 'aboutImage':
        key = 'aboutImage';
        description = 'Изображение для раздела "О нас"';
        break;
      default:
        key = type;
        description = `Изображение типа ${type}`;
    }

    // Проверяем, существует ли настройка
    const existingSetting = await prisma.settings.findUnique({
      where: { key }
    });

    if (existingSetting) {
      // Обновляем существующую настройку
      await prisma.settings.update({
        where: { key },
        data: { value: fileUrl }
      });
    } else {
      // Создаем новую настройку
      await prisma.settings.create({
        data: {
          key,
          value: fileUrl,
          description
        }
      });
    }

    return NextResponse.json({
      success: true,
      url: fileUrl,
    });
  } catch (error) {
    console.error('Ошибка при загрузке файла:', error);
    return NextResponse.json(
      { error: 'Ошибка при загрузке файла' },
      { status: 500 }
    );
  }
}
