'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminAccess() {
  const [keys, setKeys] = useState<string[]>([]);
  const router = useRouter();
  const secretCode = 'admin';

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Проверяем, что e.key существует
      if (!e.key) return;

      // Добавляем нажатую клавишу в массив
      setKeys((prevKeys) => {
        const newKeys = [...prevKeys, e.key.toLowerCase()];

        // Ограничиваем массив длиной секретного кода
        if (newKeys.length > secretCode.length) {
          newKeys.shift();
        }

        // Проверяем, совпадает ли последовательность с секретным кодом
        const enteredCode = newKeys.join('');
        if (enteredCode === secretCode) {
          router.push('/admin/login');
        }

        return newKeys;
      });
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [router]);

  return null; // Компонент не отображает ничего в DOM
}
