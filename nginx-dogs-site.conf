# Nginx configuration for Dogs Website
# Place this file in nginx/conf.d/ directory
#
# IMPORTANT: Add these directives to your main nginx.conf in the http block:
#
# http {
#     # Rate limiting
#     limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
#     limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
#
#     # Upstream for Next.js application
#     upstream dogs_app {
#         server 127.0.0.1:3000;
#         keepalive 32;
#     }
#
#     # Include this file
#     include conf.d/*.conf;
# }

# HTTP server on port 8080 (your current setup)
server {
    listen 8080;
    server_name localhost ta-shiba.duckdns.org;

    # For now, serve the application directly
    # After SSL setup, uncomment the redirect below:
    # return 301 https://$server_name$request_uri;

    # Temporary direct serving for testing
    location / {
        proxy_pass http://dogs_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Standard HTTP server (port 80) - for SSL certificate validation
server {
    listen 80;
    server_name ta-shiba.duckdns.org;

    # Redirect all HTTP requests to HTTPS (enable after SSL setup)
    # return 301 https://$server_name$request_uri;

    # Temporary redirect to port 8080 for testing
    return 301 http://$server_name:8080$request_uri;
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name ta-shiba.duckdns.org;

    # SSL Configuration (will be updated after getting certificates)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Client max body size (for file uploads)
    client_max_body_size 10M;

    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri @proxy;
    }

    # API routes with rate limiting (uncomment after adding zones to nginx.conf)
    location /api/ {
        # limit_req zone=api burst=20 nodelay;
        proxy_pass http://dogs_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Main application
    location / {
        # limit_req zone=general burst=50 nodelay;
        try_files $uri @proxy;
    }

    # Proxy to Next.js application
    location @proxy {
        proxy_pass http://dogs_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(env|log|sql)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
