import Image from 'next/image';
import Link from 'next/link';
import Layout from '@/components/layout/Layout';
import { prisma } from '@/lib/db';
import { notFound } from 'next/navigation';
import AlbumGallery from './AlbumGallery';
import { PawPrint, Leaf, FloatingElements } from '@/components/ui/Decorations';

// Генерируем метаданные для страницы
export async function generateMetadata({ params }: { params: { slug: string } }) {
  const album = await prisma.gallery.findUnique({
    where: { slug: params.slug },
    include: {
      category: true,
    },
  });

  if (!album) {
    return {
      title: 'Альбом не найден',
      description: 'Запрашиваемый альбом не найден',
    };
  }

  return {
    title: `${album.title} - Галерея - Питомник собак`,
    description: album.description || `Фотоальбом "${album.title}" ${album.category ? `в категории "${album.category.name}"` : ''} - Питомник собак`,
  };
}

export default async function AlbumPage({ params }: { params: { slug: string } }) {
  // Получаем информацию об альбоме
  const album = await prisma.gallery.findUnique({
    where: {
      slug: params.slug,
      isPublished: true,
    },
    include: {
      category: true,
      photos: {
        orderBy: {
          order: 'asc',
        },
      },
    },
  });

  // Если альбом не найден, возвращаем 404
  if (!album) {
    notFound();
  }

  // Получаем другие альбомы из той же категории
  const relatedAlbums = album.category
    ? await prisma.gallery.findMany({
        where: {
          isPublished: true,
          categoryId: album.categoryId,
          id: {
            not: album.id,
          },
        },
        include: {
          photos: {
            where: {
              isMain: true,
            },
            take: 1,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 3,
      })
    : [];

  return (
    <Layout>
      <section className="pt-16 pb-24 bg-forest-bg relative">
        <FloatingElements count={4} type="mixed" className="absolute inset-0 z-0 pointer-events-none" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="mb-8">
            <Link href="/gallery" className="text-forest-dark hover:text-shiba-orange flex items-center transition-colors">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Назад к галерее
            </Link>
          </div>

          <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-12">
            {/* Заголовок альбома */}
            <div className="relative">
              {album.photos.length > 0 ? (
                <div className="relative h-64 md:h-80">
                  <Image
                    src={album.photos[0].url}
                    alt={album.title}
                    fill
                    style={{ objectFit: 'cover' }}
                    priority
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>

                  <div className="absolute bottom-0 left-0 right-0 p-6 md:p-8">
                    {album.category && (
                      <Link
                        href={`/gallery/category/${album.category.slug}`}
                        className="inline-block bg-white/80 text-forest-dark text-sm font-medium px-3 py-1 rounded-full shadow-sm mb-3 hover:bg-white transition-colors"
                      >
                        {album.category.name}
                      </Link>
                    )}
                    <h1 className="text-3xl md:text-4xl font-bold text-white">{album.title}</h1>
                  </div>
                </div>
              ) : album.coverImage ? (
                <div className="relative h-64 md:h-80">
                  <Image
                    src={album.coverImage}
                    alt={album.title}
                    fill
                    style={{ objectFit: 'cover' }}
                    priority
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>

                  <div className="absolute bottom-0 left-0 right-0 p-6 md:p-8">
                    {album.category && (
                      <Link
                        href={`/gallery/category/${album.category.slug}`}
                        className="inline-block bg-white/80 text-forest-dark text-sm font-medium px-3 py-1 rounded-full shadow-sm mb-3 hover:bg-white transition-colors"
                      >
                        {album.category.name}
                      </Link>
                    )}
                    <h1 className="text-3xl md:text-4xl font-bold text-white">{album.title}</h1>
                  </div>
                </div>
              ) : (
                <div className="p-6 md:p-8 bg-forest-bg/10">
                  {album.category && (
                    <Link
                      href={`/gallery/category/${album.category.slug}`}
                      className="inline-block bg-white text-forest-dark text-sm font-medium px-3 py-1 rounded-full shadow-sm mb-3 hover:bg-white transition-colors"
                    >
                      {album.category.name}
                    </Link>
                  )}
                  <h1 className="text-3xl md:text-4xl font-bold text-forest-dark">{album.title}</h1>
                </div>
              )}
            </div>

            {/* Описание альбома */}
            {album.description && (
              <div className="px-6 md:px-8 pt-6">
                <p className="text-forest-medium">{album.description}</p>
              </div>
            )}

            {/* Галерея фотографий */}
            <div className="p-6 md:p-8">
              {album.photos.length > 0 ? (
                <AlbumGallery album={album} photos={album.photos} />
              ) : (
                <div className="bg-forest-bg/10 rounded-xl p-12 text-center">
                  <div className="w-20 h-20 mx-auto bg-forest-bg/20 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-10 h-10 text-forest-light" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-forest-dark mb-2">Фотографии отсутствуют</h3>
                  <p className="text-forest-medium">В этом альбоме пока нет фотографий</p>
                </div>
              )}
            </div>
          </div>

          {/* Похожие альбомы */}
          {relatedAlbums.length > 0 && (
            <div className="mt-16">
              <div className="flex items-center mb-8">
                <div className="h-px flex-grow bg-forest-light/20"></div>
                <div className="flex items-center mx-4">
                  <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
                  </svg>
                  <h2 className="text-2xl font-bold text-forest-dark">Похожие альбомы</h2>
                </div>
                <div className="h-px flex-grow bg-forest-light/20"></div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {relatedAlbums.map((relatedAlbum) => (
                  <Link
                    key={relatedAlbum.id}
                    href={`/gallery/${relatedAlbum.slug}`}
                    className="group"
                  >
                    <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform group-hover:translate-y-[-5px]">
                      <div className="relative h-48">
                        {relatedAlbum.photos.length > 0 ? (
                          <>
                            <Image
                              src={relatedAlbum.photos[0].url}
                              alt={relatedAlbum.title}
                              fill
                              style={{ objectFit: 'cover' }}
                              className="transition-transform duration-500 group-hover:scale-105"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          </>
                        ) : relatedAlbum.coverImage ? (
                          <>
                            <Image
                              src={relatedAlbum.coverImage}
                              alt={relatedAlbum.title}
                              fill
                              style={{ objectFit: 'cover' }}
                              className="transition-transform duration-500 group-hover:scale-105"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          </>
                        ) : (
                          <div className="w-full h-full bg-forest-bg flex items-center justify-center">
                            <div className="text-center">
                              <svg className="w-12 h-12 mx-auto text-forest-light mb-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
                              </svg>
                              <span className="text-forest-medium">Нет фото</span>
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="p-5">
                        <h3 className="text-lg font-bold text-forest-dark mb-2 group-hover:text-shiba-orange transition-colors line-clamp-1">{relatedAlbum.title}</h3>
                        {relatedAlbum.description && (
                          <p className="text-forest-medium mb-3 text-sm line-clamp-2">
                            {relatedAlbum.description.length > 100
                              ? `${relatedAlbum.description.substring(0, 100)}...`
                              : relatedAlbum.description}
                          </p>
                        )}
                        <div className="flex justify-end">
                          <span className="text-forest-dark font-medium text-sm group-hover:text-shiba-orange transition-colors flex items-center">
                            Смотреть альбом
                            <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}

          <div className="absolute left-0 bottom-0 w-32 h-32 opacity-10">
            <PawPrint size="lg" />
          </div>
          <div className="absolute right-10 top-40 w-16 h-16 opacity-10">
            <Leaf size="md" />
          </div>
        </div>
      </section>
    </Layout>
  );
}
