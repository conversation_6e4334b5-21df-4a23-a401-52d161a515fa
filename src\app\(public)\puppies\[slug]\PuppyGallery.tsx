'use client';

import { useState } from 'react';
import Image from 'next/image';
import PhotoViewer from '@/components/ui/PhotoViewer';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface PuppyGalleryProps {
  photos: Photo[];
  name: string;
}

export default function PuppyGallery({ photos, name }: PuppyGalleryProps) {
  const [isPhotoViewerOpen, setIsPhotoViewerOpen] = useState(false);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);

  // Находим главное фото
  const mainPhoto = photos.find(photo => photo.isMain) || photos[0];

  // Остальные фотографии
  const otherPhotos = photos.filter(photo => photo !== mainPhoto);

  const openPhotoViewer = (index: number) => {
    setSelectedPhotoIndex(index);
    setIsPhotoViewerOpen(true);
  };

  const closePhotoViewer = () => {
    setIsPhotoViewerOpen(false);
  };

  return (
    <>
      <div>
        <div className="relative h-96 rounded-lg overflow-hidden cursor-pointer group" onClick={() => openPhotoViewer(0)}>
          {mainPhoto ? (
            <>
              <Image
                src={mainPhoto.url}
                alt={name}
                fill
                style={{ objectFit: 'cover' }}
                priority
                className="transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute bottom-4 right-4 bg-white/80 text-forest-dark px-3 py-1 rounded-full text-sm font-medium shadow-md transform translate-y-2 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-300">
                Просмотреть все фото ({photos.length})
              </div>
              {mainPhoto.description && (
                <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <p className="text-white text-sm">{mainPhoto.description}</p>
                </div>
              )}
            </>
          ) : (
            <div className="w-full h-full bg-forest-bg flex items-center justify-center">
              <div className="text-center">
                <svg className="w-12 h-12 mx-auto text-forest-light mb-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
                </svg>
                <span className="text-forest-medium">Нет фотографий</span>
              </div>
            </div>
          )}
        </div>

        {otherPhotos.length > 0 && (
          <div className="mt-4 grid grid-cols-4 gap-2">
            {otherPhotos.slice(0, 4).map((photo, index) => (
              <div
                key={photo.id}
                className="relative h-24 rounded-lg overflow-hidden cursor-pointer group"
                onClick={() => openPhotoViewer(index + 1)}
              >
                <Image
                  src={photo.url}
                  alt={name}
                  fill
                  style={{ objectFit: 'cover' }}
                  className="transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            ))}
            {otherPhotos.length > 4 && (
              <div
                className="relative h-24 rounded-lg overflow-hidden cursor-pointer bg-shiba-orange flex items-center justify-center transition-all duration-300 hover:bg-shiba-orange-dark"
                onClick={() => openPhotoViewer(5)}
              >
                <span className="text-white font-bold">+{otherPhotos.length - 4}</span>
              </div>
            )}
          </div>
        )}
      </div>

      <PhotoViewer
        photos={photos}
        initialPhotoIndex={selectedPhotoIndex}
        isOpen={isPhotoViewerOpen}
        onClose={closePhotoViewer}
      />
    </>
  );
}
