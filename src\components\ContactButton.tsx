'use client';

import { useState } from 'react';
import Button from '@/components/ui/Button';
import ContactModal from '@/components/ui/ContactModal';

interface ContactButtonProps {
  subjectId: string;
  subjectType: 'dog' | 'puppy';
  subjectName: string;
  buttonText?: string;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
}

export default function ContactButton({
  subjectId,
  subjectType,
  subjectName,
  buttonText = 'Связаться с нами',
  className = 'bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md shadow-md',
  variant = 'primary',
}: ContactButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <>
      <Button
        onClick={openModal}
        className={className}
        variant={variant}
      >
        {buttonText}
      </Button>

      <ContactModal
        isOpen={isModalOpen}
        onClose={closeModal}
        subjectId={subjectId}
        subjectType={subjectType}
        subjectName={subjectName}
      />
    </>
  );
}
