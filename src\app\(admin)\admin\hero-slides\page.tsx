'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Alert from '@/components/ui/Alert';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import FileUpload from '@/components/ui/FileUpload';

interface HeroSlide {
  id: string;
  title?: string;
  subtitle?: string;
  imageUrl: string;
  buttonText?: string;
  buttonLink?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function HeroSlidesPage() {
  const [slides, setSlides] = useState<HeroSlide[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingSlide, setEditingSlide] = useState<HeroSlide | null>(null);

  // Форма для нового/редактируемого слайда
  const [formData, setFormData] = useState({
    title: '',
    subtitle: '',
    buttonText: '',
    buttonLink: '',
    isActive: true,
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Загрузка слайдов
  const fetchSlides = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/hero-slides');
      if (!response.ok) {
        throw new Error('Ошибка при загрузке слайдов');
      }
      const data = await response.json();
      setSlides(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Произошла ошибка');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSlides();
  }, []);

  // Сброс формы
  const resetForm = () => {
    setFormData({
      title: '',
      subtitle: '',
      buttonText: '',
      buttonLink: '',
      isActive: true,
    });
    setSelectedFile(null);
    setEditingSlide(null);
    setShowAddForm(false);
  };

  // Обработка отправки формы
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFile && !editingSlide) {
      setError('Выберите изображение для слайда');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      let imageUrl = editingSlide?.imageUrl || '';

      // Загружаем новое изображение, если выбрано
      if (selectedFile) {
        const uploadFormData = new FormData();
        uploadFormData.append('file', selectedFile);

        const uploadResponse = await fetch('/api/hero-slides/upload', {
          method: 'POST',
          body: uploadFormData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Ошибка при загрузке изображения');
        }

        const uploadResult = await uploadResponse.json();
        imageUrl = uploadResult.url;
      }

      // Создаем или обновляем слайд
      const slideData = {
        ...formData,
        imageUrl,
      };

      const url = editingSlide 
        ? `/api/hero-slides/${editingSlide.id}`
        : '/api/hero-slides';
      
      const method = editingSlide ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(slideData),
      });

      if (!response.ok) {
        throw new Error(editingSlide ? 'Ошибка при обновлении слайда' : 'Ошибка при создании слайда');
      }

      setSuccess(editingSlide ? 'Слайд успешно обновлен' : 'Слайд успешно создан');
      resetForm();
      fetchSlides();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Произошла ошибка');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Удаление слайда
  const handleDelete = async (slideId: string) => {
    if (!confirm('Вы уверены, что хотите удалить этот слайд?')) {
      return;
    }

    try {
      const response = await fetch(`/api/hero-slides/${slideId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Ошибка при удалении слайда');
      }

      setSuccess('Слайд успешно удален');
      fetchSlides();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Произошла ошибка');
    }
  };

  // Редактирование слайда
  const handleEdit = (slide: HeroSlide) => {
    setEditingSlide(slide);
    setFormData({
      title: slide.title || '',
      subtitle: slide.subtitle || '',
      buttonText: slide.buttonText || '',
      buttonLink: slide.buttonLink || '',
      isActive: slide.isActive,
    });
    setShowAddForm(true);
  };

  // Переключение активности слайда
  const toggleActive = async (slideId: string, isActive: boolean) => {
    try {
      const slide = slides.find(s => s.id === slideId);
      if (!slide) return;

      const response = await fetch(`/api/hero-slides/${slideId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...slide,
          isActive: !isActive,
        }),
      });

      if (!response.ok) {
        throw new Error('Ошибка при обновлении статуса слайда');
      }

      fetchSlides();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Произошла ошибка');
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Hero-слайды">
      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Hero-слайды</h1>
          <p className="text-gray-500">
            Управление слайдами для главной страницы. Всего слайдов: {slides.length}
          </p>
        </div>
        <Button 
          onClick={() => setShowAddForm(true)}
          disabled={showAddForm}
        >
          Добавить слайд
        </Button>
      </div>

      {/* Форма добавления/редактирования */}
      {showAddForm && (
        <div className="bg-white shadow-md rounded-lg p-6 mb-6">
          <h2 className="text-lg font-medium mb-4">
            {editingSlide ? 'Редактировать слайд' : 'Добавить новый слайд'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Заголовок (необязательно)"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="Введите заголовок слайда"
                fullWidth
              />
              
              <Input
                label="Подзаголовок (необязательно)"
                value={formData.subtitle}
                onChange={(e) => setFormData({ ...formData, subtitle: e.target.value })}
                placeholder="Введите подзаголовок слайда"
                fullWidth
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Текст кнопки (необязательно)"
                value={formData.buttonText}
                onChange={(e) => setFormData({ ...formData, buttonText: e.target.value })}
                placeholder="Например: Смотреть собак"
                fullWidth
              />
              
              <Input
                label="Ссылка кнопки (необязательно)"
                value={formData.buttonLink}
                onChange={(e) => setFormData({ ...formData, buttonLink: e.target.value })}
                placeholder="Например: /dogs"
                fullWidth
              />
            </div>

            <div>
              <FileUpload
                label={editingSlide ? "Изменить изображение (необязательно)" : "Изображение слайда"}
                accept="image/*"
                onChange={(files) => setSelectedFile(files[0] || null)}
                fullWidth
              />
              {editingSlide && (
                <p className="text-sm text-gray-500 mt-1">
                  Текущее изображение: {editingSlide.imageUrl}
                </p>
              )}
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                Активный слайд
              </label>
            </div>

            <div className="flex space-x-4">
              <Button type="submit" isLoading={isSubmitting}>
                {editingSlide ? 'Обновить слайд' : 'Создать слайд'}
              </Button>
              <Button 
                type="button" 
                variant="secondary" 
                onClick={resetForm}
                disabled={isSubmitting}
              >
                Отмена
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Список слайдов */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        {slides.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500">Слайды не найдены</p>
            <p className="text-sm text-gray-400 mt-2">
              Добавьте первый слайд для hero-секции
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
            {slides.map((slide) => (
              <div key={slide.id} className="border rounded-lg overflow-hidden">
                <div className="relative h-48">
                  <img
                    src={slide.imageUrl}
                    alt={slide.title || 'Hero slide'}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-2 right-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      slide.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {slide.isActive ? 'Активен' : 'Неактивен'}
                    </span>
                  </div>
                </div>
                
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 mb-2">
                    {slide.title || 'Без заголовка'}
                  </h3>
                  {slide.subtitle && (
                    <p className="text-sm text-gray-600 mb-2">{slide.subtitle}</p>
                  )}
                  {slide.buttonText && (
                    <p className="text-sm text-blue-600 mb-3">
                      Кнопка: {slide.buttonText}
                    </p>
                  )}
                  
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => handleEdit(slide)}
                    >
                      Редактировать
                    </Button>
                    <Button
                      size="sm"
                      variant={slide.isActive ? "secondary" : "primary"}
                      onClick={() => toggleActive(slide.id, slide.isActive)}
                    >
                      {slide.isActive ? 'Деактивировать' : 'Активировать'}
                    </Button>
                    <Button
                      size="sm"
                      variant="danger"
                      onClick={() => handleDelete(slide.id)}
                    >
                      Удалить
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
