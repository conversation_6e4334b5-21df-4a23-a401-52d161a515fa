'use client';

import { useState } from 'react';
import Image from 'next/image';
import PhotoViewer from '@/components/ui/PhotoViewer';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface Album {
  id: string;
  title: string;
  description: string | null;
}

interface AlbumGalleryProps {
  album: Album;
  photos: Photo[];
}

export default function AlbumGallery({ album, photos }: AlbumGalleryProps) {
  const [isPhotoViewerOpen, setIsPhotoViewerOpen] = useState(false);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);

  const openPhotoViewer = (index: number) => {
    setSelectedPhotoIndex(index);
    setIsPhotoViewerOpen(true);
  };

  const closePhotoViewer = () => {
    setIsPhotoViewerOpen(false);
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {photos.map((photo, index) => (
          <div
            key={photo.id}
            className="relative aspect-square rounded-lg overflow-hidden group cursor-pointer"
            onClick={() => openPhotoViewer(index)}
          >
            <Image
              src={photo.url}
              alt={photo.title || album.title}
              fill
              style={{ objectFit: 'cover' }}
              className="transition-transform duration-300 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
              {photo.title && (
                <div className="p-4 w-full">
                  <p className="text-white font-medium">{photo.title}</p>
                  {photo.description && (
                    <p className="text-white text-sm">{photo.description}</p>
                  )}
                </div>
              )}
            </div>
            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="bg-white/80 rounded-full p-2">
                <svg className="w-6 h-6 text-forest-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                </svg>
              </div>
            </div>
          </div>
        ))}
      </div>

      <PhotoViewer
        photos={photos}
        initialPhotoIndex={selectedPhotoIndex}
        isOpen={isPhotoViewerOpen}
        onClose={closePhotoViewer}
      />
    </>
  );
}
