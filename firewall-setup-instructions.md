# Инструкция по настройке брандмауэра для Dogs Website

## Автоматическая настройка (Рекомендуется)

### Запуск скрипта настройки:

1. Откройте PowerShell от имени администратора
2. Выполните команду:
   ```powershell
   .\setup-firewall.ps1
   ```

Скрипт автоматически создаст правила для следующих портов:
- **Порт 80 (HTTP)** - для перенаправления на HTTPS и проверки SSL сертификатов
- **Порт 443 (HTTPS)** - для безопасного веб-трафика
- **Порт 3000 (Next.js)** - для прямого доступа к приложению

## Ручная настройка

### Через графический интерфейс:

1. Откройте "Брандмауэр Защитника Windows в режиме повышенной безопасности"
2. Выберите "Правила для входящих подключений"
3. Нажмите "Создать правило..."

Для каждого порта создайте правило:

#### Порт 80 (HTTP):
- Тип правила: Порт
- Протокол: TCP
- Порт: 80
- Действие: Разрешить подключение
- Профиль: Все профили
- Имя: "Dogs Website - HTTP (Port 80)"

#### Порт 443 (HTTPS):
- Тип правила: Порт
- Протокол: TCP
- Порт: 443
- Действие: Разрешить подключение
- Профиль: Все профили
- Имя: "Dogs Website - HTTPS (Port 443)"

#### Порт 3000 (Next.js):
- Тип правила: Порт
- Протокол: TCP
- Порт: 3000
- Действие: Разрешить подключение
- Профиль: Все профили
- Имя: "Dogs Website - Next.js (Port 3000)"

### Через командную строку:

```cmd
# Запустите командную строку от имени администратора

# HTTP (Port 80)
netsh advfirewall firewall add rule name="Dogs Website - HTTP (Port 80)" dir=in action=allow protocol=TCP localport=80

# HTTPS (Port 443)
netsh advfirewall firewall add rule name="Dogs Website - HTTPS (Port 443)" dir=in action=allow protocol=TCP localport=443

# Next.js (Port 3000)
netsh advfirewall firewall add rule name="Dogs Website - Next.js (Port 3000)" dir=in action=allow protocol=TCP localport=3000
```

## Настройка роутера

### Проброс портов (Port Forwarding):

1. Войдите в веб-интерфейс вашего роутера (обычно *********** или ***********)
2. Найдите раздел "Port Forwarding" или "Virtual Servers"
3. Создайте правила для портов:

| Внешний порт | Внутренний порт | IP адрес | Протокол | Описание |
|--------------|-----------------|----------|----------|----------|
| 80           | 80              | 192.168.x.x | TCP    | HTTP     |
| 443          | 443             | 192.168.x.x | TCP    | HTTPS    |

Где `192.168.x.x` - локальный IP адрес вашего компьютера.

### Определение локального IP:

```cmd
ipconfig | findstr "IPv4"
```

## Проверка настроек

### Тестирование портов:

```powershell
# Локальное тестирование
.\test-ports.ps1

# Тестирование внешнего доступа
.\test-ports.ps1 -Domain "yourdomain.duckdns.org" -External
```

### Проверка правил брандмауэра:

```powershell
Get-NetFirewallRule | Where-Object { $_.DisplayName -like "Dogs Website*" }
```

### Проверка открытых портов:

```cmd
netstat -an | findstr ":80\|:443\|:3000"
```

## Устранение проблем

### Порт заблокирован провайдером:

Некоторые интернет-провайдеры блокируют порты 80 и 443. Решения:
1. Обратитесь к провайдеру для разблокировки портов
2. Используйте альтернативные порты (например, 8080, 8443)
3. Используйте VPN или туннелирование

### Проверка блокировки провайдером:

```powershell
# Проверка доступности портов извне
Test-NetConnection -ComputerName "yourdomain.duckdns.org" -Port 80
Test-NetConnection -ComputerName "yourdomain.duckdns.org" -Port 443
```

### Антивирус блокирует соединения:

1. Добавьте исключения для nginx.exe и node.exe в антивирус
2. Временно отключите антивирус для тестирования
3. Проверьте настройки сетевого экрана антивируса

### Конфликт с другими службами:

```cmd
# Найти процесс, использующий порт
netstat -ano | findstr :80
netstat -ano | findstr :443

# Завершить процесс (замените PID на реальный)
taskkill /PID <PID> /F
```

## Безопасность

### Рекомендации:

1. **Не открывайте порт 3000 для внешнего доступа** в продакшене
2. **Используйте только HTTPS** для внешнего доступа
3. **Регулярно обновляйте** nginx и Node.js
4. **Мониторьте логи** на предмет подозрительной активности
5. **Используйте fail2ban** или аналогичные инструменты для защиты от атак

### Дополнительная защита:

```nginx
# Добавьте в nginx конфигурацию
# Ограничение скорости запросов
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

# Блокировка подозрительных User-Agent
if ($http_user_agent ~* (bot|crawler|spider)) {
    return 403;
}
```

## Мониторинг

### Логи брандмауэра:

1. Включите логирование в брандмауэре Windows
2. Просматривайте логи в Event Viewer
3. Анализируйте заблокированные подключения

### Мониторинг портов:

Создайте задачу в планировщике для регулярной проверки:
```powershell
# Ежедневная проверка в 9:00
.\test-ports.ps1 -Domain "yourdomain.duckdns.org" -External
```
