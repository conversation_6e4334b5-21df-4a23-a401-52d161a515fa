/**
 * Преобразует строку в slug (URL-совместимую строку)
 * @param text Исходная строка
 * @returns Slug
 */
export function slugify(text: string): string {
  // Транслитерация кириллицы
  const translitMap: Record<string, string> = {
    'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'e',
    'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
    'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
    'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '',
    'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya',
    'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'E',
    'Ж': 'Zh', 'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M',
    'Н': 'N', 'О': 'O', 'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U',
    'Ф': 'F', 'Х': 'H', 'Ц': 'Ts', 'Ч': 'Ch', 'Ш': 'Sh', 'Щ': 'Sch', 'Ъ': '',
    'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'Yu', 'Я': 'Ya'
  };

  // Транслитерация
  const transliterated = text
    .split('')
    .map(char => translitMap[char] !== undefined ? translitMap[char] : char)
    .join('');

  // Преобразование в slug
  return transliterated
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w\-]+/g, '')
    .replace(/\-\-+/g, '-')
    .replace(/^-+|-+$/g, ''); // Удаляем начальные и конечные дефисы
}

/**
 * Форматирует дату в локализованную строку
 * @param date Дата
 * @param locale Локаль (по умолчанию 'ru-RU')
 * @returns Отформатированная дата
 */
export function formatDate(date: Date | string): string {
  const dateObj = date instanceof Date ? date : new Date(date);
  return dateObj.toLocaleDateString('ru-RU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
}

/**
 * Форматирует дату в полную локализованную строку
 * @param date Дата
 * @param locale Локаль (по умолчанию 'ru-RU')
 * @returns Отформатированная дата
 */
export function formatFullDate(date: Date | string): string {
  const dateObj = date instanceof Date ? date : new Date(date);
  return dateObj.toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Форматирует цену в локализованную строку
 * @param price Цена
 * @param locale Локаль (по умолчанию 'ru-RU')
 * @param currency Валюта (по умолчанию 'RUB')
 * @returns Отформатированная цена
 */
export function formatPrice(price: number | null): string {
  if (price === null) return '-';

  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: 'RUB',
    minimumFractionDigits: 0,
  }).format(price);
}

/**
 * Обрезает текст до указанной длины и добавляет многоточие
 * @param text Исходный текст
 * @param maxLength Максимальная длина
 * @returns Обрезанный текст
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

/**
 * Получает текстовое представление пола
 * @param gender Пол ('MALE' или 'FEMALE')
 * @returns Текстовое представление пола
 */
export function getGenderText(gender: 'MALE' | 'FEMALE'): string {
  return gender === 'MALE' ? 'Кобель' : 'Сука';
}

/**
 * Получает текстовое представление статуса щенка
 * @param status Статус щенка ('AVAILABLE', 'RESERVED', 'SOLD')
 * @returns Текстовое представление статуса
 */
export function getPuppyStatusText(status: string): string {
  switch (status) {
    case 'AVAILABLE':
      return 'Доступен';
    case 'RESERVED':
      return 'Зарезервирован';
    case 'SOLD':
      return 'Продан';
    default:
      return 'Неизвестно';
  }
}

/**
 * Получает текстовое представление статуса заявки
 * @param status Статус заявки ('NEW', 'IN_PROGRESS', 'COMPLETED', 'REJECTED')
 * @returns Текстовое представление статуса
 */
export function getInquiryStatusText(status: string): string {
  switch (status) {
    case 'NEW':
      return 'Новая';
    case 'IN_PROGRESS':
      return 'В обработке';
    case 'COMPLETED':
      return 'Завершена';
    case 'REJECTED':
      return 'Отклонена';
    default:
      return 'Неизвестно';
  }
}
