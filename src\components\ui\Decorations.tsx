'use client';

import { useEffect, useState } from 'react';

interface PawPrintProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

export function PawPrint({ className = '', size = 'md', color = 'var(--shiba-orange)' }: PawPrintProps) {
  const sizeMap = {
    sm: 'w-6 h-6',
    md: 'w-10 h-10',
    lg: 'w-16 h-16',
  };

  return (
    <svg 
      className={`${sizeMap[size]} ${className}`} 
      viewBox="0 0 512 512" 
      fill={color}
    >
      <path d="M226.5 92.9c14.3 42.9-.3 86.2-32.6 96.8s-70.1-15.6-84.4-58.5.3-86.2 32.6-96.8S212.2 50 226.5 92.9zM100.4 198.6c18.9 32.4 14.3 70.1-10.2 84.1s-59.7-.9-78.5-33.3S-2.7 179.3 21.8 165.3s59.7.9 78.5 33.3zM69.2 401.2C121.6 259.9 214.7 224 256 224s134.4 35.9 186.8 177.2c3.6 9.7 5.2 20.1 5.2 30.5v1.6c0 25.8-20.9 46.7-46.7 46.7c-11.5 0-22.9-1.4-34-4.2l-88-22c-15.3-3.8-31.3-3.8-46.6 0l-88 22c-11.1 2.8-22.5 4.2-34 4.2C84.9 480 64 459.1 64 433.3v-1.6c0-10.4 1.6-20.8 5.2-30.5zM421.8 282.7c-24.5-14-29.1-51.7-10.2-84.1s54-47.3 78.5-33.3s29.1 51.7 10.2 84.1s-54 47.3-78.5 33.3zM310.1 189.7c-32.3-10.6-46.9-53.9-32.6-96.8s52.1-69.1 84.4-58.5s46.9 53.9 32.6 96.8s-52.1 69.1-84.4 58.5z"/>
    </svg>
  );
}

interface LeafProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

export function Leaf({ className = '', size = 'md', color = 'var(--forest-medium)' }: LeafProps) {
  const sizeMap = {
    sm: 'w-6 h-6',
    md: 'w-10 h-10',
    lg: 'w-16 h-16',
  };

  return (
    <svg 
      className={`${sizeMap[size]} ${className}`} 
      viewBox="0 0 512 512" 
      fill={color}
    >
      <path d="M272 96c-78.6 0-145.1 51.5-167.7 122.5c33.6-17 71.5-26.5 111.7-26.5h88c8.8 0 16 7.2 16 16s-7.2 16-16 16H288 216s0 0 0 0c-16.6 0-32.7 1.9-48.3 5.4c-25.9 5.9-49.9 16.4-71.4 30.7c0 0 0 0 0 0C38.3 298.8 0 364.9 0 440v16c0 13.3 10.7 24 24 24s24-10.7 24-24V440c0-48.7 20.7-92.5 53.8-123.2C121.6 392.3 190.3 448 272 448l1 0c132.1-.7 239-130.9 239-291.4c0-42.6-7.5-83.1-21.1-119.6c-2.6-6.9-12.7-6.6-16.2-.1C455.9 72.1 418.7 96 376 96L272 96z"/>
    </svg>
  );
}

interface FloatingElementsProps {
  count?: number;
  type?: 'paw' | 'leaf' | 'mixed';
  className?: string;
}

export function FloatingElements({ count = 10, type = 'mixed', className = '' }: FloatingElementsProps) {
  const [elements, setElements] = useState<React.ReactNode[]>([]);

  useEffect(() => {
    const newElements = [];
    for (let i = 0; i < count; i++) {
      const isLeaf = type === 'leaf' || (type === 'mixed' && Math.random() > 0.5);
      const size = Math.random() > 0.7 ? 'lg' : Math.random() > 0.4 ? 'md' : 'sm';
      const opacity = Math.random() * 0.5 + 0.1; // от 0.1 до 0.6
      const left = `${Math.random() * 100}%`;
      const top = `${Math.random() * 100}%`;
      const rotate = `rotate(${Math.random() * 360}deg)`;
      const delay = `${Math.random() * 5}s`;
      const duration = `${Math.random() * 10 + 15}s`; // от 15 до 25 секунд
      
      const style = {
        position: 'absolute' as const,
        left,
        top,
        transform: rotate,
        opacity,
        animation: `float ${duration} ease-in-out ${delay} infinite alternate`,
      };
      
      if (isLeaf) {
        const color = Math.random() > 0.5 ? 'var(--forest-medium)' : 'var(--forest-light)';
        newElements.push(
          <div key={`leaf-${i}`} style={style}>
            <Leaf size={size as 'sm' | 'md' | 'lg'} color={color} />
          </div>
        );
      } else {
        const color = Math.random() > 0.5 ? 'var(--shiba-orange)' : 'var(--shiba-orange-light)';
        newElements.push(
          <div key={`paw-${i}`} style={style}>
            <PawPrint size={size as 'sm' | 'md' | 'lg'} color={color} />
          </div>
        );
      }
    }
    setElements(newElements);
  }, [count, type]);

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <style jsx global>{`
        @keyframes float {
          0% {
            transform: translateY(0) rotate(0deg);
          }
          50% {
            transform: translateY(-20px) rotate(10deg);
          }
          100% {
            transform: translateY(0) rotate(0deg);
          }
        }
      `}</style>
      {elements}
    </div>
  );
}

interface SectionDividerProps {
  type?: 'wave' | 'curve' | 'triangle';
  position?: 'top' | 'bottom';
  color?: string;
  className?: string;
}

export function SectionDivider({ 
  type = 'wave', 
  position = 'bottom', 
  color = 'var(--neutral-white)', 
  className = '' 
}: SectionDividerProps) {
  const getPath = () => {
    if (type === 'wave') {
      return position === 'top' 
        ? 'M0,0 L0,40 C250,110 350,0 500,40 L500,0 Z' 
        : 'M0,100 L0,60 C250,-10 350,100 500,60 L500,100 Z';
    } else if (type === 'curve') {
      return position === 'top' 
        ? 'M0,0 L0,40 C125,100 375,100 500,40 L500,0 Z' 
        : 'M0,100 L0,60 C125,0 375,0 500,60 L500,100 Z';
    } else {
      return position === 'top' 
        ? 'M0,0 L0,40 L250,100 L500,40 L500,0 Z' 
        : 'M0,100 L0,60 L250,0 L500,60 L500,100 Z';
    }
  };

  return (
    <div className={`absolute left-0 right-0 ${position === 'top' ? 'top-0' : 'bottom-0'} w-full overflow-hidden leading-0 ${className}`} style={{ height: '50px' }}>
      <svg
        className="relative block w-full h-full"
        viewBox="0 0 500 100"
        preserveAspectRatio="none"
      >
        <path
          d={getPath()}
          style={{ fill: color }}
        ></path>
      </svg>
    </div>
  );
}
