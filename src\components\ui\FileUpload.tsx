'use client';

import { ChangeEvent, forwardRef, useRef, useState } from 'react';
import Button from './Button';

interface FileUploadProps {
  label?: string;
  error?: string;
  accept?: string;
  multiple?: boolean;
  onChange?: (files: File[]) => void;
  fullWidth?: boolean;
  className?: string;
  buttonText?: string;
  showPreview?: boolean;
}

const FileUpload = forwardRef<HTMLInputElement, FileUploadProps>(
  ({
    label,
    error,
    accept = 'image/*',
    multiple = false,
    onChange,
    fullWidth = false,
    className = '',
    buttonText = 'Выбрать файл',
    showPreview = true,
  }, ref) => {
    const [files, setFiles] = useState<File[]>([]);
    const [previews, setPreviews] = useState<string[]>([]);
    const fileInputRef = useRef<HTMLInputElement>(null);
    
    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
      if (!e.target.files?.length) return;
      
      const selectedFiles = Array.from(e.target.files);
      setFiles(selectedFiles);
      
      if (showPreview) {
        const newPreviews = selectedFiles.map((file) => URL.createObjectURL(file));
        setPreviews(newPreviews);
      }
      
      if (onChange) {
        onChange(selectedFiles);
      }
    };
    
    const handleButtonClick = () => {
      fileInputRef.current?.click();
    };
    
    const handleRemoveFile = (index: number) => {
      const newFiles = [...files];
      newFiles.splice(index, 1);
      setFiles(newFiles);
      
      if (showPreview) {
        const newPreviews = [...previews];
        URL.revokeObjectURL(newPreviews[index]);
        newPreviews.splice(index, 1);
        setPreviews(newPreviews);
      }
      
      if (onChange) {
        onChange(newFiles);
      }
    };
    
    const widthClasses = fullWidth ? 'w-full' : '';
    
    return (
      <div className={`${widthClasses} ${className}`}>
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}
        <div className="flex flex-col space-y-2">
          <input
            ref={(node) => {
              if (typeof ref === 'function') {
                ref(node);
              } else if (ref) {
                ref.current = node;
              }
              fileInputRef.current = node;
            }}
            type="file"
            accept={accept}
            multiple={multiple}
            onChange={handleFileChange}
            className="hidden"
          />
          <Button
            type="button"
            variant="outline"
            onClick={handleButtonClick}
            fullWidth={fullWidth}
          >
            {buttonText}
          </Button>
          
          {files.length > 0 && (
            <div className="mt-2">
              <p className="text-sm text-gray-500 mb-2">
                {files.length} {files.length === 1 ? 'файл выбран' : 'файлов выбрано'}
              </p>
              {showPreview && (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 mt-2">
                  {previews.map((preview, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={preview}
                        alt={`Preview ${index + 1}`}
                        className="h-24 w-24 object-cover rounded-md"
                      />
                      <button
                        type="button"
                        onClick={() => handleRemoveFile(index)}
                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
        {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
      </div>
    );
  }
);

FileUpload.displayName = 'FileUpload';

export default FileUpload;
