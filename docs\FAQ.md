# ❓ Часто задаваемые вопросы (FAQ)

## 🚀 Установка и настройка

### В: Какие системные требования для запуска сайта?
**О:** Минимальные требования:
- Node.js 18+
- PostgreSQL 13+
- 1GB RAM (рекомендуется 2GB+)
- 20GB свободного места на диске

### В: Можно ли использовать другую базу данных вместо PostgreSQL?
**О:** Да, Prisma поддерживает MySQL, SQLite и другие базы данных. Измените `provider` в `schema.prisma` и обновите `DATABASE_URL`.

### В: Как сменить порт по умолчанию?
**О:** Добавьте переменную окружения `PORT=4000` или измените в `package.json` скрипт `dev`.

### В: Ошибка "Module not found" при запуске
**О:** Выполните:
```bash
rm -rf node_modules package-lock.json
npm install
npx prisma generate
```

## 🔐 Аутентификация и безопасность

### В: Как сбросить пароль администратора?
**О:** Выполните в консоли базы данных:
```sql
UPDATE "User" SET password = '$2b$10$hashedpassword' WHERE email = '<EMAIL>';
```
Или пересоздайте пользователя через seed скрипт.

### В: Можно ли добавить двухфакторную аутентификацию?
**О:** В текущей версии не реализована, но можно добавить через NextAuth.js провайдеры.

### В: Как ограничить доступ к админке по IP?
**О:** Добавьте проверку IP в middleware или настройте на уровне веб-сервера (Nginx).

### В: Безопасно ли хранить файлы локально?
**О:** Для production рекомендуется использовать облачные хранилища (AWS S3, Cloudinary).

## 🗄️ База данных

### В: Как сделать резервную копию базы данных?
**О:** Для PostgreSQL:
```bash
pg_dump -h localhost -U username database_name > backup.sql
```

### В: Можно ли изменить структуру базы данных?
**О:** Да, измените `schema.prisma` и выполните:
```bash
npx prisma migrate dev --name your_migration_name
```

### В: Как очистить все данные и начать заново?
**О:** 
```bash
npx prisma migrate reset
npx prisma db seed
```

### В: Ошибка "Table doesn't exist" после миграции
**О:** Убедитесь, что миграции применены:
```bash
npx prisma migrate deploy
npx prisma generate
```

## 📸 Загрузка и управление файлами

### В: Какие форматы изображений поддерживаются?
**О:** JPG, PNG, WebP. Максимальный размер по умолчанию - 10MB.

### В: Как изменить максимальный размер загружаемых файлов?
**О:** Измените переменную окружения `MAX_FILE_SIZE` (в байтах).

### В: Где хранятся загруженные изображения?
**О:** В папке `public/uploads/` с подпапками по типам файлов.

### В: Можно ли использовать внешнее хранилище для изображений?
**О:** Да, можно интегрировать AWS S3, Cloudinary или другие сервисы.

### В: Изображения не отображаются после деплоя
**О:** Проверьте права доступа к папке uploads и настройки веб-сервера для статических файлов.

## 🎨 Дизайн и кастомизация

### В: Как изменить цветовую схему сайта?
**О:** Измените CSS переменные в `globals.css`:
```css
:root {
  --forest-dark: #your-color;
  --shiba-orange: #your-color;
}
```

### В: Можно ли добавить новые страницы?
**О:** Да, создайте новые файлы в папке `src/app/` следуя структуре App Router.

### В: Как изменить логотип в шапке?
**О:** Загрузите новый логотип через Админка → Настройки → Логотип.

### В: Можно ли изменить шрифты?
**О:** Да, измените импорты шрифтов в `layout.tsx` и обновите CSS классы.

## 📱 Мобильная версия

### В: Адаптивен ли сайт для мобильных устройств?
**О:** Да, сайт полностью адаптивен и оптимизирован для всех размеров экранов.

### В: Можно ли создать мобильное приложение?
**О:** Можно использовать технологии PWA или создать React Native приложение.

### В: Почему медленно загружается на мобильных?
**О:** Проверьте размеры изображений и используйте оптимизацию Next.js Image.

## 🔧 Разработка и кастомизация

### В: Как добавить новое поле в модель собаки?
**О:** 
1. Обновите модель в `schema.prisma`
2. Создайте миграцию: `npx prisma migrate dev`
3. Обновите формы и компоненты

### В: Можно ли добавить систему комментариев?
**О:** Да, создайте модель Comment и связи с News/User в Prisma схеме.

### В: Как добавить новый тип пользователя?
**О:** Добавьте новую роль в enum Role в `schema.prisma` и обновите логику авторизации.

### В: Можно ли интегрировать платежную систему?
**О:** Да, можно интегрировать Stripe, PayPal или российские системы (ЮKassa, Сбербанк).

## 🚀 Производительность

### В: Сайт медленно загружается, что делать?
**О:** 
- Оптимизируйте изображения
- Включите кэширование
- Используйте CDN
- Проверьте производительность базы данных

### В: Как включить кэширование?
**О:** Next.js автоматически кэширует статические страницы. Для API используйте заголовки Cache-Control.

### В: Можно ли использовать Redis для кэширования?
**О:** Да, можно добавить Redis для кэширования сессий и данных.

## 📊 SEO и аналитика

### В: Как настроить Google Analytics?
**О:** Добавьте Google Analytics код в `layout.tsx` или используйте библиотеку `@next/third-parties`.

### В: Как улучшить SEO?
**О:** 
- Заполните meta теги в настройках
- Добавьте sitemap.xml
- Используйте семантическую разметку
- Оптимизируйте изображения

### В: Как добавить sitemap?
**О:** Создайте `app/sitemap.ts` с динамической генерацией URL страниц.

## 🔄 Обновления и миграции

### В: Как обновить Next.js до новой версии?
**О:** 
```bash
npm update next react react-dom
npm audit fix
```
Проверьте breaking changes в документации Next.js.

### В: Как перенести данные со старого сайта?
**О:** Создайте скрипт миграции для импорта данных в новую структуру базы данных.

### В: Можно ли откатить миграцию базы данных?
**О:** Prisma не поддерживает автоматический откат. Создайте новую миграцию для отмены изменений.

## 🌐 Многоязычность

### В: Можно ли добавить поддержку английского языка?
**О:** Да, используйте библиотеку `next-intl` или `react-i18next` для интернационализации.

### В: Как изменить язык интерфейса?
**О:** В текущей версии интерфейс на русском языке. Для изменения нужно обновить все текстовые строки.

## 🆘 Решение проблем

### В: Ошибка "Cannot connect to database"
**О:** 
- Проверьте DATABASE_URL
- Убедитесь, что PostgreSQL запущен
- Проверьте сетевые настройки

### В: Ошибка 500 на production
**О:** 
- Проверьте логи сервера
- Убедитесь, что все переменные окружения настроены
- Проверьте права доступа к файлам

### В: Не работает загрузка файлов
**О:** 
- Проверьте права доступа к папке uploads
- Убедитесь в наличии свободного места
- Проверьте настройки веб-сервера

### В: Ошибка "Module not found" в production
**О:** 
- Убедитесь, что все зависимости в dependencies, а не devDependencies
- Проверьте правильность путей импорта

## 📞 Техническая поддержка

### В: Где получить помощь?
**О:** 
- Изучите документацию в папке `docs/`
- Создайте Issue в репозитории
- Обратитесь к разработчику

### В: Как сообщить об ошибке?
**О:** 
- Опишите шаги для воспроизведения
- Приложите скриншоты
- Укажите версию браузера и ОС
- Добавьте логи ошибок

### В: Можно ли заказать доработку функций?
**О:** Да, обратитесь к разработчику для обсуждения технического задания и стоимости.

---

**Не нашли ответ на свой вопрос? Создайте Issue в репозитории или обратитесь к технической поддержке! 🤝**
