import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// PUT /api/hero-slides/[id] - Обновление hero-слайда (защищенный маршрут)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const { id } = params;
    const data = await request.json();

    // Проверяем, существует ли слайд
    const existingSlide = await prisma.heroSlide.findUnique({
      where: { id }
    });

    if (!existingSlide) {
      return NextResponse.json(
        { error: 'Слайд не найден' },
        { status: 404 }
      );
    }

    // Обновляем слайд
    const updatedSlide = await prisma.heroSlide.update({
      where: { id },
      data: {
        title: data.title || null,
        subtitle: data.subtitle || null,
        imageUrl: data.imageUrl,
        buttonText: data.buttonText || null,
        buttonLink: data.buttonLink || null,
        isActive: data.isActive ?? true,
      }
    });

    return NextResponse.json(updatedSlide);
  } catch (error) {
    console.error('Ошибка при обновлении hero-слайда:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении hero-слайда' },
      { status: 500 }
    );
  }
}

// DELETE /api/hero-slides/[id] - Удаление hero-слайда (защищенный маршрут)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const { id } = params;

    // Проверяем, существует ли слайд
    const existingSlide = await prisma.heroSlide.findUnique({
      where: { id }
    });

    if (!existingSlide) {
      return NextResponse.json(
        { error: 'Слайд не найден' },
        { status: 404 }
      );
    }

    // Удаляем слайд
    await prisma.heroSlide.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Слайд успешно удален' });
  } catch (error) {
    console.error('Ошибка при удалении hero-слайда:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении hero-слайда' },
      { status: 500 }
    );
  }
}
