import Layout from '@/components/layout/Layout';
import { prisma } from '@/lib/db';
import { FloatingElements } from '@/components/ui/Decorations';
import PuppiesClient from './PuppiesClient';

export const metadata = {
  title: 'Щенки на продажу - Питомник собак',
  description: 'Щенки на продажу от нашего питомника. Все щенки имеют документы и прививки по возрасту.',
};

export default async function PuppiesPage() {
  // Получаем список щенков
  const puppies = await prisma.puppy.findMany({
    where: {
      isPublished: true,
      status: {
        in: ['AVAILABLE', 'RESERVED'],
      },
    },
    include: {
      photos: true,
      breeding: {
        include: {
          father: true,
          mother: true,
        },
      },
    },
    orderBy: [
      {
        status: 'asc', // Сначала доступные, потом зарезервированные
      },
      {
        birthDate: 'desc', // От новых к старым
      },
    ],
  });

  // Получаем настройки сайта
  const settings = await prisma.settings.findFirst();

  return (
    <Layout>
      <FloatingElements count={6} type="mixed" className="absolute inset-0 z-0 pointer-events-none" />
      <PuppiesClient puppies={puppies} settings={settings} />
    </Layout>
  );
}
