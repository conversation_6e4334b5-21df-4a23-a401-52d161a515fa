# Comprehensive System Test for Dogs Website Deployment
# Tests all components: Next.js app, nginx, SSL, DuckDNS, firewall

param(
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [string]$NginxPath = "C:\nginx",
    [switch]$SkipSSL,
    [switch]$Verbose
)

$ErrorActionPreference = "Continue"
$TestResults = @()

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Message = "",
        [string]$Details = ""
    )
    
    $Result = @{
        Test = $TestName
        Success = $Success
        Message = $Message
        Details = $Details
        Timestamp = Get-Date
    }
    
    $script:TestResults += $Result
    
    $Color = if ($Success) { "Green" } else { "Red" }
    $Status = if ($Success) { "PASS" } else { "FAIL" }
    
    Write-Host "[$Status] $TestName" -ForegroundColor $Color
    if ($Message) {
        Write-Host "    $Message" -ForegroundColor Gray
    }
    if ($Verbose -and $Details) {
        Write-Host "    Details: $Details" -ForegroundColor DarkGray
    }
}

function Test-Port {
    param([string]$Host, [int]$Port, [int]$Timeout = 3000)
    try {
        $TcpClient = New-Object System.Net.Sockets.TcpClient
        $Connect = $TcpClient.BeginConnect($Host, $Port, $null, $null)
        $Wait = $Connect.AsyncWaitHandle.WaitOne($Timeout, $false)
        if ($Wait) {
            $TcpClient.EndConnect($Connect)
            $TcpClient.Close()
            return $true
        }
        $TcpClient.Close()
        return $false
    } catch {
        return $false
    }
}

function Test-HttpEndpoint {
    param([string]$Url, [int]$Timeout = 10)
    try {
        $Response = Invoke-WebRequest -Uri $Url -TimeoutSec $Timeout -UseBasicParsing -ErrorAction Stop
        return @{ Success = $true; StatusCode = $Response.StatusCode; Content = $Response.Content }
    } catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

Write-Host "=== Dogs Website Deployment Test ===" -ForegroundColor Cyan
Write-Host "Domain: $Domain" -ForegroundColor Yellow
Write-Host "Test started at: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

# Test 1: Node.js and npm availability
Write-Host "1. Testing Node.js environment..." -ForegroundColor Cyan
try {
    $NodeVersion = node --version 2>$null
    $NpmVersion = npm --version 2>$null
    
    if ($NodeVersion -and $NpmVersion) {
        Write-TestResult "Node.js Environment" $true "Node.js $NodeVersion, npm $NpmVersion"
    } else {
        Write-TestResult "Node.js Environment" $false "Node.js or npm not found"
    }
} catch {
    Write-TestResult "Node.js Environment" $false "Error checking Node.js: $($_.Exception.Message)"
}

# Test 2: Project dependencies
Write-Host "`n2. Testing project dependencies..." -ForegroundColor Cyan
if (Test-Path "package.json") {
    if (Test-Path "node_modules") {
        Write-TestResult "Project Dependencies" $true "node_modules folder exists"
    } else {
        Write-TestResult "Project Dependencies" $false "node_modules folder missing - run 'npm install'"
    }
} else {
    Write-TestResult "Project Dependencies" $false "package.json not found"
}

# Test 3: Database connection
Write-Host "`n3. Testing database connection..." -ForegroundColor Cyan
try {
    $DatabaseTest = npx prisma db pull --preview-feature 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-TestResult "Database Connection" $true "Database accessible"
    } else {
        Write-TestResult "Database Connection" $false "Database connection failed"
    }
} catch {
    Write-TestResult "Database Connection" $false "Error testing database: $($_.Exception.Message)"
}

# Test 4: Next.js application
Write-Host "`n4. Testing Next.js application..." -ForegroundColor Cyan
if (Test-Port -Host "localhost" -Port 3000) {
    $AppTest = Test-HttpEndpoint -Url "http://localhost:3000"
    if ($AppTest.Success) {
        Write-TestResult "Next.js Application" $true "Application responding on port 3000"
    } else {
        Write-TestResult "Next.js Application" $false "Application not responding: $($AppTest.Error)"
    }
} else {
    Write-TestResult "Next.js Application" $false "Port 3000 not accessible - is the app running?"
}

# Test 5: Nginx configuration
Write-Host "`n5. Testing nginx configuration..." -ForegroundColor Cyan
if (Test-Path "$NginxPath\nginx.exe") {
    try {
        $NginxTest = & "$NginxPath\nginx.exe" -t 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-TestResult "Nginx Configuration" $true "Configuration syntax is valid"
        } else {
            Write-TestResult "Nginx Configuration" $false "Configuration syntax error: $NginxTest"
        }
    } catch {
        Write-TestResult "Nginx Configuration" $false "Error testing nginx: $($_.Exception.Message)"
    }
} else {
    Write-TestResult "Nginx Configuration" $false "nginx.exe not found at $NginxPath"
}

# Test 6: Nginx service
Write-Host "`n6. Testing nginx service..." -ForegroundColor Cyan
if (Test-Port -Host "localhost" -Port 80) {
    $HttpTest = Test-HttpEndpoint -Url "http://localhost"
    if ($HttpTest.Success) {
        Write-TestResult "Nginx HTTP Service" $true "HTTP service responding"
    } else {
        Write-TestResult "Nginx HTTP Service" $false "HTTP service error: $($HttpTest.Error)"
    }
} else {
    Write-TestResult "Nginx HTTP Service" $false "Port 80 not accessible - is nginx running?"
}

# Test 7: HTTPS service (if not skipping SSL)
if (-not $SkipSSL) {
    Write-Host "`n7. Testing HTTPS service..." -ForegroundColor Cyan
    if (Test-Port -Host "localhost" -Port 443) {
        $HttpsTest = Test-HttpEndpoint -Url "https://localhost"
        if ($HttpsTest.Success) {
            Write-TestResult "Nginx HTTPS Service" $true "HTTPS service responding"
        } else {
            Write-TestResult "Nginx HTTPS Service" $false "HTTPS service error: $($HttpsTest.Error)"
        }
    } else {
        Write-TestResult "Nginx HTTPS Service" $false "Port 443 not accessible"
    }
}

# Test 8: Firewall rules
Write-Host "`n8. Testing firewall rules..." -ForegroundColor Cyan
$FirewallRules = Get-NetFirewallRule | Where-Object { $_.DisplayName -like "Dogs Website*" -and $_.Enabled -eq "True" }
if ($FirewallRules.Count -ge 3) {
    Write-TestResult "Firewall Rules" $true "$($FirewallRules.Count) firewall rules found and enabled"
} else {
    Write-TestResult "Firewall Rules" $false "Missing or disabled firewall rules"
}

# Test 9: DuckDNS resolution
Write-Host "`n9. Testing DuckDNS resolution..." -ForegroundColor Cyan
try {
    $DnsResult = Resolve-DnsName -Name $Domain -ErrorAction Stop
    if ($DnsResult) {
        $ResolvedIP = $DnsResult | Where-Object { $_.Type -eq "A" } | Select-Object -First 1 -ExpandProperty IPAddress
        Write-TestResult "DuckDNS Resolution" $true "Domain resolves to $ResolvedIP"
    } else {
        Write-TestResult "DuckDNS Resolution" $false "Domain does not resolve"
    }
} catch {
    Write-TestResult "DuckDNS Resolution" $false "DNS resolution error: $($_.Exception.Message)"
}

# Test 10: External HTTP access
Write-Host "`n10. Testing external HTTP access..." -ForegroundColor Cyan
$ExternalHttpTest = Test-HttpEndpoint -Url "http://$Domain" -Timeout 15
if ($ExternalHttpTest.Success) {
    if ($ExternalHttpTest.StatusCode -eq 301 -or $ExternalHttpTest.StatusCode -eq 302) {
        Write-TestResult "External HTTP Access" $true "HTTP redirects to HTTPS (Status: $($ExternalHttpTest.StatusCode))"
    } else {
        Write-TestResult "External HTTP Access" $true "HTTP accessible (Status: $($ExternalHttpTest.StatusCode))"
    }
} else {
    Write-TestResult "External HTTP Access" $false "HTTP not accessible: $($ExternalHttpTest.Error)"
}

# Test 11: External HTTPS access (if not skipping SSL)
if (-not $SkipSSL) {
    Write-Host "`n11. Testing external HTTPS access..." -ForegroundColor Cyan
    $ExternalHttpsTest = Test-HttpEndpoint -Url "https://$Domain" -Timeout 15
    if ($ExternalHttpsTest.Success) {
        Write-TestResult "External HTTPS Access" $true "HTTPS accessible (Status: $($ExternalHttpsTest.StatusCode))"
    } else {
        Write-TestResult "External HTTPS Access" $false "HTTPS not accessible: $($ExternalHttpsTest.Error)"
    }
}

# Generate test report
Write-Host "`n=== Test Summary ===" -ForegroundColor Cyan

$PassedTests = ($TestResults | Where-Object { $_.Success }).Count
$TotalTests = $TestResults.Count
$FailedTests = $TotalTests - $PassedTests

Write-Host "Total Tests: $TotalTests" -ForegroundColor White
Write-Host "Passed: $PassedTests" -ForegroundColor Green
Write-Host "Failed: $FailedTests" -ForegroundColor Red

if ($FailedTests -gt 0) {
    Write-Host "`nFailed Tests:" -ForegroundColor Red
    $TestResults | Where-Object { -not $_.Success } | ForEach-Object {
        Write-Host "- $($_.Test): $($_.Message)" -ForegroundColor Red
    }
}

# Save detailed report
$ReportPath = "deployment-test-report-$(Get-Date -Format 'yyyy-MM-dd-HH-mm-ss').json"
$TestResults | ConvertTo-Json -Depth 3 | Out-File -FilePath $ReportPath -Encoding UTF8

Write-Host "`nDetailed report saved to: $ReportPath" -ForegroundColor Yellow

# Final recommendation
if ($FailedTests -eq 0) {
    Write-Host "`n🎉 All tests passed! Your Dogs Website is ready for production!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some tests failed. Please address the issues before going live." -ForegroundColor Yellow
}
