# DuckDNS IP Update Script with Logging
# Replace YOUR_DOMAIN and YOUR_TOKEN with your actual values

param(
    [string]$Domain = "YOUR_DOMAIN",
    [string]$Token = "YOUR_TOKEN"
)

$LogFile = "duckdns-update.log"
$Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

function Write-Log {
    param([string]$Message)
    $LogEntry = "$Timestamp - $Message"
    Write-Host $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry
}

Write-Log "Starting DuckDNS IP update for domain: $Domain"

try {
    # Get current public IP
    $CurrentIP = (Invoke-WebRequest -Uri "https://api.ipify.org" -UseBasicParsing).Content.Trim()
    Write-Log "Current public IP: $CurrentIP"
    
    # Update DuckDNS
    $UpdateUrl = "https://www.duckdns.org/update?domains=$Domain&token=$Token&ip=$CurrentIP"
    $Response = Invoke-WebRequest -Uri $UpdateUrl -UseBasicParsing
    
    if ($Response.Content.Trim() -eq "OK") {
        Write-Log "DuckDNS update successful"
    } else {
        Write-Log "DuckDNS update failed: $($Response.Content)"
    }
} catch {
    Write-Log "Error updating DuckDNS: $($_.Exception.Message)"
}

Write-Log "DuckDNS update completed"
