'use client';

import { useRouter, usePathname, useSearchParams } from 'next/navigation';

export type FilterType = 'all' | 'male' | 'female' | 'sale';

interface DogFiltersProps {
  // Убираем onFilterChange, так как мы используем только URL для фильтрации
  activeFilter: FilterType;
  counts: {
    all: number;
    male: number;
    female: number;
    sale: number;
  };
}

export default function DogFilters({ activeFilter, counts }: DogFiltersProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const filters: { id: FilterType; label: string }[] = [
    { id: 'all', label: `Все собаки (${counts.all})` },
    { id: 'male', label: `Кобели (${counts.male})` },
    { id: 'female', label: `Суки (${counts.female})` },
    { id: 'sale', label: `Доступны для продажи (${counts.sale})` },
  ];

  const handleFilterChange = (filter: FilterType) => {
    // Обновляем URL с новым фильтром
    const params = new URLSearchParams(searchParams);
    if (filter === 'all') {
      params.delete('filter');
    } else {
      params.set('filter', filter);
    }

    // Переходим на новый URL с обновленными параметрами
    router.push(`${pathname}?${params.toString()}`);
  };

  return (
    <div className="flex flex-wrap justify-center gap-3 mb-10">
      {filters.map((filter) => (
        <button
          key={filter.id}
          onClick={() => handleFilterChange(filter.id)}
          className={`px-4 py-2 rounded-full text-sm transition-all ${
            activeFilter === filter.id
              ? 'bg-white text-forest-dark font-medium shadow-md'
              : 'bg-white/50 text-forest-medium hover:bg-white hover:text-forest-dark'
          }`}
        >
          {filter.label}
        </button>
      ))}
    </div>
  );
}
