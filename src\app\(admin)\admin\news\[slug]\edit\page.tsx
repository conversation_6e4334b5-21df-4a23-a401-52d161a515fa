'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import FileUpload from '@/components/ui/FileUpload';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface NewsItem {
  id: string;
  title: string;
  content: string;
  slug: string;
  isPublished: boolean;
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  coverImage: string | null;
  photos: Photo[];
}

const newsSchema = z.object({
  title: z.string().min(1, 'Заголовок обязателен'),
  content: z.string().min(1, 'Содержание обязательно'),
  isPublished: z.boolean().default(false),
});

type NewsFormData = z.infer<typeof newsSchema>;

export default function EditNewsPage({ params }: { params: { slug: string } }) {
  const [newsItem, setNewsItem] = useState<NewsItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [additionalPhotos, setAdditionalPhotos] = useState<File[]>([]);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<NewsFormData>({
    resolver: zodResolver(newsSchema),
  });

  useEffect(() => {
    const fetchNewsItem = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/news/${params.slug}`);

        if (!response.ok) {
          throw new Error('Ошибка при загрузке данных');
        }

        const data: NewsItem = await response.json();
        setNewsItem(data);

        // Преобразуем данные для формы
        reset({
          title: data.title,
          content: data.content,
          isPublished: data.isPublished,
        });
      } catch (err) {
        setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
        console.error('Ошибка при загрузке новости:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNewsItem();
  }, [params.slug, reset]);

  const onSubmit = async (data: NewsFormData) => {
    if (!newsItem) return;

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Создаем объект с данными новости
      const newsData = {
        ...data,
      };

      // Отправляем запрос на обновление новости
      const response = await fetch(`/api/news/${params.slug}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newsData),
      });

      if (!response.ok) {
        throw new Error('Ошибка при обновлении новости');
      }

      // Если есть новая обложка, загружаем ее
      if (coverImage) {
        const formData = new FormData();
        formData.append('file', coverImage);
        formData.append('newsId', newsItem.id);
        formData.append('isMain', 'true');
        formData.append('order', '0');

        const imageResponse = await fetch('/api/news/photos', {
          method: 'POST',
          body: formData,
        });

        if (!imageResponse.ok) {
          console.error('Ошибка при загрузке обложки:', await imageResponse.text());
        }
      }

      // Если есть дополнительные фотографии, загружаем их
      if (additionalPhotos.length > 0) {
        for (let i = 0; i < additionalPhotos.length; i++) {
          const formData = new FormData();
          formData.append('file', additionalPhotos[i]);
          formData.append('newsId', newsItem.id);
          formData.append('isMain', 'false');
          formData.append('order', (i + 1).toString());

          const photoResponse = await fetch('/api/news/photos', {
            method: 'POST',
            body: formData,
          });

          if (!photoResponse.ok) {
            console.error(`Ошибка при загрузке фотографии ${i + 1}:`, await photoResponse.text());
          }
        }
      }

      setSuccess('Новость успешно обновлена');

      // Обновляем данные на странице
      const updatedNewsResponse = await fetch(`/api/news/${params.slug}`);
      if (updatedNewsResponse.ok) {
        const updatedNews = await updatedNewsResponse.json();
        setNewsItem(updatedNews);
      }
    } catch (err) {
      console.error('Ошибка при обновлении новости:', err);
      setError('Произошла ошибка при обновлении новости. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCoverImageChange = (files: File[]) => {
    if (files.length > 0) {
      setCoverImage(files[0]);
    } else {
      setCoverImage(null);
    }
  };

  const handleAdditionalPhotosChange = (files: File[]) => {
    setAdditionalPhotos(files);
  };

  const handleRemoveCoverImage = async () => {
    if (!newsItem) return;

    try {
      const response = await fetch(`/api/news/${params.slug}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ coverImage: null }),
      });

      if (!response.ok) {
        throw new Error('Ошибка при удалении обложки');
      }

      // Обновляем данные на странице
      const updatedNewsResponse = await fetch(`/api/news/${params.slug}`);
      if (updatedNewsResponse.ok) {
        const updatedNews = await updatedNewsResponse.json();
        setNewsItem(updatedNews);
      }
    } catch (err) {
      console.error('Ошибка при удалении обложки:', err);
      setError('Произошла ошибка при удалении обложки. Пожалуйста, попробуйте позже.');
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error && !newsItem) {
    return (
      <AdminLayout title="Ошибка">
        <Alert type="error">{error}</Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/news')}>
            Вернуться к списку новостей
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={`Редактирование: ${newsItem?.title || ''}`}>
      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Основная информация</h2>
          <div className="space-y-6">
            <Input
              label="Заголовок"
              {...register('title')}
              error={errors.title?.message}
              fullWidth
            />
            <Textarea
              label="Содержание"
              {...register('content')}
              error={errors.content?.message}
              rows={10}
              fullWidth
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Изображения</h2>

          {/* Текущие фотографии */}
          {newsItem && newsItem.photos && newsItem.photos.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Текущие фотографии</h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {newsItem.photos.map((photo) => (
                  <div key={photo.id} className="relative group">
                    <div className={`relative aspect-square rounded-md overflow-hidden ${
                      photo.isMain ? 'ring-2 ring-blue-500' : ''
                    }`}>
                      <img
                        src={photo.url}
                        alt={photo.title || newsItem.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {photo.isMain && (
                      <div className="absolute top-2 left-2">
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Обложка
                        </span>
                      </div>
                    )}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <div className="flex space-x-2">
                        {!photo.isMain && (
                          <button
                            type="button"
                            onClick={async () => {
                              try {
                                const response = await fetch(`/api/news/photos/${photo.id}`, {
                                  method: 'PUT',
                                  headers: {
                                    'Content-Type': 'application/json',
                                  },
                                  body: JSON.stringify({ isMain: true }),
                                });

                                if (response.ok) {
                                  // Обновляем данные на странице
                                  const updatedNewsResponse = await fetch(`/api/news/${params.slug}`);
                                  if (updatedNewsResponse.ok) {
                                    const updatedNews = await updatedNewsResponse.json();
                                    setNewsItem(updatedNews);
                                  }
                                }
                              } catch (err) {
                                console.error('Ошибка при установке главной фотографии:', err);
                              }
                            }}
                            className="bg-blue-500 text-white rounded-full p-1"
                            title="Сделать обложкой"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                          </button>
                        )}
                        <button
                          type="button"
                          onClick={async () => {
                            try {
                              const response = await fetch(`/api/news/photos/${photo.id}`, {
                                method: 'DELETE',
                              });

                              if (response.ok) {
                                // Обновляем данные на странице
                                const updatedNewsResponse = await fetch(`/api/news/${params.slug}`);
                                if (updatedNewsResponse.ok) {
                                  const updatedNews = await updatedNewsResponse.json();
                                  setNewsItem(updatedNews);
                                }
                              }
                            } catch (err) {
                              console.error('Ошибка при удалении фотографии:', err);
                            }
                          }}
                          className="bg-red-500 text-white rounded-full p-1"
                          title="Удалить"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Загрузка новой обложки */}
          <div className="space-y-6 mt-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Загрузка новой обложки</h3>
            <FileUpload
              label="Загрузите новое изображение для обложки"
              accept="image/*"
              onChange={handleCoverImageChange}
              fullWidth
            />
            {coverImage && (
              <p className="text-sm text-green-600">
                Обложка выбрана: {coverImage.name}
              </p>
            )}
          </div>

          {/* Загрузка дополнительных фотографий */}
          <div className="space-y-6 mt-8">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Загрузка дополнительных фотографий</h3>
            <FileUpload
              label="Загрузите дополнительные фотографии"
              accept="image/*"
              multiple
              onChange={handleAdditionalPhotosChange}
              fullWidth
            />
            {additionalPhotos.length > 0 && (
              <p className="text-sm text-green-600">
                Выбрано дополнительных фотографий: {additionalPhotos.length}
              </p>
            )}
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Публикация</h2>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPublished"
                {...register('isPublished')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
                Опубликовать на сайте
              </label>
            </div>
            {newsItem && newsItem.publishedAt && (
              <div>
                <p className="text-sm text-gray-500">
                  Дата публикации: {new Date(newsItem.publishedAt).toLocaleDateString('ru-RU')}
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => router.push(`/admin/news/${params.slug}`)}
          >
            Отмена
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            Сохранить
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
