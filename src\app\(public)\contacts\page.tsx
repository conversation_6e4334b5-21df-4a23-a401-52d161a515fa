import Layout from '@/components/layout/Layout';
import Image from 'next/image';
import { PawPrint, Leaf, FloatingElements } from '@/components/ui/Decorations';

export const metadata = {
  title: 'Контакты | Питомник собак',
  description: 'Свяжитесь с нами для получения дополнительной информации о наших собаках и щенках.',
};

export default function ContactsPage() {
  // В будущем данные будут загружаться из базы данных
  const contacts = [
    {
      id: '1',
      type: 'PHONE',
      value: '+7 (XXX) XXX-XX-XX',
      description: 'Основной телефон',
    },
    {
      id: '2',
      type: 'PHONE',
      value: '+7 (XXX) XXX-XX-XX',
      description: 'Дополнительный телефон',
    },
    {
      id: '3',
      type: 'EMAIL',
      value: '<EMAIL>',
      description: 'Основной email',
    },
    {
      id: '4',
      type: 'ADDRESS',
      value: 'г. Москва, ул. Примерная, д. 123',
      description: 'Адрес питомника',
    },
  ];

  const socialMedia = [
    {
      id: '1',
      type: 'SOCIAL',
      value: 'https://instagram.com',
      description: 'Instagram',
    },
    {
      id: '2',
      type: 'SOCIAL',
      value: 'https://facebook.com',
      description: 'Facebook',
    },
    {
      id: '3',
      type: 'SOCIAL',
      value: 'https://vk.com',
      description: 'ВКонтакте',
    },
  ];

  return (
    <Layout>
      {/* Заголовок страницы */}
      <section className="pt-16 pb-24 bg-forest-bg relative">
        <FloatingElements count={6} type="mixed" className="absolute inset-0 z-0 pointer-events-none" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex items-center justify-center mb-6">
            <PawPrint size="sm" className="mr-3 text-shiba-orange" />
            <span className="text-shiba-orange font-medium tracking-wider uppercase text-sm">Наш питомник</span>
          </div>

          <h1 className="text-3xl md:text-4xl font-bold text-center text-forest-dark mb-6">Контакты</h1>

          <div className="max-w-3xl mx-auto text-center mb-12">
            <p className="text-lg text-forest-medium">
              Свяжитесь с нами для получения дополнительной информации о наших собаках и щенках.
            </p>
          </div>

          <div className="absolute left-0 bottom-0 w-32 h-32 opacity-10">
            <PawPrint size="lg" />
          </div>
          <div className="absolute right-10 top-40 w-16 h-16 opacity-10">
            <Leaf size="md" />
          </div>
        </div>
      </section>

      {/* Контактная информация и форма */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <div className="flex items-center mb-6">
                <div className="h-px flex-grow bg-forest-light/20"></div>
                <div className="flex items-center mx-4">
                  <PawPrint size="sm" className="mr-3 text-shiba-orange" />
                  <h2 className="text-2xl font-bold text-forest-dark">Наши контакты</h2>
                </div>
                <div className="h-px flex-grow bg-forest-light/20"></div>
              </div>

              <div className="space-y-6 mt-8">
                {contacts.map((contact) => (
                  <div key={contact.id} className="flex items-start bg-forest-bg/5 p-4 rounded-lg hover:bg-forest-bg/10 transition-colors">
                    <div className="mr-4 text-shiba-orange">
                      {contact.type === 'PHONE' && (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                      )}
                      {contact.type === 'EMAIL' && (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                      )}
                      {contact.type === 'ADDRESS' && (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                      )}
                    </div>
                    <div>
                      <p className="font-semibold text-forest-dark">{contact.description}</p>
                      <p className="text-forest-medium">{contact.value}</p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex items-center mt-12 mb-6">
                <div className="h-px flex-grow bg-forest-light/20"></div>
                <div className="flex items-center mx-4">
                  <Leaf size="sm" className="mr-3 text-shiba-orange" />
                  <h2 className="text-2xl font-bold text-forest-dark">Мы в социальных сетях</h2>
                </div>
                <div className="h-px flex-grow bg-forest-light/20"></div>
              </div>

              <div className="flex space-x-4 mt-8 justify-center">
                {socialMedia.map((social) => (
                  <a
                    key={social.id}
                    href={social.value}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-forest-medium hover:text-shiba-orange transition-colors p-3 bg-forest-bg/5 rounded-full hover:bg-forest-bg/10"
                  >
                    <span className="sr-only">{social.description}</span>
                    {social.description === 'Instagram' && (
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                      </svg>
                    )}
                    {social.description === 'Facebook' && (
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z"/>
                      </svg>
                    )}
                    {social.description === 'ВКонтакте' && (
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21.547 7h-3.29a.743.743 0 0 0-.655.392s-1.312 2.416-1.734 3.23C14.734 12.813 14 12.126 14 11.11V7.603A1.104 1.104 0 0 0 12.896 6.5h-2.474a1.982 1.982 0 0 0-1.75.813s1.255-.204 1.255 1.49c0 .42.022 1.626.04 2.64a.73.73 0 0 1-1.272.503 21.54 21.54 0 0 1-2.498-4.543.693.693 0 0 0-.63-.403h-2.99a.508.508 0 0 0-.48.685C3.005 10.175 6.918 18 11.38 18h1.878a.742.742 0 0 0 .742-.742v-1.135a.73.73 0 0 1 1.23-.53l2.247 2.112a1.09 1.09 0 0 0 .746.295h2.953c1.424 0 1.424-.988.647-1.753-.546-.538-2.518-2.617-2.518-2.617a1.02 1.02 0 0 1-.078-1.323c.637-.84 1.68-2.212 2.122-2.8.603-.804 1.697-2.507.197-2.507z"/>
                      </svg>
                    )}
                  </a>
                ))}
              </div>


            </div>

            <div className="relative">
              <div className="flex items-center mb-6">
                <div className="h-px flex-grow bg-forest-light/20"></div>
                <div className="flex items-center mx-4">
                  <Leaf size="sm" className="mr-3 text-shiba-orange" />
                  <h2 className="text-2xl font-bold text-forest-dark">Отправьте нам сообщение</h2>
                </div>
                <div className="h-px flex-grow bg-forest-light/20"></div>
              </div>

              <form className="space-y-6 mt-8 bg-white p-6 rounded-xl shadow-md relative z-10">
                <div>
                  <label htmlFor="name" className="block text-forest-dark mb-2 font-medium">Ваше имя *</label>
                  <input
                    type="text"
                    id="name"
                    className="w-full px-4 py-2 border border-forest-light/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-shiba-orange/50 focus:border-transparent"
                    placeholder="Введите ваше имя"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-forest-dark mb-2 font-medium">Email *</label>
                  <input
                    type="email"
                    id="email"
                    className="w-full px-4 py-2 border border-forest-light/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-shiba-orange/50 focus:border-transparent"
                    placeholder="Введите ваш email"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="phone" className="block text-forest-dark mb-2 font-medium">Телефон *</label>
                  <input
                    type="tel"
                    id="phone"
                    className="w-full px-4 py-2 border border-forest-light/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-shiba-orange/50 focus:border-transparent"
                    placeholder="Введите ваш телефон"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="subject" className="block text-forest-dark mb-2 font-medium">Тема</label>
                  <input
                    type="text"
                    id="subject"
                    className="w-full px-4 py-2 border border-forest-light/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-shiba-orange/50 focus:border-transparent"
                    placeholder="Введите тему сообщения"
                  />
                </div>
                <div>
                  <label htmlFor="message" className="block text-forest-dark mb-2 font-medium">Сообщение *</label>
                  <textarea
                    id="message"
                    rows={6}
                    className="w-full px-4 py-2 border border-forest-light/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-shiba-orange/50 focus:border-transparent"
                    placeholder="Введите ваше сообщение"
                    required
                  ></textarea>
                </div>
                <button
                  type="submit"
                  className="bg-forest-dark hover:bg-forest-medium text-white font-semibold px-6 py-3 rounded-lg transition-colors w-full"
                >
                  Отправить сообщение
                </button>
              </form>

              <div className="absolute right-[-20px] bottom-[-20px] opacity-10 z-0">
                <PawPrint size="lg" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Карта */}
      <section className="py-16 bg-forest-bg/5 relative">
        <FloatingElements count={3} type="mixed" className="absolute inset-0 z-0 pointer-events-none opacity-30" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex items-center mb-8">
            <div className="h-px flex-grow bg-forest-light/20"></div>
            <div className="flex items-center mx-4">
              <PawPrint size="sm" className="mr-3 text-shiba-orange" />
              <h2 className="text-2xl font-bold text-forest-dark">Наше местоположение</h2>
            </div>
            <div className="h-px flex-grow bg-forest-light/20"></div>
          </div>

          <div className="h-96 bg-white rounded-xl shadow-md overflow-hidden">
            {/* Здесь будет карта */}
            <div className="flex flex-col items-center justify-center h-full bg-forest-bg/5">
              <svg className="w-16 h-16 text-forest-light mb-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"></path>
              </svg>
              <p className="text-forest-medium text-lg">Карта будет добавлена позже</p>
              <p className="text-forest-medium mt-2">Вы можете связаться с нами для получения подробных инструкций</p>
            </div>
          </div>

          <div className="absolute left-10 bottom-10 w-24 h-24 opacity-10">
            <Leaf size="md" />
          </div>
        </div>
      </section>
    </Layout>
  );
}
