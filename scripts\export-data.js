const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function exportData() {
  try {
    console.log('🚀 Начинаем экспорт данных...');

    // Создание папки для бэкапов
    const backupDir = path.join(process.cwd(), 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const backupFile = path.join(backupDir, `prisma-backup-${timestamp}.json`);

    // Экспорт всех данных
    const data = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      data: {}
    };

    // Экспорт пользователей
    console.log('📤 Экспорт пользователей...');
    data.data.users = await prisma.user.findMany();
    console.log(`✅ Экспортировано ${data.data.users.length} пользователей`);

    // Экспорт собак
    console.log('📤 Экспорт собак...');
    data.data.dogs = await prisma.dog.findMany({
      include: {
        photos: true
      }
    });
    console.log(`✅ Экспортировано ${data.data.dogs.length} собак`);

    // Экспорт щенков
    console.log('📤 Экспорт щенков...');
    data.data.puppies = await prisma.puppy.findMany({
      include: {
        photos: true,
        breeding: true
      }
    });
    console.log(`✅ Экспортировано ${data.data.puppies.length} щенков`);

    // Экспорт новостей
    console.log('📤 Экспорт новостей...');
    data.data.news = await prisma.news.findMany({
      include: {
        author: true
      }
    });
    console.log(`✅ Экспортировано ${data.data.news.length} новостей`);

    // Экспорт hero-слайдов
    console.log('📤 Экспорт hero-слайдов...');
    data.data.heroSlides = await prisma.heroSlide.findMany();
    console.log(`✅ Экспортировано ${data.data.heroSlides.length} слайдов`);

    // Экспорт настроек
    console.log('📤 Экспорт настроек...');
    data.data.settings = await prisma.settings.findMany();
    console.log(`✅ Экспортировано ${data.data.settings.length} настроек`);

    // Экспорт вязок (если есть)
    try {
      console.log('📤 Экспорт вязок...');
      data.data.breedings = await prisma.breeding.findMany({
        include: {
          father: true,
          mother: true,
          puppies: true
        }
      });
      console.log(`✅ Экспортировано ${data.data.breedings.length} вязок`);
    } catch (error) {
      console.log('⚠️ Таблица вязок не найдена, пропускаем...');
      data.data.breedings = [];
    }

    // Экспорт галереи (если есть)
    try {
      console.log('📤 Экспорт галереи...');
      data.data.gallery = await prisma.gallery.findMany({
        include: {
          photos: true
        }
      });
      console.log(`✅ Экспортировано ${data.data.gallery.length} альбомов галереи`);
    } catch (error) {
      console.log('⚠️ Таблица галереи не найдена, пропускаем...');
      data.data.gallery = [];
    }

    // Экспорт заявок (если есть)
    try {
      console.log('📤 Экспорт заявок...');
      data.data.inquiries = await prisma.inquiry.findMany();
      console.log(`✅ Экспортировано ${data.data.inquiries.length} заявок`);
    } catch (error) {
      console.log('⚠️ Таблица заявок не найдена, пропускаем...');
      data.data.inquiries = [];
    }

    // Сохранение в файл
    fs.writeFileSync(backupFile, JSON.stringify(data, null, 2));
    
    // Создание сжатой версии
    const zlib = require('zlib');
    const gzipFile = backupFile.replace('.json', '.json.gz');
    const gzip = zlib.createGzip();
    const input = fs.createReadStream(backupFile);
    const output = fs.createWriteStream(gzipFile);
    
    input.pipe(gzip).pipe(output);

    console.log('🎉 Экспорт завершен!');
    console.log(`📁 Файл сохранен: ${backupFile}`);
    console.log(`📁 Сжатый файл: ${gzipFile}`);
    
    // Показать размеры файлов
    const stats = fs.statSync(backupFile);
    console.log(`📊 Размер файла: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);

    // Статистика
    console.log('\n📈 Статистика экспорта:');
    console.log(`👥 Пользователи: ${data.data.users.length}`);
    console.log(`🐕 Собаки: ${data.data.dogs.length}`);
    console.log(`🐶 Щенки: ${data.data.puppies.length}`);
    console.log(`📰 Новости: ${data.data.news.length}`);
    console.log(`🎨 Hero-слайды: ${data.data.heroSlides.length}`);
    console.log(`⚙️ Настройки: ${data.data.settings.length}`);
    console.log(`💕 Вязки: ${data.data.breedings.length}`);
    console.log(`📸 Галерея: ${data.data.gallery.length}`);
    console.log(`📧 Заявки: ${data.data.inquiries.length}`);

  } catch (error) {
    console.error('❌ Ошибка при экспорте данных:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

exportData();
