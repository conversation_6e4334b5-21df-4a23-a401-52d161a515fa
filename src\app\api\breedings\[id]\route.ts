import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/breedings/[id] - Получение информации о конкретной вязке
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    const breeding = await prisma.breeding.findUnique({
      where: { id },
      include: {
        mother: {
          include: {
            photos: true,
          },
        },
        father: {
          include: {
            photos: true,
          },
        },
        puppies: true,
      },
    });

    if (!breeding) {
      return NextResponse.json(
        { error: 'Вязка не найдена' },
        { status: 404 }
      );
    }

    return NextResponse.json(breeding);
  } catch (error) {
    console.error('Ошибка при получении информации о вязке:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении информации о вязке' },
      { status: 500 }
    );
  }
}

// PUT /api/breedings/[id] - Обновление информации о вязке (защищенный маршрут)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const id = params.id;
    const data = await request.json();

    // Проверяем, существует ли вязка
    const existingBreeding = await prisma.breeding.findUnique({
      where: { id }
    });

    if (!existingBreeding) {
      return NextResponse.json(
        { error: 'Вязка не найдена' },
        { status: 404 }
      );
    }

    // Если меняются собаки, проверяем их существование и пол
    if (data.motherId || data.fatherId) {
      const mother = data.motherId
        ? await prisma.dog.findUnique({ where: { id: data.motherId } })
        : null;

      const father = data.fatherId
        ? await prisma.dog.findUnique({ where: { id: data.fatherId } })
        : null;

      if ((data.motherId && !mother) || (data.fatherId && !father)) {
        return NextResponse.json(
          { error: 'Одна или обе собаки не найдены' },
          { status: 404 }
        );
      }

      if ((mother && mother.gender !== 'FEMALE') || (father && father.gender !== 'MALE')) {
        return NextResponse.json(
          { error: 'Неверный пол собак для вязки' },
          { status: 400 }
        );
      }
    }

    // Обновляем информацию о вязке
    const updatedBreeding = await prisma.breeding.update({
      where: { id },
      data: {
        date: data.date ? new Date(data.date) : undefined,
        description: data.description,
        status: data.status,
        motherId: data.motherId,
        fatherId: data.fatherId,
      },
      include: {
        mother: true,
        father: true,
      },
    });

    return NextResponse.json(updatedBreeding);
  } catch (error) {
    console.error('Ошибка при обновлении информации о вязке:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении информации о вязке' },
      { status: 500 }
    );
  }
}

// DELETE /api/breedings/[id] - Удаление вязки (защищенный маршрут)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Проверяем, существует ли вязка
    const existingBreeding = await prisma.breeding.findUnique({
      where: { id },
      include: {
        puppies: true,
      },
    });

    if (!existingBreeding) {
      return NextResponse.json(
        { error: 'Вязка не найдена' },
        { status: 404 }
      );
    }

    // Проверяем, есть ли у вязки щенки
    if (existingBreeding.puppies.length > 0) {
      return NextResponse.json(
        { error: 'Невозможно удалить вязку, у которой есть щенки' },
        { status: 400 }
      );
    }

    // Удаляем вязку
    await prisma.breeding.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении вязки:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении вязки' },
      { status: 500 }
    );
  }
}
