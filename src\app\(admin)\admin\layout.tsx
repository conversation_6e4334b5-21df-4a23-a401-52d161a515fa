'use client';


import { Inter } from "next/font/google";
import { SessionProvider } from 'next-auth/react';
import { ReactNode } from 'react';

const inter = Inter({
  subsets: ["latin", "cyrillic"],
  variable: "--font-inter",
});

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <html lang="ru">
      <body className={`${inter.variable} antialiased`}>
        <SessionProvider>
          <div className="min-h-screen bg-gray-50">
            {children}
          </div>
        </SessionProvider>
      </body>
    </html>
  );
}
