# Инструкция по настройке SSL сертификата

## 1. Установка Certbot

1. Перейдите на [https://certbot.eff.org/instructions?ws=nginx&os=windows](https://certbot.eff.org/instructions?ws=nginx&os=windows)
2. Скачайте Certbot для Windows
3. Установите Certbot, следуя инструкциям на сайте

## 2. Получение SSL сертификата

### Автоматический способ (рекомендуется):

1. Убедитесь, что ваш домен DuckDNS настроен и указывает на ваш IP
2. Убедитесь, что порты 80 и 443 открыты в брандмауэре и роутере
3. Запустите PowerShell от имени администратора
4. Выполните команду:
   ```powershell
   .\ssl-setup.ps1 -Domain "yourdomain.duckdns.org" -Email "<EMAIL>"
   ```

### Ручной способ:

1. Остановите nginx:
   ```cmd
   C:\nginx\nginx.exe -s stop
   ```

2. Получите сертификат:
   ```cmd
   certbot certonly --standalone -d yourdomain.duckdns.org
   ```

3. Обновите nginx конфигурацию:
   - Откройте файл `nginx-dogs-site.conf`
   - Замените пути к сертификатам:
     ```nginx
     ssl_certificate C:\Certbot\live\yourdomain.duckdns.org\fullchain.pem;
     ssl_certificate_key C:\Certbot\live\yourdomain.duckdns.org\privkey.pem;
     ```

4. Запустите nginx:
   ```cmd
   C:\nginx\nginx.exe
   ```

## 3. Настройка автоматического обновления

### Создание задачи в Планировщике задач:

1. Откройте "Планировщик задач" (Task Scheduler)
2. Создайте новую задачу:
   - **Имя**: "SSL Certificate Renewal"
   - **Безопасность**: Выполнять с наивысшими правами
   - **Триггер**: Ежемесячно (1 число каждого месяца)
   - **Действие**: Запуск программы
   - **Программа**: `powershell.exe`
   - **Аргументы**: `-ExecutionPolicy Bypass -File "C:\path\to\your\ssl-renew.ps1"`

## 4. Проверка SSL сертификата

1. Откройте браузер и перейдите на `https://yourdomain.duckdns.org`
2. Проверьте, что соединение защищено (зеленый замок)
3. Используйте онлайн-инструменты для проверки:
   - [SSL Labs Test](https://www.ssllabs.com/ssltest/)
   - [SSL Checker](https://www.sslshopper.com/ssl-checker.html)

## 5. Устранение проблем

### Ошибка "Port 80 already in use":
- Убедитесь, что nginx остановлен перед получением сертификата
- Проверьте, что никакие другие веб-серверы не используют порт 80

### Ошибка "Domain validation failed":
- Убедитесь, что домен DuckDNS указывает на ваш публичный IP
- Проверьте, что порт 80 открыт в брандмауэре и роутере
- Убедитесь, что нет других сервисов на порту 80

### Ошибка nginx при запуске:
- Проверьте конфигурацию: `nginx -t`
- Убедитесь, что пути к сертификатам корректны
- Проверьте права доступа к файлам сертификатов

## Примечания

- Сертификаты Let's Encrypt действительны 90 дней
- Автоматическое обновление должно происходить ежемесячно
- Логи обновления сохраняются в файл `ssl-renew.log`
- После получения сертификата ваш сайт будет доступен только по HTTPS
