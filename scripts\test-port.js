#!/usr/bin/env node

const http = require('http');
const { exec } = require('child_process');

const TEST_PORT = 8081;

// Создаем простой тестовый сервер
const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
  res.end(`
    <h1>🎉 Тестовый сервер работает!</h1>
    <p>Порт ${TEST_PORT} доступен из интернета</p>
    <p>Время: ${new Date().toLocaleString('ru-RU')}</p>
    <p>IP клиента: ${req.connection.remoteAddress}</p>
  `);
});

server.listen(TEST_PORT, '0.0.0.0', () => {
  console.log(`🚀 Тестовый сервер запущен на порту ${TEST_PORT}`);
  console.log(`📍 Локальный адрес: http://localhost:${TEST_PORT}`);
  console.log(`🌐 Внешний адрес: http://ta-shiba.duckdns.org:${TEST_PORT}`);
  console.log(`\n💡 Если внешний адрес работает, значит проблема в nginx конфигурации`);
  console.log(`💡 Если не работает, значит проблема в роутере или firewall`);
  console.log(`\n⏹️  Нажмите Ctrl+C для остановки`);
});

// Обработка завершения
process.on('SIGINT', () => {
  console.log('\n🛑 Остановка тестового сервера...');
  server.close(() => {
    console.log('✅ Сервер остановлен');
    process.exit(0);
  });
});
