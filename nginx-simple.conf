worker_processes auto;
error_log C:/nginx-1.28.0/logs/error.log;
pid C:/nginx-1.28.0/logs/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    
    access_log C:/nginx-1.28.0/logs/access.log;
    
    sendfile on;
    keepalive_timeout 65;
    
    # Upstream for Next.js application
    upstream dogs_app {
        server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # Main HTTP server on port 8080
    server {
        listen 8080;
        server_name localhost ta-shiba.duckdns.org;

        location / {
            proxy_pass http://dogs_app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
        }

        # Static files for uploads
        location /uploads/ {
            alias "C:/Users/<USER>/Desktop/dev/исходники/site normal/uploads/";
            expires 1y;
            add_header Cache-Control "public";
        }

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }


}
