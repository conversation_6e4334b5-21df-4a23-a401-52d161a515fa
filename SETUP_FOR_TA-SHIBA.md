# Настройка для домена ta-shiba.duckdns.org

## Быстрый старт для вашего домена

Поскольку у вас уже есть рабочая конфигурация nginx на порту 8080, мы адаптируем настройку под ваши текущие параметры.

### 1. Обновление DuckDNS

Вам нужно получить токен DuckDNS для домена `ta-shiba`:

1. Войдите на [DuckDNS.org](https://www.duckdns.org)
2. Найдите ваш домен `ta-shiba`
3. Скопируйте токен
4. Обновите скрипт:

```powershell
# Отредактируйте duckdns-update.ps1
# Замените YOUR_TOKEN на ваш реальный токен
.\duckdns-update.ps1
```

### 2. Настройка nginx

Ваша текущая конфигурация использует порт 8080. Обновленная конфигурация `nginx-dogs-site.conf` адаптирована под это:

```nginx
# Порт 8080 - ваша текущая настройка
server {
    listen 8080;
    server_name localhost ta-shiba.duckdns.org;
    # Проксирует запросы к Next.js приложению
}

# Порт 80 - для SSL сертификатов и редиректа
server {
    listen 80;
    server_name ta-shiba.duckdns.org;
    # Временно редиректит на порт 8080
}
```

### 3. Установка конфигурации nginx

```powershell
# Скопируйте обновленную конфигурацию
Copy-Item "nginx-dogs-site.conf" "C:\nginx\conf\conf.d\ta-shiba.conf"

# Проверьте конфигурацию
C:\nginx\nginx.exe -t

# Перезагрузите nginx
C:\nginx\nginx.exe -s reload
```

### 4. Настройка брандмауэра

```powershell
# Откройте необходимые порты
.\setup-firewall.ps1

# Убедитесь, что порт 8080 также открыт
New-NetFirewallRule -DisplayName "Dogs Website - Port 8080" -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow
```

### 5. Проверка текущей настройки

```powershell
# Проверьте доступность на порту 8080
.\test-ports.ps1

# Проверьте через браузер
# http://localhost:8080
# http://ta-shiba.duckdns.org:8080 (если DuckDNS настроен)
```

### 6. Настройка SSL (опционально)

После того как базовая настройка работает:

```powershell
# Получите SSL сертификат
.\ssl-setup.ps1 -Domain "ta-shiba.duckdns.org"

# После получения SSL, обновите nginx конфигурацию:
# - Раскомментируйте редирект с HTTP на HTTPS
# - Убедитесь, что HTTPS секция настроена правильно
```

### 7. Тестирование

```powershell
# Полное тестирование системы
.\full-system-test.ps1 -Domain "ta-shiba.duckdns.org"

# Если SSL еще не настроен, пропустите SSL тесты
.\full-system-test.ps1 -Domain "ta-shiba.duckdns.org" -SkipSSL
```

## Поэтапный план

### Этап 1: Базовая настройка (без SSL)
1. ✅ Обновить DuckDNS токен
2. ✅ Установить nginx конфигурацию
3. ✅ Настроить брандмауэр
4. ✅ Проверить доступность на порту 8080

### Этап 2: SSL и безопасность
1. Получить SSL сертификат
2. Обновить nginx для HTTPS
3. Настроить автоматическое обновление сертификатов
4. Переключить на порт 443

### Этап 3: Автозапуск и мониторинг
1. Настроить автозапуск приложения
2. Настроить мониторинг
3. Настроить резервное копирование

## Текущие URL для тестирования

После настройки ваш сайт будет доступен по адресам:

- **Локально**: http://localhost:8080
- **Через DuckDNS**: http://ta-shiba.duckdns.org:8080
- **После SSL**: https://ta-shiba.duckdns.org

## Важные файлы для редактирования

1. **duckdns-update.ps1** - добавьте ваш токен DuckDNS
2. **.env.production** - уже обновлен для вашего домена
3. **nginx-dogs-site.conf** - уже адаптирован под порт 8080

## Следующие шаги

1. Получите токен DuckDNS для домена `ta-shiba`
2. Обновите `duckdns-update.ps1` с вашим токеном
3. Запустите `duckdns-update.ps1` для обновления IP
4. Установите nginx конфигурацию
5. Протестируйте доступность
6. При необходимости настройте SSL

Нужна помощь с любым из этих шагов? Просто спросите!
