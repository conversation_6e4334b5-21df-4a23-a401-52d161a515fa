/* Цветовая палитра для сайта питомника собак */
:root {
  /* Основные лесные цвета */
  --forest-dark: #2c3e2d;      /* Темно-зеленый лесной */
  --forest-medium: #3e5641;    /* Средний лесной зеленый */
  --forest-light: #5a7259;     /* Светлый лесной зеленый */
  --forest-pale: #8ba888;      /* Бледно-зеленый */
  --forest-bg: #f0f4f1;        /* Фоновый светло-зеленый */
  
  /* Оранжевые акценты (под цвет сиба-ину) */
  --shiba-orange: #e67e22;     /* Основной оранжевый */
  --shiba-orange-dark: #d35400; /* Темно-оранжевый */
  --shiba-orange-light: #f39c12; /* Светло-оранжевый */
  --shiba-cream: #fef5e7;      /* Кремовый */
  --shiba-brown: #8b4513;      /* Коричневый */
  
  /* Нейтральные цвета */
  --neutral-dark: #333333;     /* Темно-серый */
  --neutral-medium: #666666;   /* Средне-серый */
  --neutral-light: #999999;    /* Светло-серый */
  --neutral-pale: #e0e0e0;     /* Бледно-серый */
  --neutral-white: #ffffff;    /* Белый */
  
  /* Функциональные цвета */
  --success: #2ecc71;          /* Зеленый для успешных действий */
  --warning: #f1c40f;          /* Желтый для предупреждений */
  --error: #e74c3c;            /* Красный для ошибок */
  --info: #3498db;             /* Синий для информации */
}

/* Классы для текста */
.text-forest-dark { color: var(--forest-dark); }
.text-forest-medium { color: var(--forest-medium); }
.text-forest-light { color: var(--forest-light); }
.text-forest-pale { color: var(--forest-pale); }

.text-shiba-orange { color: var(--shiba-orange); }
.text-shiba-orange-dark { color: var(--shiba-orange-dark); }
.text-shiba-orange-light { color: var(--shiba-orange-light); }
.text-shiba-cream { color: var(--shiba-cream); }
.text-shiba-brown { color: var(--shiba-brown); }

/* Классы для фона */
.bg-forest-dark { background-color: var(--forest-dark); }
.bg-forest-medium { background-color: var(--forest-medium); }
.bg-forest-light { background-color: var(--forest-light); }
.bg-forest-pale { background-color: var(--forest-pale); }
.bg-forest-bg { background-color: var(--forest-bg); }

.bg-shiba-orange { background-color: var(--shiba-orange); }
.bg-shiba-orange-dark { background-color: var(--shiba-orange-dark); }
.bg-shiba-orange-light { background-color: var(--shiba-orange-light); }
.bg-shiba-cream { background-color: var(--shiba-cream); }
.bg-shiba-brown { background-color: var(--shiba-brown); }

/* Градиенты */
.bg-forest-gradient {
  background: linear-gradient(135deg, var(--forest-dark), var(--forest-medium));
}

.bg-shiba-gradient {
  background: linear-gradient(135deg, var(--shiba-orange), var(--shiba-orange-dark));
}

.bg-forest-to-shiba-gradient {
  background: linear-gradient(135deg, var(--forest-medium), var(--shiba-orange));
}

/* Классы для границ */
.border-forest-dark { border-color: var(--forest-dark); }
.border-forest-medium { border-color: var(--forest-medium); }
.border-forest-light { border-color: var(--forest-light); }
.border-forest-pale { border-color: var(--forest-pale); }

.border-shiba-orange { border-color: var(--shiba-orange); }
.border-shiba-orange-dark { border-color: var(--shiba-orange-dark); }
.border-shiba-orange-light { border-color: var(--shiba-orange-light); }
.border-shiba-cream { border-color: var(--shiba-cream); }
.border-shiba-brown { border-color: var(--shiba-brown); }

/* Кнопки */
.btn-forest {
  background-color: var(--forest-medium);
  color: white;
  transition: background-color 0.3s ease;
}

.btn-forest:hover {
  background-color: var(--forest-dark);
}

.btn-shiba {
  background-color: var(--shiba-orange);
  color: white;
  transition: background-color 0.3s ease;
}

.btn-shiba:hover {
  background-color: var(--shiba-orange-dark);
}

.btn-outline-forest {
  background-color: transparent;
  color: var(--forest-medium);
  border: 2px solid var(--forest-medium);
  transition: all 0.3s ease;
}

.btn-outline-forest:hover {
  background-color: var(--forest-medium);
  color: white;
}

.btn-outline-shiba {
  background-color: transparent;
  color: var(--shiba-orange);
  border: 2px solid var(--shiba-orange);
  transition: all 0.3s ease;
}

.btn-outline-shiba:hover {
  background-color: var(--shiba-orange);
  color: white;
}
