import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { writeFile } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// POST /api/news/photos - Загрузка фотографии для новости (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const newsId = formData.get('newsId') as string;
    const isMain = formData.get('isMain') === 'true';
    const order = parseInt(formData.get('order') as string) || 0;
    const title = formData.get('title') as string || null;
    const description = formData.get('description') as string || null;

    if (!file) {
      return NextResponse.json(
        { error: 'Файл не найден' },
        { status: 400 }
      );
    }

    if (!newsId) {
      return NextResponse.json(
        { error: 'ID новости не указан' },
        { status: 400 }
      );
    }

    // Проверяем, существует ли новость
    const news = await prisma.news.findUnique({
      where: { id: newsId }
    });

    if (!news) {
      return NextResponse.json(
        { error: 'Новость не найдена' },
        { status: 404 }
      );
    }

    // Получаем расширение файла
    const fileExtension = path.extname(file.name);

    // Создаем уникальное имя файла
    const fileName = `news_${uuidv4()}${fileExtension}`;

    // Путь для сохранения файла
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');
    const filePath = path.join(uploadDir, fileName);

    // Получаем содержимое файла
    const fileBuffer = await file.arrayBuffer();

    // Сохраняем файл
    await writeFile(filePath, Buffer.from(fileBuffer));

    // URL для доступа к файлу
    const fileUrl = `/uploads/${fileName}`;

    // Если это главное фото, сбрасываем флаг isMain у других фотографий новости
    if (isMain) {
      await prisma.photo.updateMany({
        where: {
          newsId: newsId,
          isMain: true,
        },
        data: {
          isMain: false,
        },
      });
    }

    // Создаем запись о фотографии в базе данных
    const photo = await prisma.photo.create({
      data: {
        url: fileUrl,
        title,
        description,
        isMain,
        order,
        newsId: newsId,
      },
    });

    return NextResponse.json(photo, { status: 201 });
  } catch (error) {
    console.error('Ошибка при загрузке фотографии:', error);
    return NextResponse.json(
      { error: 'Ошибка при загрузке фотографии' },
      { status: 500 }
    );
  }
}
