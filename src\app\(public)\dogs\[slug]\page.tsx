import Image from 'next/image';
import Link from 'next/link';
import Layout from '@/components/layout/Layout';
import { prisma } from '@/lib/db';
import { formatDate } from '@/lib/utils';
import { notFound } from 'next/navigation';
import ContactButton from '@/components/ContactButton';
import DogGallery from './DogGallery';
import { PawPrint, Leaf, FloatingElements, SectionDivider } from '@/components/ui/Decorations';

// Генерируем метаданные для страницы
export async function generateMetadata({ params }: { params: { slug: string } }) {
  const { slug } = await params;
  const dog = await prisma.dog.findUnique({
    where: { slug },
  });

  if (!dog) {
    return {
      title: 'Собака не найдена',
      description: 'Запрашиваемая собака не найдена',
    };
  }

  return {
    title: `${dog.name} - ${dog.breed} - Питомник собак`,
    description: dog.description || `${dog.name} - ${dog.breed}. Узнайте больше о нашей собаке.`,
  };
}

export default async function DogPage({ params }: { params: { slug: string } }) {
  const { slug } = await params;
  // Получаем настройки сайта
  const settingsList = await prisma.settings.findMany();
  const settings = settingsList.reduce((acc, setting) => {
    acc[setting.key] = setting.value;
    return acc;
  }, {} as Record<string, string>);

  // Получаем информацию о собаке
  const dog = await prisma.dog.findUnique({
    where: {
      slug,
      isPublished: true,
    },
    include: {
      photos: {
        orderBy: {
          order: 'asc',
        },
      },
      breedings: {
        where: {
          status: {
            in: ['COMPLETED', 'IN_PROGRESS'],
          },
        },
        include: {
          puppies: true,
          father: {
            include: {
              photos: {
                where: { isMain: true },
                take: 1,
              },
            },
          },
          mother: {
            include: {
              photos: {
                where: { isMain: true },
                take: 1,
              },
            },
          },
        },
        orderBy: {
          date: 'desc',
        },
      },
      sireOf: {
        where: {
          status: {
            in: ['COMPLETED', 'IN_PROGRESS'],
          },
        },
        include: {
          puppies: true,
          father: {
            include: {
              photos: {
                where: { isMain: true },
                take: 1,
              },
            },
          },
          mother: {
            include: {
              photos: {
                where: { isMain: true },
                take: 1,
              },
            },
          },
        },
        orderBy: {
          date: 'desc',
        },
      },
    },
  });

  // Если собака не найдена, возвращаем 404
  if (!dog) {
    notFound();
  }

  // Находим главное фото
  const mainPhoto = dog.photos.find((photo: any) => photo.isMain) || dog.photos[0];

  // Остальные фотографии
  const otherPhotos = dog.photos.filter((photo: any) => photo !== mainPhoto);

  // Объединяем все вязки (как мать и как отец)
  const allBreedings = [...dog.breedings, ...dog.sireOf]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  return (
    <Layout>
      <section className="pt-16 pb-24 bg-forest-bg relative">
        <FloatingElements count={8} type="mixed" className="absolute inset-0 z-0 pointer-events-none" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="mb-8">
            <Link href="/dogs" className="text-forest-dark hover:text-shiba-orange transition-colors flex items-center group">
              <svg className="w-5 h-5 mr-2 text-shiba-orange group-hover:translate-x-[-3px] transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              <span className="font-medium">Назад к списку собак</span>
            </Link>
          </div>

          <div className="flex items-center mb-6">
            <PawPrint size="sm" className="mr-3 text-shiba-orange" />
            <span className="text-shiba-orange font-medium tracking-wider uppercase text-sm">Профиль собаки</span>
          </div>

          {/* Профиль собаки в стиле соцсетей */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="relative h-48 md:h-64 bg-forest-bg/50">
              {/* Фоновое изображение профиля */}
              {dog.photos.length > 1 && (
                <div className="absolute inset-0 overflow-hidden">
                  <Image
                    src={dog.photos[1].url}
                    alt={`${dog.name} - фон`}
                    fill
                    style={{ objectFit: 'cover', objectPosition: 'center' }}
                    className="opacity-30"
                  />
                  <div className="absolute inset-0 bg-gradient-to-b from-forest-dark/30 to-forest-medium/50"></div>
                </div>
              )}

              {/* Аватар собаки */}
              <div className="absolute -bottom-16 left-8 w-32 h-32 md:w-40 md:h-40 rounded-full border-4 border-white shadow-lg overflow-hidden bg-white">
                {mainPhoto ? (
                  <Image
                    src={mainPhoto.url}
                    alt={dog.name}
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                ) : (
                  <div className="w-full h-full bg-forest-bg flex items-center justify-center">
                    <span className="text-forest-medium">Нет фото</span>
                  </div>
                )}
              </div>

              {/* Кнопки действий */}
              <div className="absolute bottom-4 right-4 flex space-x-2">
                {dog.isForSale && (
                  <div className="bg-shiba-orange text-white px-4 py-2 rounded-lg shadow-md font-medium flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clipRule="evenodd"></path>
                    </svg>
                    <span>Доступен для продажи</span>
                  </div>
                )}
                <ContactButton
                  subjectId={dog.id}
                  subjectType="dog"
                  subjectName={dog.name}
                  buttonText="Связаться"
                  className="bg-[#3e5641] text-white hover:bg-[#3e5641]/90 px-4 py-2 rounded-lg shadow-md font-medium"
                />
              </div>
            </div>

            {/* Информация о собаке */}
            <div className="pt-20 px-8 pb-8">
              <div className="mb-6">
                <div className="flex items-center">
                  <h1 className="text-3xl font-bold text-forest-dark">{dog.name}</h1>
                  <div className="ml-3 bg-forest-bg text-forest-dark text-sm font-bold px-3 py-1 rounded-full">
                    {dog.gender === 'MALE' ? 'Кобель' : 'Сука'}
                  </div>
                  {dog.price && dog.isForSale && (
                    <div className="ml-3 bg-shiba-orange/10 text-shiba-orange text-sm font-bold px-3 py-1 rounded-full">
                      {dog.price} руб.
                    </div>
                  )}
                </div>
                <p className="text-lg text-forest-medium mt-1">{dog.breed}</p>
              </div>

              {/* Основная информация и галерея */}
              <div className="grid md:grid-cols-12 gap-6 mt-8">
                <div className="md:col-span-5">
                  {/* Основная информация */}
                  <div className="bg-forest-bg/10 rounded-xl p-5 mb-6">
                    <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                      <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                      </svg>
                      <span>Информация</span>
                    </h3>

                    <div className="space-y-3">
                      <div className="flex items-center border-b border-forest-pale pb-2">
                        <span className="font-medium w-32 text-forest-dark text-sm">Дата рождения:</span>
                        <span className="text-forest-medium">{formatDate(dog.birthDate)} ({new Date().getFullYear() - new Date(dog.birthDate).getFullYear()} лет)</span>
                      </div>

                      {dog.color && (
                        <div className="flex items-center border-b border-forest-pale pb-2">
                          <span className="font-medium w-32 text-forest-dark text-sm">Окрас:</span>
                          <span className="text-forest-medium">{dog.color}</span>
                        </div>
                      )}

                      {dog.weight && (
                        <div className="flex items-center border-b border-forest-pale pb-2">
                          <span className="font-medium w-32 text-forest-dark text-sm">Вес:</span>
                          <span className="text-forest-medium">{dog.weight} кг</span>
                        </div>
                      )}

                      {dog.height && (
                        <div className="flex items-center border-b border-forest-pale pb-2">
                          <span className="font-medium w-32 text-forest-dark text-sm">Рост:</span>
                          <span className="text-forest-medium">{dog.height} см</span>
                        </div>
                      )}

                      {allBreedings.length > 0 && (
                        <div className="flex items-center border-b border-forest-pale pb-2">
                          <span className="font-medium w-32 text-forest-dark text-sm">Вязки:</span>
                          <span className="text-forest-medium">{allBreedings.length}</span>
                        </div>
                      )}

                      {dog.pedigree && (
                        <div className="flex items-center border-b border-forest-pale pb-2">
                          <span className="font-medium w-32 text-forest-dark text-sm">Родословная:</span>
                          <span className="text-forest-medium">{dog.pedigree}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Достижения */}
                  {dog.achievements && (
                    <div className="bg-forest-bg/10 rounded-xl p-5">
                      <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                        <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <span>Достижения</span>
                      </h3>

                      <div className="text-forest-medium whitespace-pre-line text-sm">
                        {dog.achievements}
                      </div>
                    </div>
                  )}
                </div>

                <div className="md:col-span-7">
                  {/* Галерея фотографий */}
                  <div className="bg-white rounded-xl overflow-hidden">
                    <DogGallery photos={dog.photos} name={dog.name} />
                  </div>

                  {/* Описание */}
                  {dog.description && (
                    <div className="mt-6 bg-forest-bg/10 rounded-xl p-5">
                      <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                        <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                        </svg>
                        <span>Описание</span>
                      </h3>

                      <div className="text-forest-medium whitespace-pre-line leading-relaxed">
                        {dog.description}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {allBreedings.length > 0 && (
            <div className="mt-12">
              <div className="flex items-center mb-6">
                <div className="h-px flex-grow bg-forest-light/20"></div>
                <div className="flex items-center mx-4">
                  <PawPrint size="sm" className="mr-3 text-shiba-orange" />
                  <h2 className="text-2xl font-bold text-forest-dark">Вязки и потомство</h2>
                </div>
                <div className="h-px flex-grow bg-forest-light/20"></div>
              </div>

              {/* Вкладки для вязок */}
              <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="border-b border-forest-pale">
                  <div className="flex">
                    <div className="px-6 py-3 border-b-2 border-shiba-orange text-shiba-orange font-medium">
                      Все вязки ({allBreedings.length})
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  {/* Карточки вязок в стиле соцсетей */}
                  <div className="space-y-6">
                    {allBreedings.map((breeding) => (
                      <div key={breeding.id} className="bg-white rounded-xl shadow-lg overflow-hidden">
                        {/* Карточка вязки в стиле соцсетей */}
                        <div className="p-6 border-b border-forest-pale/30">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div className="w-12 h-12 rounded-full bg-shiba-orange/10 flex items-center justify-center">
                                <svg className="w-6 h-6 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                </svg>
                              </div>
                              <div>
                                <h3 className="text-xl font-bold text-forest-dark">Вязка от {formatDate(breeding.date)}</h3>
                                <p className="text-forest-medium text-sm">
                                  {breeding.father.id === dog.id ? 'Участвует как отец' : 'Участвует как мать'}
                                </p>
                              </div>
                            </div>

                            <span className={`inline-block px-4 py-2 rounded-full text-sm font-bold shadow-md ${
                              breeding.status === 'COMPLETED'
                                ? 'bg-green-100 text-green-800'
                                : breeding.status === 'IN_PROGRESS'
                                ? 'bg-blue-100 text-blue-800'
                                : breeding.status === 'PLANNED'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {breeding.status === 'COMPLETED' ? 'Завершена' :
                               breeding.status === 'IN_PROGRESS' ? 'В процессе' :
                               breeding.status === 'PLANNED' ? 'Запланирована' : 'Отменена'}
                            </span>
                          </div>

                          {breeding.description && (
                            <div className="mt-4 text-forest-medium">
                              <p>{breeding.description}</p>
                            </div>
                          )}
                        </div>

                        {/* Родители */}
                        <div className="grid md:grid-cols-2 gap-0">
                          <Link
                            href={`/dogs/${breeding.father.slug}`}
                            className="p-6 hover:bg-forest-bg/10 transition-colors border-r border-b border-forest-pale/30"
                          >
                            <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                              <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path>
                              </svg>
                              <span>Отец</span>
                            </h3>

                            <div className="flex items-start">
                              {breeding.father.photos && breeding.father.photos.length > 0 ? (
                                <div className="relative w-20 h-20 rounded-full overflow-hidden mr-4 border-2 border-shiba-orange/20">
                                  <Image
                                    src={breeding.father.photos[0].url}
                                    alt={breeding.father.name}
                                    fill
                                    style={{ objectFit: 'cover' }}
                                  />
                                </div>
                              ) : (
                                <div className="w-20 h-20 rounded-full bg-forest-bg flex items-center justify-center mr-4 border-2 border-shiba-orange/20">
                                  <span className="text-forest-medium text-xs">Нет фото</span>
                                </div>
                              )}
                              <div>
                                <h4 className="font-bold text-forest-dark">{breeding.father.name}</h4>
                                <p className="text-forest-medium text-sm">{breeding.father.breed}</p>
                                <div className="flex items-center mt-2 text-forest-medium text-xs">
                                  <svg className="w-3 h-3 mr-1 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path>
                                  </svg>
                                  <span>
                                    Возраст: {new Date().getFullYear() - new Date(breeding.father.birthDate).getFullYear()} лет
                                  </span>
                                </div>
                              </div>
                            </div>

                            <div className="mt-3 text-forest-dark/70 text-xs flex items-center">
                              <span>Подробнее о собаке</span>
                              <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                              </svg>
                            </div>
                          </Link>

                          <Link
                            href={`/dogs/${breeding.mother.slug}`}
                            className="p-6 hover:bg-forest-bg/10 transition-colors border-b border-forest-pale/30"
                          >
                            <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                              <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path>
                              </svg>
                              <span>Мать</span>
                            </h3>

                            <div className="flex items-start">
                              {breeding.mother.photos && breeding.mother.photos.length > 0 ? (
                                <div className="relative w-20 h-20 rounded-full overflow-hidden mr-4 border-2 border-shiba-orange/20">
                                  <Image
                                    src={breeding.mother.photos[0].url}
                                    alt={breeding.mother.name}
                                    fill
                                    style={{ objectFit: 'cover' }}
                                  />
                                </div>
                              ) : (
                                <div className="w-20 h-20 rounded-full bg-forest-bg flex items-center justify-center mr-4 border-2 border-shiba-orange/20">
                                  <span className="text-forest-medium text-xs">Нет фото</span>
                                </div>
                              )}
                              <div>
                                <h4 className="font-bold text-forest-dark">{breeding.mother.name}</h4>
                                <p className="text-forest-medium text-sm">{breeding.mother.breed}</p>
                                <div className="flex items-center mt-2 text-forest-medium text-xs">
                                  <svg className="w-3 h-3 mr-1 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path>
                                  </svg>
                                  <span>
                                    Возраст: {new Date().getFullYear() - new Date(breeding.mother.birthDate).getFullYear()} лет
                                  </span>
                                </div>
                              </div>
                            </div>

                            <div className="mt-3 text-forest-dark/70 text-xs flex items-center">
                              <span>Подробнее о собаке</span>
                              <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                              </svg>
                            </div>
                          </Link>
                        </div>

                        {/* Щенки */}
                        {breeding.puppies.length > 0 && (
                          <div className="p-6 border-t border-forest-pale/30">
                            <h3 className="text-lg font-bold mb-4 text-forest-dark flex items-center">
                              <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
                              </svg>
                              <span>Щенки</span>
                              <span className="ml-2 text-sm text-forest-medium">({breeding.puppies.length})</span>
                            </h3>

                            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
                              {breeding.puppies.map((puppy: any) => (
                                <Link
                                  key={puppy.id}
                                  href={`/puppies/${puppy.slug}`}
                                  className="block bg-white hover:bg-forest-bg/10 p-2 rounded-lg transition-colors border border-forest-pale/30 hover:border-shiba-orange/30"
                                >
                                  <div className="text-center">
                                    <p className="font-medium text-forest-dark text-sm truncate">{puppy.name || 'Щенок'}</p>
                                    <div className="flex justify-center items-center mt-1 space-x-1">
                                      <span className="text-xs text-forest-medium">
                                        {puppy.gender === 'MALE' ? 'Кобель' : 'Сука'}
                                      </span>
                                      <span className="text-forest-light">•</span>
                                      <span className={`text-xs font-medium ${
                                        puppy.status === 'AVAILABLE'
                                          ? 'text-green-600'
                                          : puppy.status === 'RESERVED'
                                          ? 'text-orange-600'
                                          : 'text-red-600'
                                      }`}>
                                        {puppy.status === 'AVAILABLE' ? 'Доступен' :
                                         puppy.status === 'RESERVED' ? 'Резерв' : 'Продан'}
                                      </span>
                                    </div>
                                  </div>
                                </Link>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Информация о статусе вязки */}
                        {breeding.puppies.length === 0 && breeding.status !== 'CANCELLED' && (
                          <div className="p-6 border-t border-forest-pale/30">
                            <div className="flex items-center bg-forest-bg/10 p-4 rounded-lg">
                              <svg className="w-5 h-5 text-shiba-orange mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                              </svg>
                              <p className="text-forest-dark">
                                {breeding.status === 'PLANNED'
                                  ? 'Вязка запланирована. Щенки появятся в будущем.'
                                  : breeding.status === 'IN_PROGRESS'
                                  ? 'Вязка в процессе. Щенки скоро появятся.'
                                  : 'Информация о щенках будет добавлена позже.'}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="mt-12">
            <div className="flex items-center mb-6">
              <div className="h-px flex-grow btn-forest"></div>
              <div className="flex items-center mx-4">
                <Leaf size="sm" className="mr-3 text-shiba-orange" />
                <h2 className="text-2xl font-bold text-forest-dark">Связаться с нами</h2>
              </div>
              <div className="h-px flex-grow bg-forest-light"></div>
            </div>

            {/* Карточка контакта в стиле соцсетей */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="p-6 border-b border-forest-pale/30">
                <div className="flex items-center justify-center">
                  <div className="w-12 h-12 rounded-full bg-shiba-orange/10 flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-forest-dark">Интересует {dog.name}?</h3>
                    <p className="text-forest-medium text-sm">
                      Мы с радостью ответим на все ваши вопросы
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <p className="text-forest-medium mb-6">
                  Если вы заинтересованы в приобретении щенка от этой собаки или у вас есть вопросы, пожалуйста, свяжитесь с нами. Мы предоставим всю необходимую информацию.
                </p>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center p-3 bg-forest-bg/10 rounded-lg hover:bg-forest-bg/20 transition-colors">
                      <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                      </div>
                      <div>
                        <p className="text-xs text-forest-medium">Телефон</p>
                        <p className="text-forest-dark font-medium">{settings?.contactPhone || '+7 (XXX) XXX-XX-XX'}</p>
                      </div>
                    </div>

                    <div className="flex items-center p-3 bg-forest-bg/10 rounded-lg hover:bg-forest-bg/20 transition-colors">
                      <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                      </div>
                      <div>
                        <p className="text-xs text-forest-medium">Email</p>
                        <p className="text-forest-dark font-medium">{settings?.contactEmail || '<EMAIL>'}</p>
                      </div>
                    </div>

                    {settings?.address && (
                      <div className="flex items-center p-3 bg-forest-bg/10 rounded-lg hover:bg-forest-bg/20 transition-colors">
                        <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-3">
                          <svg className="w-5 h-5 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          </svg>
                        </div>
                        <div>
                          <p className="text-xs text-forest-medium">Адрес</p>
                          <p className="text-forest-dark font-medium">{settings?.address}</p>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="bg-forest-bg/10 rounded-lg p-5 flex flex-col justify-between">
                    <div>
                      <h4 className="font-bold text-forest-dark mb-2">Отправить запрос</h4>
                      <p className="text-forest-medium text-sm mb-4">
                        Заполните форму, и мы свяжемся с вами в ближайшее время
                      </p>
                    </div>

                    <div className="flex flex-col space-y-3">
                      <div className="flex space-x-2">
                        <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center flex-shrink-0">
                          <PawPrint size="sm" color="var(--shiba-orange)" />
                        </div>
                        <div className="bg-white rounded-lg p-3 flex-grow">
                          <p className="text-xs text-forest-medium">Запрос о</p>
                          <p className="text-forest-dark font-medium">{dog.name}</p>
                        </div>
                      </div>

                      <ContactButton
                        subjectId={dog.id}
                        subjectType="dog"
                        subjectName={dog.name}
                        buttonText="Отправить запрос"
                        className="w-full justify-center bg-[#3e5641] hover:bg-[#3e5641]/90 text-white font-medium py-3 rounded-lg transition-colors"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Социальные сети */}
              {(settings?.instagramUrl || settings?.facebookUrl || settings?.vkUrl) && (
                <div className="p-4 border-t border-forest-pale/30 bg-forest-bg/5">
                  <div className="flex items-center">
                    <p className="text-sm text-forest-medium mr-3">Мы в соцсетях:</p>
                    <div className="flex space-x-2">
                      {settings?.instagramUrl && (
                        <a href={settings.instagramUrl} target="_blank" rel="noopener noreferrer" className="w-8 h-8 rounded-full bg-white flex items-center justify-center text-forest-medium hover:text-shiba-orange transition-colors">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                          </svg>
                        </a>
                      )}
                      {settings?.facebookUrl && (
                        <a href={settings.facebookUrl} target="_blank" rel="noopener noreferrer" className="w-8 h-8 rounded-full bg-white flex items-center justify-center text-forest-medium hover:text-shiba-orange transition-colors">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z"/>
                          </svg>
                        </a>
                      )}
                      {settings?.vkUrl && (
                        <a href={settings.vkUrl} target="_blank" rel="noopener noreferrer" className="w-8 h-8 rounded-full bg-white flex items-center justify-center text-forest-medium hover:text-shiba-orange transition-colors">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21.547 7h-3.29a.743.743 0 0 0-.655.392s-1.312 2.416-1.734 3.23c-1.43 2.78-2.006 2.913-2.238 2.745-.546-.401-.41-1.618-.41-2.48V7.708c0-.655-.187-.915-.74-.915H9.161a.652.652 0 0 0-.672.601c0 .628.937.773 1.03 2.543v3.845c0 .84-.151.995-.486.995-.892 0-3.057-3.21-4.34-6.885-.252-.673-.507-.903-1.172-.903H.9c-.75 0-.9.345-.9.731 0 .682.892 4.073 4.148 8.553C6.318 20.25 9.374 22 12.153 22c1.671 0 1.875-.368 1.875-1.003v-2.313c0-.737.158-.884.687-.884.39 0 1.057.192 2.615 1.667 1.78 1.75 2.073 2.533 3.075 2.533h3.29c.75 0 1.126-.368.91-1.096-.238-.724-1.088-1.775-2.215-3.022-.612-.71-1.53-1.475-1.809-1.858-.389-.491-.278-.71 0-1.147 0 0 2.948-4.070 3.254-5.456.153-.684 0-1.18-.7-1.18z"/>
                          </svg>
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="absolute left-0 bottom-0 w-32 h-32 opacity-10">
          <PawPrint size="lg" />
        </div>
        <div className="absolute right-10 top-40 w-16 h-16 opacity-10">
          <Leaf size="md" />
        </div>
      </section>
    </Layout>
  );
}
