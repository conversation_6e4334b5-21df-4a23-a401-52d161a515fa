import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  // Создаем администратора
  const adminPassword = await bcrypt.hash('admin123', 10);
  
  const admin = await prisma.admin.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Администратор',
      email: '<EMAIL>',
      password: adminPassword,
    },
  });
  
  console.log({ admin });
  
  // Создаем тестовые данные для собак
  const dog1 = await prisma.dog.upsert({
    where: { slug: 'rex' },
    update: {},
    create: {
      name: 'Рекс',
      breed: 'Немецкая овчарка',
      gender: 'MALE',
      birthDate: new Date('2020-05-15'),
      color: 'Черно-подпалый',
      weight: 35.5,
      height: 65.0,
      pedigree: 'RKF123456',
      achievements: 'Чемпион России',
      description: 'Рекс - прекрасный представитель породы немецкая овчарка. Обладает отличным характером и экстерьером.',
      slug: 'rex',
      isForSale: false,
    },
  });
  
  const dog2 = await prisma.dog.upsert({
    where: { slug: 'bella' },
    update: {},
    create: {
      name: 'Белла',
      breed: 'Лабрадор',
      gender: 'FEMALE',
      birthDate: new Date('2021-03-10'),
      color: 'Палевый',
      weight: 28.0,
      height: 56.0,
      pedigree: 'RKF654321',
      achievements: 'Юный чемпион России',
      description: 'Белла - красивая и умная собака породы лабрадор. Отлично ладит с детьми и другими животными.',
      slug: 'bella',
      isForSale: true,
      price: 80000,
    },
  });
  
  console.log({ dog1, dog2 });
  
  // Создаем тестовые данные для щенков
  const puppy1 = await prisma.puppy.upsert({
    where: { slug: 'puppy-1' },
    update: {},
    create: {
      name: 'Щенок 1',
      gender: 'MALE',
      birthDate: new Date('2023-04-15'),
      color: 'Черно-подпалый',
      description: 'Красивый и здоровый щенок немецкой овчарки.',
      slug: 'puppy-1',
      status: 'AVAILABLE',
      price: 50000,
    },
  });
  
  const puppy2 = await prisma.puppy.upsert({
    where: { slug: 'puppy-2' },
    update: {},
    create: {
      name: 'Щенок 2',
      gender: 'FEMALE',
      birthDate: new Date('2023-04-15'),
      color: 'Палевый',
      description: 'Красивый и здоровый щенок лабрадора.',
      slug: 'puppy-2',
      status: 'AVAILABLE',
      price: 45000,
    },
  });
  
  console.log({ puppy1, puppy2 });
  
  // Создаем тестовые данные для новостей
  const news1 = await prisma.news.upsert({
    where: { slug: 'success-at-international-exhibition' },
    update: {},
    create: {
      title: 'Успешное выступление на международной выставке',
      content: 'Наши собаки заняли призовые места на международной выставке в Москве. Рекс получил титул CAC, а Белла - CACIB.',
      excerpt: 'Наши собаки заняли призовые места на международной выставке в Москве.',
      slug: 'success-at-international-exhibition',
    },
  });
  
  const news2 = await prisma.news.upsert({
    where: { slug: 'new-litter-of-puppies' },
    update: {},
    create: {
      title: 'Новый помет щенков',
      content: 'У нас родились новые щенки породы Лабрадор. Все щенки здоровы и готовы к бронированию. В помете 6 щенков: 3 кобеля и 3 суки.',
      excerpt: 'У нас родились новые щенки породы Лабрадор. Все щенки здоровы и готовы к бронированию.',
      slug: 'new-litter-of-puppies',
    },
  });
  
  console.log({ news1, news2 });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
