# Полное руководство по развертыванию Dogs Website

## Обзор

Это руководство поможет вам развернуть ваш Dogs Website для доступа из интернета через nginx и DuckDNS на Windows.

## Предварительные требования

- Windows 10/11
- Node.js (версия 18 или выше)
- PostgreSQL
- nginx для Windows
- Доступ к интернету с публичным IP

## Пошаговое развертывание

### 1. Подготовка приложения

```powershell
# Установите зависимости
npm install

# Создайте продакшен сборку
npm run build

# Настройте переменные окружения
# Отредактируйте .env.production
```

### 2. Настройка DuckDNS

1. Зарегистрируйтесь на [DuckDNS.org](https://www.duckdns.org)
2. Создайте домен (например: `mydogs.duckdns.org`)
3. Настройте автообновление IP:

```powershell
# Отредактируйте duckdns-update.ps1 с вашими данными
.\duckdns-update.ps1

# Настройте автозапуск через планировщик задач
```

### 3. Настройка nginx

```powershell
# Скопируйте конфигурацию nginx
# Отредактируйте nginx-dogs-site.conf с вашим доменом
# Поместите файл в C:\nginx\conf\conf.d\

# Проверьте конфигурацию
C:\nginx\nginx.exe -t

# Запустите nginx
C:\nginx\nginx.exe
```

### 4. Настройка SSL сертификата

```powershell
# Установите Certbot
# Получите SSL сертификат
.\ssl-setup.ps1 -Domain "yourdomain.duckdns.org" -Email "<EMAIL>"

# Настройте автообновление
# Создайте задачу в планировщике для ssl-renew.ps1
```

### 5. Настройка брандмауэра

```powershell
# Автоматическая настройка
.\setup-firewall.ps1

# Настройте проброс портов в роутере:
# Порт 80 -> 80 (HTTP)
# Порт 443 -> 443 (HTTPS)
```

### 6. Настройка автозапуска

Выберите один из вариантов:

#### Вариант A: Windows Service (Рекомендуется)
```powershell
# Установите NSSM
# Создайте службу
.\create-service.ps1
```

#### Вариант B: Автозагрузка
```powershell
# Настройте автозапуск
.\setup-autostart.ps1
```

### 7. Тестирование системы

```powershell
# Комплексное тестирование
.\full-system-test.ps1 -Domain "yourdomain.duckdns.org"

# Тестирование портов
.\test-ports.ps1 -Domain "yourdomain.duckdns.org" -External
```

## Структура файлов

```
dogs/
├── .env.production              # Продакшен переменные
├── nginx-dogs-site.conf         # Конфигурация nginx
├── start-production.bat         # Запуск в продакшене
├── start-production.ps1         # PowerShell версия
├── duckdns-update.ps1          # Обновление DuckDNS
├── ssl-setup.ps1               # Настройка SSL
├── ssl-renew.ps1               # Обновление SSL
├── setup-firewall.ps1          # Настройка брандмауэра
├── create-service.ps1          # Создание службы
├── setup-autostart.ps1         # Настройка автозапуска
├── test-ports.ps1              # Тестирование портов
├── full-system-test.ps1        # Полное тестирование
└── docs/
    ├── nginx-setup-instructions.md
    ├── duckdns-setup-instructions.md
    ├── ssl-setup-instructions.md
    ├── autostart-setup-instructions.md
    └── firewall-setup-instructions.md
```

## Быстрый старт

1. **Настройте DuckDNS:**
   ```powershell
   # Отредактируйте duckdns-update.ps1
   .\duckdns-update.ps1
   ```

2. **Настройте nginx:**
   ```powershell
   # Отредактируйте nginx-dogs-site.conf
   # Скопируйте в C:\nginx\conf\conf.d\
   C:\nginx\nginx.exe -t
   C:\nginx\nginx.exe
   ```

3. **Настройте брандмауэр:**
   ```powershell
   .\setup-firewall.ps1
   ```

4. **Получите SSL сертификат:**
   ```powershell
   .\ssl-setup.ps1 -Domain "yourdomain.duckdns.org"
   ```

5. **Запустите приложение:**
   ```powershell
   .\start-production.ps1
   ```

6. **Протестируйте:**
   ```powershell
   .\full-system-test.ps1 -Domain "yourdomain.duckdns.org"
   ```

## Мониторинг и обслуживание

### Ежедневные проверки:
- Доступность сайта
- Статус служб
- Логи ошибок

### Еженедельные задачи:
- Проверка обновлений безопасности
- Анализ логов nginx
- Проверка использования ресурсов

### Ежемесячные задачи:
- Обновление SSL сертификатов (автоматически)
- Резервное копирование базы данных
- Обновление зависимостей

## Устранение проблем

### Сайт недоступен:
1. Проверьте статус служб
2. Проверьте логи nginx
3. Проверьте DuckDNS резолюцию
4. Проверьте брандмауэр

### SSL ошибки:
1. Проверьте срок действия сертификата
2. Обновите сертификат
3. Проверьте nginx конфигурацию

### Проблемы с базой данных:
1. Проверьте статус PostgreSQL
2. Проверьте подключение
3. Проверьте логи приложения

## Безопасность

- Регулярно обновляйте все компоненты
- Мониторьте логи на подозрительную активность
- Используйте сильные пароли
- Настройте резервное копирование
- Ограничьте доступ к административным функциям

## Поддержка

При возникновении проблем:
1. Запустите диагностические скрипты
2. Проверьте логи всех компонентов
3. Обратитесь к документации по конкретным компонентам
4. Используйте онлайн-инструменты для диагностики SSL и DNS

## Полезные команды

```powershell
# Проверка статуса служб
Get-Service | Where-Object { $_.Name -like "*nginx*" -or $_.Name -like "*Dogs*" }

# Проверка портов
netstat -an | findstr ":80\|:443\|:3000"

# Проверка процессов
Get-Process | Where-Object { $_.Name -like "*nginx*" -or $_.Name -like "*node*" }

# Перезапуск nginx
C:\nginx\nginx.exe -s reload

# Проверка SSL сертификата
certbot certificates
```
