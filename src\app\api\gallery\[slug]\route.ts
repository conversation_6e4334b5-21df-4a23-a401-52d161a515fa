import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/gallery/[slug] - Получение информации о конкретном альбоме
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;

    const album = await prisma.gallery.findUnique({
      where: { slug },
      include: {
        category: true,
        photos: {
          orderBy: {
            order: 'asc'
          }
        }
      }
    });

    if (!album) {
      return NextResponse.json(
        { error: 'Альбом не найден' },
        { status: 404 }
      );
    }

    return NextResponse.json(album);
  } catch (error) {
    console.error('Ошибка при получении информации об альбоме:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении информации об альбоме' },
      { status: 500 }
    );
  }
}

// PUT /api/gallery/[slug] - Обновление информации об альбоме (защищенный маршрут)
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const slug = params.slug;
    const data = await request.json();

    // Проверяем, существует ли альбом
    const existingAlbum = await prisma.gallery.findUnique({
      where: { slug }
    });

    if (!existingAlbum) {
      return NextResponse.json(
        { error: 'Альбом не найден' },
        { status: 404 }
      );
    }

    // Проверяем, существует ли категория, если она указана
    if (data.categoryId) {
      const category = await prisma.galleryCategory.findUnique({
        where: { id: data.categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'Указанная категория не найдена' },
          { status: 400 }
        );
      }
    }

    // Обновляем информацию об альбоме
    const updatedAlbum = await prisma.gallery.update({
      where: { slug },
      data: {
        title: data.title,
        description: data.description,
        coverImage: data.coverImage,
        order: data.order !== undefined ? data.order : existingAlbum.order,
        isPublished: data.isPublished !== undefined ? data.isPublished : existingAlbum.isPublished,
        categoryId: data.categoryId === '' ? null : data.categoryId
      }
    });

    return NextResponse.json(updatedAlbum);
  } catch (error) {
    console.error('Ошибка при обновлении информации об альбоме:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении информации об альбоме' },
      { status: 500 }
    );
  }
}

// DELETE /api/gallery/[slug] - Удаление альбома (защищенный маршрут)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const slug = params.slug;

    // Проверяем, существует ли альбом
    const existingAlbum = await prisma.gallery.findUnique({
      where: { slug }
    });

    if (!existingAlbum) {
      return NextResponse.json(
        { error: 'Альбом не найден' },
        { status: 404 }
      );
    }

    // Удаляем альбом
    await prisma.gallery.delete({
      where: { slug }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении альбома:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении альбома' },
      { status: 500 }
    );
  }
}
