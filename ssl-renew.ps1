# SSL Certificate Renewal Script
# Run this script monthly to renew SSL certificates

param(
    [string]$NginxPath = "C:\nginx",
    [string]$LogFile = "ssl-renew.log"
)

$Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

function Write-Log {
    param([string]$Message)
    $LogEntry = "$Timestamp - $Message"
    Write-Host $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry
}

Write-Log "Starting SSL certificate renewal process"

try {
    # Check if certificates need renewal
    Write-Log "Checking certificate expiration..."
    
    $RenewOutput = & certbot renew --dry-run 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Log "Dry run successful. Proceeding with actual renewal..."
        
        # Stop nginx
        Write-Log "Stopping nginx..."
        & "$NginxPath\nginx.exe" -s stop
        Start-Sleep -Seconds 2
        
        # Renew certificates
        Write-Log "Renewing certificates..."
        $RenewResult = & certbot renew --standalone 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Certificate renewal successful"
            
            # Test nginx configuration
            Write-Log "Testing nginx configuration..."
            $TestResult = & "$NginxPath\nginx.exe" -t 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Write-Log "Nginx configuration test passed"
                
                # Start nginx
                Write-Log "Starting nginx..."
                & "$NginxPath\nginx.exe"
                
                Write-Log "SSL certificate renewal completed successfully"
            } else {
                Write-Log "Nginx configuration test failed: $TestResult"
                Write-Log "Starting nginx with old configuration..."
                & "$NginxPath\nginx.exe"
            }
        } else {
            Write-Log "Certificate renewal failed: $RenewResult"
            Write-Log "Starting nginx..."
            & "$NginxPath\nginx.exe"
        }
    } else {
        Write-Log "Dry run failed or no renewal needed: $RenewOutput"
    }
} catch {
    Write-Log "Error during renewal process: $($_.Exception.Message)"
    Write-Log "Attempting to start nginx..."
    & "$NginxPath\nginx.exe"
}

Write-Log "SSL renewal process completed"
