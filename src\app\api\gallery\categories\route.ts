import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/gallery/categories - Получение списка категорий
export async function GET(request: NextRequest) {
  try {
    const categories = await prisma.galleryCategory.findMany({
      orderBy: [
        { order: 'asc' },
        { name: 'asc' }
      ]
    });
    
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Ошибка при получении списка категорий:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении списка категорий' },
      { status: 500 }
    );
  }
}

// POST /api/gallery/categories - Создание новой категории (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const data = await request.json();
    
    // Создаем новую категорию
    const category = await prisma.galleryCategory.create({
      data: {
        name: data.name,
        description: data.description,
        slug: data.slug,
        order: data.order || 0
      }
    });
    
    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    console.error('Ошибка при создании категории:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании категории' },
      { status: 500 }
    );
  }
}
