'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Textarea from '@/components/ui/Textarea';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { formatDate, formatFullDate, getInquiryStatusText } from '@/lib/utils';

interface Comment {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  authorName: string;
}

interface Inquiry {
  id: string;
  name: string;
  email: string;
  phone: string;
  message: string;
  status: 'NEW' | 'IN_PROGRESS' | 'COMPLETED' | 'REJECTED';
  createdAt: string;
  updatedAt: string;
  dogId?: string | null;
  puppyId?: string | null;
  dog?: {
    id: string;
    name: string;
    breed: string;
    slug: string;
  } | null;
  puppy?: {
    id: string;
    name: string;
    slug: string;
    breeding?: {
      mother: {
        breed: string;
      };
    } | null;
  } | null;
  comments: Comment[];
}

export default function InquiryDetailsPage({ params }: { params: { id: string } }) {
  const [inquiry, setInquiry] = useState<Inquiry | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [comment, setComment] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchInquiry = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/inquiries/${params.id}`);
        
        if (!response.ok) {
          throw new Error('Ошибка при загрузке данных');
        }
        
        const data = await response.json();
        setInquiry(data);
      } catch (err) {
        setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
        console.error('Ошибка при загрузке заявки:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInquiry();
  }, [params.id]);

  const handleAddComment = async () => {
    if (!inquiry || !comment.trim()) return;
    
    setIsSubmittingComment(true);
    
    try {
      const response = await fetch(`/api/inquiries/${inquiry.id}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: comment }),
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при добавлении комментария');
      }
      
      // Обновляем данные на странице
      const updatedInquiryResponse = await fetch(`/api/inquiries/${params.id}`);
      if (updatedInquiryResponse.ok) {
        const updatedInquiry = await updatedInquiryResponse.json();
        setInquiry(updatedInquiry);
        setComment('');
      }
    } catch (err) {
      setError('Произошла ошибка при добавлении комментария. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при добавлении комментария:', err);
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const handleUpdateStatus = async (newStatus: string) => {
    if (!inquiry) return;
    
    setIsUpdatingStatus(true);
    
    try {
      const response = await fetch(`/api/inquiries/${inquiry.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при обновлении статуса');
      }
      
      // Обновляем данные на странице
      const updatedInquiryResponse = await fetch(`/api/inquiries/${params.id}`);
      if (updatedInquiryResponse.ok) {
        const updatedInquiry = await updatedInquiryResponse.json();
        setInquiry(updatedInquiry);
      }
    } catch (err) {
      setError('Произошла ошибка при обновлении статуса. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при обновлении статуса:', err);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-blue-100 text-blue-800';
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error || !inquiry) {
    return (
      <AdminLayout title="Ошибка">
        <Alert type="error">{error || 'Заявка не найдена'}</Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/inquiries')}>
            Вернуться к списку заявок
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={`Заявка от ${inquiry.name}`}>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            Дата создания: {formatFullDate(inquiry.createdAt)}
          </p>
        </div>
        <div>
          <Button
            variant="secondary"
            onClick={() => router.push('/admin/inquiries')}
          >
            Вернуться к списку
          </Button>
        </div>
      </div>

      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Информация о заявке</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Статус</p>
                <p>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(
                      inquiry.status
                    )}`}
                  >
                    {getInquiryStatusText(inquiry.status)}
                  </span>
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">ID</p>
                <p className="font-mono text-sm">{inquiry.id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Создано</p>
                <p>{formatDate(inquiry.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Обновлено</p>
                <p>{formatDate(inquiry.updatedAt)}</p>
              </div>
            </div>
          </div>

          <div className="mt-6 bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Контактная информация</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Имя</p>
                <p className="font-medium">{inquiry.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Email</p>
                <p>
                  <a href={`mailto:${inquiry.email}`} className="text-blue-600 hover:text-blue-800">
                    {inquiry.email}
                  </a>
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Телефон</p>
                <p>
                  <a href={`tel:${inquiry.phone}`} className="text-blue-600 hover:text-blue-800">
                    {inquiry.phone}
                  </a>
                </p>
              </div>
            </div>
          </div>

          {(inquiry.dog || inquiry.puppy) && (
            <div className="mt-6 bg-white shadow-md rounded-lg p-6">
              <h2 className="text-lg font-medium mb-4">Связанный объект</h2>
              <div className="space-y-4">
                {inquiry.dog && (
                  <div>
                    <p className="text-sm text-gray-500">Собака</p>
                    <p className="font-medium">{inquiry.dog.name}</p>
                    <p className="text-sm text-gray-600">{inquiry.dog.breed}</p>
                    <Link 
                      href={`/admin/dogs/${inquiry.dog.slug}`}
                      className="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block"
                    >
                      Просмотреть
                    </Link>
                  </div>
                )}
                {inquiry.puppy && (
                  <div>
                    <p className="text-sm text-gray-500">Щенок</p>
                    <p className="font-medium">{inquiry.puppy.name}</p>
                    <p className="text-sm text-gray-600">
                      {inquiry.puppy.breeding?.mother.breed || 'Порода не указана'}
                    </p>
                    <Link 
                      href={`/admin/puppies/${inquiry.puppy.slug}`}
                      className="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block"
                    >
                      Просмотреть
                    </Link>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="md:col-span-2">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Сообщение</h2>
            <div className="p-4 bg-gray-50 rounded-md whitespace-pre-line">
              {inquiry.message}
            </div>
          </div>

          <div className="mt-6 bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Управление статусом</h2>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="primary"
                disabled={inquiry.status === 'IN_PROGRESS' || isUpdatingStatus}
                onClick={() => handleUpdateStatus('IN_PROGRESS')}
              >
                В обработку
              </Button>
              <Button
                variant="success"
                disabled={inquiry.status === 'COMPLETED' || isUpdatingStatus}
                onClick={() => handleUpdateStatus('COMPLETED')}
              >
                Завершить
              </Button>
              <Button
                variant="danger"
                disabled={inquiry.status === 'REJECTED' || isUpdatingStatus}
                onClick={() => handleUpdateStatus('REJECTED')}
              >
                Отклонить
              </Button>
            </div>
          </div>

          <div className="mt-6 bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Комментарии ({inquiry.comments.length})</h2>
            
            {inquiry.comments.length > 0 ? (
              <div className="space-y-4 mb-6">
                {inquiry.comments.map((comment) => (
                  <div key={comment.id} className="p-4 bg-gray-50 rounded-md">
                    <div className="flex justify-between items-start mb-2">
                      <p className="font-medium">{comment.authorName}</p>
                      <p className="text-sm text-gray-500">{formatDate(comment.createdAt)}</p>
                    </div>
                    <p className="whitespace-pre-line">{comment.content}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 mb-6">Комментариев пока нет</p>
            )}
            
            <div className="space-y-4">
              <Textarea
                label="Добавить комментарий"
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                rows={3}
                fullWidth
              />
              <div className="flex justify-end">
                <Button
                  onClick={handleAddComment}
                  disabled={!comment.trim() || isSubmittingComment}
                  isLoading={isSubmittingComment}
                >
                  Добавить комментарий
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
