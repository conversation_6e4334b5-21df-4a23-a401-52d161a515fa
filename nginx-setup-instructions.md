# Инструкция по настройке Nginx для Dogs Website

## 1. Установка конфигурации

1. Найдите папку установки nginx (обычно `C:\nginx` или `C:\Program Files\nginx`)
2. Скопируйте файл `nginx-dogs-site.conf` в папку `conf\conf.d\` 
3. Если папки `conf.d` нет, создайте её

## 2. Обновление основного конфигурационного файла nginx

Откройте файл `nginx.conf` в папке `conf\` и убедитесь, что в секции `http` есть строка:

```nginx
include conf.d/*.conf;
```

Если её нет, добавьте её в секцию `http`.

## 3. Замена домена

В файле `nginx-dogs-site.conf` замените `yourdomain.duckdns.org` на ваш реальный домен DuckDNS.

## 4. Проверка конфигурации

Откройте командную строку от имени администратора и выполните:

```cmd
cd C:\nginx
nginx -t
```

Если конфигурация корректна, вы увидите сообщение об успешной проверке.

## 5. Перезапуск nginx

```cmd
nginx -s reload
```

Или остановите и запустите nginx заново:

```cmd
nginx -s stop
nginx
```

## 6. Проверка работы

После настройки DuckDNS и SSL сертификатов, ваш сайт будет доступен по адресу:
- HTTP: http://yourdomain.duckdns.org (будет перенаправлен на HTTPS)
- HTTPS: https://yourdomain.duckdns.org

## Примечания

- Убедитесь, что Next.js приложение запущено на порту 3000
- Порты 80 и 443 должны быть открыты в брандмауэре
- SSL сертификаты будут настроены на следующем этапе
