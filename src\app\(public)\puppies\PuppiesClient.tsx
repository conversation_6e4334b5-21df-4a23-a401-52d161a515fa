'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';
import { PawPrint, Leaf } from '@/components/ui/Decorations';

type PuppyStatus = 'AVAILABLE' | 'RESERVED' | 'SOLD';

interface Photo {
  id: string;
  url: string;
  isMain: boolean;
}

interface Dog {
  id: string;
  name: string;
  breed: string;
  slug: string;
}

interface Breeding {
  id: string;
  father: <PERSON>;
  mother: <PERSON>;
}

interface Puppy {
  id: string;
  name: string;
  slug: string;
  birthDate: Date;
  gender: 'MALE' | 'FEMALE';
  color: string | null;
  status: PuppyStatus;
  price: number | null;
  photos: Photo[];
  breeding: Breeding | null;
}

interface Settings {
  id: string;
  key: string;
  value: string;
}

interface PuppiesClientProps {
  puppies: Puppy[];
  settings: Settings | null;
}

type StatusFilterType = 'ALL' | 'AVAILABLE' | 'RESERVED';
type GenderFilterType = 'ALL' | 'MALE' | 'FEMALE';

export default function PuppiesClient({ puppies, settings }: PuppiesClientProps) {
  const [statusFilter, setStatusFilter] = useState<StatusFilterType>('ALL');
  const [genderFilter, setGenderFilter] = useState<GenderFilterType>('ALL');

  // Фильтруем щенков по статусу и полу
  const filteredPuppies = puppies.filter(puppy => {
    // Проверка по статусу
    if (statusFilter !== 'ALL' && puppy.status !== statusFilter) {
      return false;
    }

    // Проверка по полу
    if (genderFilter !== 'ALL' && puppy.gender !== genderFilter) {
      return false;
    }

    return true;
  });

  return (
    <section className="pt-16 pb-24 bg-forest-bg relative">
      <div className="container mx-auto px-4 relative z-10">
        <div className="flex items-center justify-center mb-6">
          <PawPrint size="sm" className="mr-3 text-shiba-orange" />
          <span className="text-shiba-orange font-medium tracking-wider uppercase text-sm">Наш питомник</span>
        </div>

        <h1 className="text-3xl md:text-4xl font-bold text-center text-forest-dark mb-6">Щенки на продажу</h1>

        <div className="max-w-3xl mx-auto text-center mb-12">
          <p className="text-lg text-forest-medium">
            Все наши щенки выращены с любовью и заботой. Они имеют документы и прививки по возрасту.
          </p>
        </div>

        {/* Фильтры в одну строку */}
        <div className="mb-10">
          <div className="flex flex-wrap justify-center gap-3">
            <button
              className={`px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all text-sm font-medium ${
                statusFilter === 'ALL' && genderFilter === 'ALL'
                  ? 'bg-white text-forest-dark'
                  : 'bg-white/50 text-forest-medium hover:bg-white hover:text-forest-dark'
              }`}
              onClick={() => {
                setStatusFilter('ALL');
                setGenderFilter('ALL');
              }}
            >
              Все щенки
            </button>
            <button
              className={`px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all text-sm font-medium ${
                statusFilter === 'AVAILABLE' && genderFilter === 'ALL'
                  ? 'bg-white text-forest-dark'
                  : 'bg-white/50 text-forest-medium hover:bg-white hover:text-forest-dark'
              }`}
              onClick={() => {
                setStatusFilter('AVAILABLE');
                setGenderFilter('ALL');
              }}
            >
              Доступны для продажи
            </button>
            <button
              className={`px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all text-sm font-medium ${
                statusFilter === 'RESERVED' && genderFilter === 'ALL'
                  ? 'bg-white text-forest-dark'
                  : 'bg-white/50 text-forest-medium hover:bg-white hover:text-forest-dark'
              }`}
              onClick={() => {
                setStatusFilter('RESERVED');
                setGenderFilter('ALL');
              }}
            >
              Зарезервированы
            </button>
            <button
              className={`px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all text-sm font-medium ${
                statusFilter === 'ALL' && genderFilter === 'MALE'
                  ? 'bg-white text-forest-dark'
                  : 'bg-white/50 text-forest-medium hover:bg-white hover:text-forest-dark'
              }`}
              onClick={() => {
                setStatusFilter('ALL');
                setGenderFilter('MALE');
              }}
            >
              Кобели
            </button>
            <button
              className={`px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all text-sm font-medium ${
                statusFilter === 'ALL' && genderFilter === 'FEMALE'
                  ? 'bg-white text-forest-dark'
                  : 'bg-white/50 text-forest-medium hover:bg-white hover:text-forest-dark'
              }`}
              onClick={() => {
                setStatusFilter('ALL');
                setGenderFilter('FEMALE');
              }}
            >
              Суки
            </button>

            {/* Кнопка сброса фильтров */}
            {(statusFilter !== 'ALL' || genderFilter !== 'ALL') && (
              <button
                className="px-4 py-2 rounded-full text-shiba-orange bg-white/50 hover:bg-white text-sm flex items-center transition-colors"
                onClick={() => {
                  setStatusFilter('ALL');
                  setGenderFilter('ALL');
                }}
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Сбросить
              </button>
            )}
          </div>
        </div>

        {/* Карточки щенков */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPuppies.length > 0 ? (
            filteredPuppies.map((puppy) => {
              // Находим главное фото или используем первое доступное
              const mainPhoto = puppy.photos.find(photo => photo.isMain) || puppy.photos[0];
              const birthDate = new Date(puppy.birthDate);
              const today = new Date();
              const ageInMonths = (today.getFullYear() - birthDate.getFullYear()) * 12 + today.getMonth() - birthDate.getMonth();

              return (
                <Link
                  key={puppy.id}
                  href={`/puppies/${puppy.slug}`}
                  className="group"
                >
                  <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform group-hover:translate-y-[-5px]">
                    <div className="relative h-72">
                      {mainPhoto ? (
                        <>
                          <Image
                            src={mainPhoto.url}
                            alt={puppy.name || 'Щенок'}
                            fill
                            style={{ objectFit: 'cover' }}
                            className="transition-transform duration-500 group-hover:scale-105"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          <div className="absolute top-2 right-2">
                            <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold ${
                              puppy.status === 'AVAILABLE'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-orange-100 text-orange-800'
                            }`}>
                              {puppy.status === 'AVAILABLE' ? 'Доступен' : 'Зарезервирован'}
                            </span>
                          </div>
                          <div className="absolute bottom-0 left-0 right-0 p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="flex gap-2">
                              <span className="bg-white/80 text-forest-dark text-xs font-medium px-2 py-1 rounded-full">
                                {ageInMonths} {ageInMonths === 1 ? 'месяц' : ageInMonths < 5 ? 'месяца' : 'месяцев'}
                              </span>
                              <span className="bg-white/80 text-forest-dark text-xs font-medium px-2 py-1 rounded-full">
                                {puppy.gender === 'MALE' ? 'Кобель' : 'Сука'}
                              </span>
                            </div>
                          </div>
                        </>
                      ) : (
                        <div className="w-full h-full bg-forest-bg flex items-center justify-center">
                          <div className="text-center">
                            <svg className="w-12 h-12 mx-auto text-forest-light mb-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
                            </svg>
                            <span className="text-forest-medium">Нет фото</span>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="p-6">
                      <div className="flex justify-between items-start mb-2">
                        <h2 className="text-xl font-bold text-forest-dark">{puppy.name || 'Щенок'}</h2>
                        {puppy.breeding?.father.breed && (
                          <div className="bg-forest-bg/20 text-forest-dark text-xs font-medium px-2 py-1 rounded-full">
                            {puppy.breeding.father.breed}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center text-forest-medium text-sm mb-4">
                        <svg className="w-4 h-4 mr-1 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path>
                        </svg>
                        <span>Дата рождения: {formatDate(puppy.birthDate)}</span>
                      </div>

                      {puppy.breeding && (
                        <div className="mb-4 text-forest-medium text-sm">
                          <p className="font-medium text-forest-dark mb-1">Родители:</p>
                          <div className="flex flex-col space-y-1">
                            <div className="flex items-center">
                              <span className="w-12 text-xs">Отец:</span>
                              <span className="text-xs">{puppy.breeding.father.name}</span>
                            </div>
                            <div className="flex items-center">
                              <span className="w-12 text-xs">Мать:</span>
                              <span className="text-xs">{puppy.breeding.mother.name}</span>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="flex justify-between items-center">
                        <div className="flex gap-2">
                          {puppy.color && (
                            <div className="bg-forest-bg/20 text-forest-dark text-xs px-2 py-1 rounded-full">
                              {puppy.color}
                            </div>
                          )}
                        </div>

                        <div className="flex items-center">
                          {puppy.price && puppy.status === 'AVAILABLE' && (
                            <span className="font-bold text-shiba-orange mr-3"></span>
                          )}
                          <span className="text-forest-dark font-medium text-sm group-hover:text-shiba-orange transition-colors flex items-center">
                            Подробнее
                            <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })
          ) : (
            <div className="col-span-full bg-white rounded-xl shadow-md p-12 text-center">
              <div className="w-20 h-20 mx-auto bg-forest-bg/20 rounded-full flex items-center justify-center mb-4">
                <PawPrint size="md" className="text-forest-light" />
              </div>
              <h3 className="text-xl font-bold text-forest-dark mb-2">Щенки не найдены</h3>
              <p className="text-forest-medium">
                В данный момент нет
                {genderFilter !== 'ALL' ? (genderFilter === 'MALE' ? ' кобелей' : ' сук') : ' щенков'}
                {statusFilter !== 'ALL' ? (statusFilter === 'AVAILABLE' ? ' доступных для продажи' : ' зарезервированных') : ''}
                {genderFilter === 'ALL' && statusFilter === 'ALL' ? ' по заданным критериям' : ''}
              </p>
              <p className="text-forest-medium mt-2">Пожалуйста, свяжитесь с нами, чтобы узнать о планируемых пометах</p>
            </div>
          )}
        </div>

        {/* Контактная информация */}
        <div className="mt-16 bg-white rounded-xl shadow-lg p-8 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-40 h-40 bg-shiba-orange/5 rounded-bl-full"></div>
          <div className="absolute bottom-0 left-0 w-32 h-32 bg-forest-light/5 rounded-tr-full"></div>

          <div className="text-center relative z-10 max-w-2xl mx-auto">
            <Leaf size="sm" className="mx-auto text-shiba-orange mb-4" />
            <h2 className="text-2xl font-bold text-forest-dark mb-4">Интересуетесь щенками?</h2>
            <p className="text-forest-medium mb-6">
              Если вы заинтересованы в приобретении щенка или хотите узнать о планируемых пометах,
              пожалуйста, свяжитесь с нами. Мы с радостью ответим на все ваши вопросы.
            </p>

            <div className="flex flex-wrap justify-center gap-4">
              <a
                href={`tel:${settings?.value || '+7XXXXXXXXXX'}`}
                className="flex items-center bg-forest-bg/10 hover:bg-forest-bg/20 px-4 py-2 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span className="text-forest-dark">{settings?.value || '+7 (XXX) XXX-XX-XX'}</span>
              </a>

              <Link
                href="/contact"
                className="flex items-center bg-[#3e5641] hover:bg-[#3e5641]/90 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span>Связаться с нами</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
