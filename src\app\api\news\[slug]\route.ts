import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/news/[slug] - Получение информации о конкретной новости
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;
    
    const news = await prisma.news.findUnique({
      where: { slug },
      include: {
        photos: {
          orderBy: {
            order: 'asc'
          }
        }
      }
    });
    
    if (!news) {
      return NextResponse.json(
        { error: 'Новость не найдена' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(news);
  } catch (error) {
    console.error('Ошибка при получении информации о новости:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении информации о новости' },
      { status: 500 }
    );
  }
}

// PUT /api/news/[slug] - Обновление информации о новости (защищенный маршрут)
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const slug = params.slug;
    const data = await request.json();
    
    // Проверяем, существует ли новость
    const existingNews = await prisma.news.findUnique({
      where: { slug }
    });
    
    if (!existingNews) {
      return NextResponse.json(
        { error: 'Новость не найдена' },
        { status: 404 }
      );
    }
    
    // Обновляем информацию о новости
    const updatedNews = await prisma.news.update({
      where: { slug },
      data: {
        title: data.title,
        content: data.content,
        excerpt: data.excerpt,
        isPublished: data.isPublished
      }
    });
    
    return NextResponse.json(updatedNews);
  } catch (error) {
    console.error('Ошибка при обновлении информации о новости:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении информации о новости' },
      { status: 500 }
    );
  }
}

// DELETE /api/news/[slug] - Удаление новости (защищенный маршрут)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const slug = params.slug;
    
    // Проверяем, существует ли новость
    const existingNews = await prisma.news.findUnique({
      where: { slug }
    });
    
    if (!existingNews) {
      return NextResponse.json(
        { error: 'Новость не найдена' },
        { status: 404 }
      );
    }
    
    // Удаляем новость
    await prisma.news.delete({
      where: { slug }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении новости:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении новости' },
      { status: 500 }
    );
  }
}
