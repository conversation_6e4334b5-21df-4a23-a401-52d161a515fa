# Setup autostart for Dogs Website without Windows Service
# This creates startup scripts and shortcuts

param(
    [string]$AppPath = $PSScriptRoot
)

Write-Host "Setting up autostart for Dogs Website..." -ForegroundColor Green

# Create startup script
$StartupScript = @"
@echo off
cd /d "$AppPath"
echo Starting Dogs Website...
set NODE_ENV=production
npm run start
pause
"@

$StartupScriptPath = Join-Path $AppPath "startup-dogs-website.bat"
Set-Content -Path $StartupScriptPath -Value $StartupScript

Write-Host "Created startup script: $StartupScriptPath" -ForegroundColor Yellow

# Create PowerShell startup script (hidden window)
$PowerShellScript = @"
# Dogs Website Startup Script
Set-Location "$AppPath"
`$env:NODE_ENV = "production"

# Start the application in background
Start-Process -FilePath "npm" -ArgumentList "run", "start" -WindowStyle Hidden -WorkingDirectory "$AppPath"

Write-Host "Dogs Website started successfully!" -ForegroundColor Green
"@

$PowerShellScriptPath = Join-Path $AppPath "startup-dogs-website.ps1"
Set-Content -Path $PowerShellScriptPath -Value $PowerShellScript

Write-Host "Created PowerShell startup script: $PowerShellScriptPath" -ForegroundColor Yellow

# Get startup folder path
$StartupFolder = [Environment]::GetFolderPath("Startup")
Write-Host "Startup folder: $StartupFolder" -ForegroundColor Cyan

# Create shortcut in startup folder
$WshShell = New-Object -ComObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut((Join-Path $StartupFolder "Dogs Website.lnk"))
$Shortcut.TargetPath = "powershell.exe"
$Shortcut.Arguments = "-WindowStyle Hidden -ExecutionPolicy Bypass -File `"$PowerShellScriptPath`""
$Shortcut.WorkingDirectory = $AppPath
$Shortcut.Description = "Dogs Website Auto Startup"
$Shortcut.Save()

Write-Host "Created startup shortcut in: $StartupFolder" -ForegroundColor Green

# Create manual start/stop scripts
$StartScript = @"
@echo off
echo Starting Dogs Website...
cd /d "$AppPath"
set NODE_ENV=production
start "Dogs Website" npm run start
echo Dogs Website started!
pause
"@

$StopScript = @"
@echo off
echo Stopping Dogs Website...
taskkill /f /im node.exe
echo Dogs Website stopped!
pause
"@

Set-Content -Path (Join-Path $AppPath "start-dogs-website.bat") -Value $StartScript
Set-Content -Path (Join-Path $AppPath "stop-dogs-website.bat") -Value $StopScript

Write-Host "`nCreated management scripts:" -ForegroundColor Cyan
Write-Host "- start-dogs-website.bat (manual start)" -ForegroundColor White
Write-Host "- stop-dogs-website.bat (manual stop)" -ForegroundColor White
Write-Host "- startup-dogs-website.bat (startup script)" -ForegroundColor White

Write-Host "`nAutostart setup completed!" -ForegroundColor Green
Write-Host "The application will start automatically when Windows boots." -ForegroundColor Yellow
Write-Host "To disable autostart, delete the shortcut from: $StartupFolder" -ForegroundColor Yellow
