#!/usr/bin/env node

const { exec } = require('child_process');
const dns = require('dns');
const https = require('https');
const http = require('http');

const DUCKDNS_DOMAIN = 'ta-shiba.duckdns.org';
const LOCAL_IP = '*************';
const NGINX_PORT = 8080;

// Функция для получения внешнего IP
function getExternalIP() {
  return new Promise((resolve, reject) => {
    https.get('https://api.ipify.org', (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => resolve(data.trim()));
    }).on('error', reject);
  });
}

// Функция для получения IP домена
function getDomainIP(domain) {
  return new Promise((resolve, reject) => {
    dns.lookup(domain, (err, address) => {
      if (err) reject(err);
      else resolve(address);
    });
  });
}

// Функция для проверки HTTP соединения
function checkHTTP(url) {
  return new Promise((resolve, reject) => {
    const request = http.get(url, { timeout: 5000 }, (res) => {
      resolve({
        status: res.statusCode,
        success: res.statusCode >= 200 && res.statusCode < 400
      });
    });
    
    request.on('error', (err) => {
      resolve({
        status: 'ERROR',
        success: false,
        error: err.message
      });
    });
    
    request.on('timeout', () => {
      request.destroy();
      resolve({
        status: 'TIMEOUT',
        success: false,
        error: 'Connection timeout'
      });
    });
  });
}

// Основная функция диагностики
async function diagnoseConnection() {
  console.log('🔍 Диагностика подключения Dogs Website...\n');
  
  try {
    // 1. Проверка DNS
    console.log('📡 1. Проверка DNS...');
    const externalIP = await getExternalIP();
    const domainIP = await getDomainIP(DUCKDNS_DOMAIN);
    
    console.log(`   Ваш внешний IP: ${externalIP}`);
    console.log(`   IP домена: ${domainIP}`);
    
    if (externalIP === domainIP) {
      console.log('   ✅ DNS настроен правильно\n');
    } else {
      console.log('   ❌ DNS настроен неправильно\n');
      return;
    }
    
    // 2. Проверка локальных соединений
    console.log('🏠 2. Проверка локальных соединений...');
    
    const localhost3000 = await checkHTTP('http://localhost:3000');
    console.log(`   localhost:3000 - ${localhost3000.success ? '✅' : '❌'} (${localhost3000.status})`);
    
    const localhost8080 = await checkHTTP('http://localhost:8080');
    console.log(`   localhost:8080 - ${localhost8080.success ? '✅' : '❌'} (${localhost8080.status})`);
    
    const localIP3000 = await checkHTTP(`http://${LOCAL_IP}:3000`);
    console.log(`   ${LOCAL_IP}:3000 - ${localIP3000.success ? '✅' : '❌'} (${localIP3000.status})`);
    
    const localIP8080 = await checkHTTP(`http://${LOCAL_IP}:8080`);
    console.log(`   ${LOCAL_IP}:8080 - ${localIP8080.success ? '✅' : '❌'} (${localIP8080.status})\n`);
    
    // 3. Проверка внешнего соединения
    console.log('🌐 3. Проверка внешнего соединения...');
    
    const external = await checkHTTP(`http://${DUCKDNS_DOMAIN}:${NGINX_PORT}`);
    console.log(`   ${DUCKDNS_DOMAIN}:${NGINX_PORT} - ${external.success ? '✅' : '❌'} (${external.status})`);
    
    if (external.error) {
      console.log(`   Ошибка: ${external.error}`);
    }
    
    // 4. Анализ результатов
    console.log('\n📊 Анализ результатов:');
    
    if (localhost3000.success && localhost8080.success) {
      console.log('   ✅ Локальные сервисы работают');
    } else {
      console.log('   ❌ Проблемы с локальными сервисами');
    }
    
    if (localIP3000.success && localIP8080.success) {
      console.log('   ✅ Сервисы доступны в локальной сети');
    } else {
      console.log('   ❌ Проблемы с доступом в локальной сети');
    }
    
    if (!external.success) {
      console.log('   ❌ Сайт недоступен из интернета');
      console.log('\n🔧 Возможные причины:');
      
      if (external.status === 'TIMEOUT' || external.error?.includes('timeout')) {
        console.log('   • Порт 8080 не проброшен в роутере');
        console.log('   • Windows Firewall блокирует соединения');
        console.log('   • Провайдер блокирует входящие соединения');
      } else if (external.status === 503) {
        console.log('   • Nginx не может подключиться к Next.js');
        console.log('   • Проблема с конфигурацией nginx');
      } else if (external.status === 'ERROR') {
        console.log('   • Сетевая ошибка');
        console.log('   • DNS проблемы');
      }
      
      console.log('\n💡 Что нужно сделать:');
      console.log('   1. Запустите setup-firewall.ps1 от имени администратора');
      console.log('   2. Настройте проброс порта 8080 в роутере');
      console.log('   3. Проверьте, что nginx и Next.js запущены');
    } else {
      console.log('   ✅ Сайт доступен из интернета!');
      console.log(`\n🎉 Ваш сайт работает: http://${DUCKDNS_DOMAIN}:${NGINX_PORT}`);
    }
    
  } catch (error) {
    console.error('\n❌ Ошибка при диагностике:');
    console.error(`   ${error.message}`);
  }
}

// Запуск диагностики
diagnoseConnection();
