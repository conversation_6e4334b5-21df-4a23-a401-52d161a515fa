# 🌐 Отображение URL-адресов Dogs Website

## Что изменилось

Теперь при запуске `npm run start` и `npm run dev` вы увидите все доступные адреса для доступа к сайту, включая внешний адрес через DuckDNS!

## 🚀 Новые команды

### Основные команды
```bash
# Запуск в режиме разработки (с красивыми URL)
npm run dev

# Запуск продакшн сервера (с красивыми URL)
npm run start

# Проверка статуса DuckDNS
npm run check:duckdns
```

### Оригинальные команды (без кастомного вывода)
```bash
# Оригинальный dev режим
npm run dev:original

# Оригинальный start
npm run start:original
```

## 📋 Что показывается при запуске

### При запуске `npm run dev`:
```
🚀 Dogs Website (Development) запущен!

📍 Локальные адреса:
   ├─ Локальный:     http://localhost:3000
   └─ В сети:        http://*************:3000

🌐 Внешние адреса (через nginx):
   ├─ Локальный:     http://localhost:8080
   ├─ В сети:        http://*************:8080
   └─ Интернет:      http://ta-shiba.duckdns.org:8080

🔒 HTTPS адреса (после настройки SSL):
   └─ Интернет:      https://ta-shiba.duckdns.org

💡 Советы:
   • Для разработки используйте http://localhost:3000
   • Hot reload включен - изменения применяются автоматически
   • Для тестирования в продакшн режиме: npm run build && npm run start
```

### При запуске `npm run start`:
```
🚀 Dogs Website запущен!

📍 Локальные адреса:
   ├─ Локальный:     http://localhost:3000
   └─ В сети:        http://*************:3000

🌐 Внешние адреса (через nginx):
   ├─ Локальный:     http://localhost:8080
   ├─ В сети:        http://*************:8080
   └─ Интернет:      http://ta-shiba.duckdns.org:8080

🔒 HTTPS адреса (после настройки SSL):
   └─ Интернет:      https://ta-shiba.duckdns.org
```

## 🔍 Проверка DuckDNS

Команда `npm run check:duckdns` покажет:

### Если DuckDNS настроен правильно:
```
🔍 Проверка статуса DuckDNS...

📡 Получение внешнего IP адреса...
   Ваш внешний IP: 123.456.789.012

🌐 Проверка DNS записи...
   IP домена ta-shiba.duckdns.org: 123.456.789.012

📊 Результат:
   ✅ DuckDNS настроен правильно!
   ✅ Домен указывает на ваш IP адрес

🌍 Ваш сайт должен быть доступен по адресу:
   http://ta-shiba.duckdns.org:8080
```

### Если DuckDNS настроен неправильно:
```
📊 Результат:
   ❌ DuckDNS настроен неправильно
   ❌ Домен указывает на другой IP адрес

🔧 Что нужно сделать:
   1. Обновите DuckDNS с помощью: .\duckdns-update.ps1
   2. Подождите 1-2 минуты для обновления DNS
   3. Запустите эту проверку снова
```

## 📁 Новые файлы

- `scripts/dev-with-urls.js` - кастомный скрипт для dev режима
- `scripts/start-with-urls.js` - кастомный скрипт для продакшн режима
- `scripts/check-duckdns.js` - скрипт проверки DuckDNS

## 🔧 Настройка

Если ваш DuckDNS домен отличается от `ta-shiba.duckdns.org`, отредактируйте файлы:
- `scripts/dev-with-urls.js`
- `scripts/start-with-urls.js`
- `scripts/check-duckdns.js`

Найдите строку:
```javascript
const DUCKDNS_DOMAIN = 'ta-shiba.duckdns.org';
```

И замените на ваш домен.

## 🌍 Доступ к сайту

### Для разработки:
- **Локально**: http://localhost:3000
- **В локальной сети**: http://[ваш-IP]:3000

### Для продакшн (через nginx):
- **Локально**: http://localhost:8080
- **В локальной сети**: http://[ваш-IP]:8080
- **Из интернета**: http://ta-shiba.duckdns.org:8080

### После настройки SSL:
- **Из интернета**: https://ta-shiba.duckdns.org

## 💡 Полезные советы

1. **Для разработки** всегда используйте порт 3000 (прямое подключение к Next.js)
2. **Для тестирования** используйте порт 8080 (через nginx)
3. **Для продакшн** настройте SSL и используйте HTTPS
4. **Проверяйте DuckDNS** регулярно с помощью `npm run check:duckdns`

Теперь вы всегда будете видеть, как попасть на ваш сайт из интернета! 🎉
