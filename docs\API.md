# 🔌 API Документация

## Обзор

Сайт питомника предоставляет REST API для управления данными. Все административные эндпоинты требуют аутентификации.

## 🔐 Аутентификация

### Вход в систему
```http
POST /api/auth/signin
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

### Выход из системы
```http
POST /api/auth/signout
```

### Проверка сессии
```http
GET /api/auth/session
```

## 🐕 API Собак

### Получение всех собак
```http
GET /api/dogs
```

**Параметры запроса:**
- `search` - поиск по кличке
- `gender` - фильтр по полу (MALE/FEMALE)
- `isGraduate` - фильтр выпускников (true/false)

**Пример:**
```http
GET /api/dogs?search=Акира&gender=FEMALE&isGraduate=false
```

**Ответ:**
```json
[
  {
    "id": "clp123456",
    "name": "Акира",
    "breed": "Сиба-ину",
    "gender": "FEMALE",
    "birthDate": "2023-01-15T00:00:00.000Z",
    "color": "Рыжий",
    "weight": 8.5,
    "height": 35,
    "description": "Активная и дружелюбная собака",
    "images": ["/uploads/dogs/akira1.jpg"],
    "isGraduate": false,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
]
```

### Получение собаки по ID
```http
GET /api/dogs/[id]
```

### Создание собаки (требует аутентификации)
```http
POST /api/dogs
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Хачико",
  "gender": "MALE",
  "birthDate": "2023-03-20",
  "color": "Кунжутный",
  "weight": 12.0,
  "height": 40,
  "description": "Спокойный и уравновешенный кобель",
  "isGraduate": false
}
```

### Обновление собаки (требует аутентификации)
```http
PUT /api/dogs/[id]
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Хачико",
  "weight": 12.5,
  "description": "Обновленное описание"
}
```

### Удаление собаки (требует аутентификации)
```http
DELETE /api/dogs/[id]
Authorization: Bearer <token>
```

## 🐶 API Щенков

### Получение всех щенков
```http
GET /api/puppies
```

**Параметры запроса:**
- `available` - только доступные (true/false)
- `gender` - фильтр по полу
- `minPrice` - минимальная цена
- `maxPrice` - максимальная цена

**Пример:**
```http
GET /api/puppies?available=true&gender=MALE&minPrice=50000&maxPrice=100000
```

### Создание щенка (требует аутентификации)
```http
POST /api/puppies
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Юки",
  "gender": "FEMALE",
  "birthDate": "2024-01-10",
  "color": "Белый",
  "price": 75000,
  "isAvailable": true,
  "description": "Красивая девочка с отличным характером",
  "parentMale": "Хачико",
  "parentFemale": "Сакура"
}
```

## 📰 API Новостей

### Получение всех новостей
```http
GET /api/news
```

**Параметры запроса:**
- `published` - только опубликованные (true/false)
- `limit` - количество записей
- `offset` - смещение для пагинации

### Получение новости по slug
```http
GET /api/news/[slug]
```

### Создание новости (требует аутентификации)
```http
POST /api/news
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Новые щенки родились!",
  "content": "<p>Полный HTML контент статьи</p>",
  "excerpt": "Краткое описание новости",
  "slug": "novye-shchenki-rodilis",
  "isPublished": true,
  "publishedAt": "2024-01-15T10:00:00.000Z"
}
```

## 🎨 API Hero-слайдов

### Получение всех слайдов
```http
GET /api/hero-slides
```

### Создание слайда (требует аутентификации)
```http
POST /api/hero-slides
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Добро пожаловать в наш питомник",
  "subtitle": "Лучшие собаки породы Сиба-ину",
  "imageUrl": "/uploads/hero-slides/slide1.jpg",
  "buttonText": "Смотреть собак",
  "buttonLink": "/dogs",
  "isActive": true
}
```

### Обновление порядка слайдов (требует аутентификации)
```http
PUT /api/hero-slides
Authorization: Bearer <token>
Content-Type: application/json

{
  "slides": [
    {"id": "slide1", "order": 0},
    {"id": "slide2", "order": 1},
    {"id": "slide3", "order": 2}
  ]
}
```

## ⚙️ API Настроек

### Получение всех настроек
```http
GET /api/settings
```

**Ответ:**
```json
{
  "siteName": "Питомник Сиба-ину",
  "logo": "/uploads/logo.png",
  "contactEmail": "<EMAIL>",
  "contactPhone": "+7 (999) 123-45-67",
  "address": "Москва, ул. Примерная, 123"
}
```

### Обновление настроек (требует аутентификации)
```http
PUT /api/settings
Authorization: Bearer <token>
Content-Type: application/json

{
  "siteName": "Новое название питомника",
  "contactEmail": "<EMAIL>"
}
```

## 📁 API Загрузки файлов

### Загрузка изображения собаки (требует аутентификации)
```http
POST /api/dogs/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <image-file>
```

### Загрузка изображения щенка (требует аутентификации)
```http
POST /api/puppies/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <image-file>
```

### Загрузка изображения для настроек (требует аутентификации)
```http
POST /api/settings/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <image-file>
type: logo|favicon|aboutImage
```

### Загрузка изображения для hero-слайда (требует аутентификации)
```http
POST /api/hero-slides/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <image-file>
```

## 👥 API Пользователей

### Получение всех пользователей (требует аутентификации)
```http
GET /api/users
Authorization: Bearer <token>
```

### Создание пользователя (требует аутентификации)
```http
POST /api/users
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Новый пользователь",
  "email": "<EMAIL>",
  "password": "securepassword",
  "role": "USER"
}
```

## 📊 API Статистики

### Получение общей статистики (требует аутентификации)
```http
GET /api/stats
Authorization: Bearer <token>
```

**Ответ:**
```json
{
  "totalDogs": 25,
  "totalPuppies": 8,
  "availablePuppies": 5,
  "publishedNews": 12,
  "totalUsers": 3,
  "graduateDogs": 15
}
```

## 🔍 API Поиска

### Глобальный поиск
```http
GET /api/search?q=поисковый+запрос
```

**Ответ:**
```json
{
  "dogs": [...],
  "puppies": [...],
  "news": [...]
}
```

## ❌ Коды ошибок

### HTTP статус коды
- `200` - Успешный запрос
- `201` - Ресурс создан
- `400` - Неверный запрос
- `401` - Не авторизован
- `403` - Доступ запрещен
- `404` - Ресурс не найден
- `500` - Внутренняя ошибка сервера

### Формат ошибок
```json
{
  "error": "Описание ошибки",
  "code": "ERROR_CODE",
  "details": {
    "field": "Дополнительная информация"
  }
}
```

## 📝 Примеры использования

### JavaScript/Fetch
```javascript
// Получение списка собак
const response = await fetch('/api/dogs');
const dogs = await response.json();

// Создание новой собаки (с аутентификацией)
const response = await fetch('/api/dogs', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    name: 'Новая собака',
    gender: 'MALE',
    birthDate: '2023-01-01',
    color: 'Рыжий'
  })
});
```

### cURL
```bash
# Получение списка щенков
curl -X GET "https://yourdomain.com/api/puppies?available=true"

# Создание новости
curl -X POST "https://yourdomain.com/api/news" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "Новая статья",
    "content": "Содержание статьи",
    "isPublished": true
  }'
```

### Python/Requests
```python
import requests

# Получение данных
response = requests.get('https://yourdomain.com/api/dogs')
dogs = response.json()

# Создание с аутентификацией
headers = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {token}'
}
data = {
    'name': 'Новая собака',
    'gender': 'MALE',
    'birthDate': '2023-01-01',
    'color': 'Рыжий'
}
response = requests.post('https://yourdomain.com/api/dogs', 
                        headers=headers, json=data)
```

## 🔒 Безопасность API

### Рекомендации
- Всегда используйте HTTPS в production
- Проверяйте токены аутентификации
- Валидируйте входные данные
- Ограничивайте размер загружаемых файлов
- Используйте rate limiting для предотвращения злоупотреблений

### Rate Limiting
По умолчанию ограничений нет, но рекомендуется добавить:
- 100 запросов в минуту для чтения
- 10 запросов в минуту для записи
- 5 запросов в минуту для загрузки файлов

---

**Эта документация поможет вам эффективно использовать API сайта питомника! 🚀**
