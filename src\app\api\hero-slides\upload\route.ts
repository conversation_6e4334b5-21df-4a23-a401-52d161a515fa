import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { writeFile } from 'fs/promises';
import { join } from 'path';

// POST /api/hero-slides/upload - Загрузка изображения для hero-слайда (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'Файл не найден' },
        { status: 400 }
      );
    }

    // Проверяем тип файла
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Файл должен быть изображением' },
        { status: 400 }
      );
    }

    // Проверяем размер файла (максимум 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'Размер файла не должен превышать 10MB' },
        { status: 400 }
      );
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Генерируем уникальное имя файла
    const timestamp = Date.now();
    const originalName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const fileName = `hero-slide-${timestamp}-${originalName}`;

    // Путь для сохранения файла
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'hero-slides');
    const filePath = join(uploadDir, fileName);

    // Создаем директорию если она не существует
    const fs = require('fs');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Сохраняем файл
    await writeFile(filePath, buffer);

    // Возвращаем URL файла
    const fileUrl = `/uploads/hero-slides/${fileName}`;

    return NextResponse.json({
      url: fileUrl,
      fileName: fileName,
      originalName: file.name,
      size: file.size,
      type: file.type
    });
  } catch (error) {
    console.error('Ошибка при загрузке изображения hero-слайда:', error);
    return NextResponse.json(
      { error: 'Ошибка при загрузке изображения' },
      { status: 500 }
    );
  }
}
