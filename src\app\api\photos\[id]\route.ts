import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { unlink } from 'fs/promises';
import path from 'path';

// GET /api/photos/[id] - Получение информации о фотографии
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    const photo = await prisma.photo.findUnique({
      where: { id }
    });
    
    if (!photo) {
      return NextResponse.json(
        { error: 'Фотография не найдена' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(photo);
  } catch (error) {
    console.error('Ошибка при получении информации о фотографии:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении информации о фотографии' },
      { status: 500 }
    );
  }
}

// PUT /api/photos/[id] - Обновление информации о фотографии (защищенный маршрут)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const id = params.id;
    const data = await request.json();
    
    // Проверяем, существует ли фотография
    const existingPhoto = await prisma.photo.findUnique({
      where: { id }
    });
    
    if (!existingPhoto) {
      return NextResponse.json(
        { error: 'Фотография не найдена' },
        { status: 404 }
      );
    }
    
    // Обновляем информацию о фотографии
    const updatedPhoto = await prisma.photo.update({
      where: { id },
      data: {
        title: data.title,
        description: data.description,
        order: data.order,
        isMain: data.isMain
      }
    });
    
    // Если фото отмечено как главное, обновляем другие фото
    if (data.isMain) {
      if (existingPhoto.galleryId) {
        await prisma.photo.updateMany({
          where: {
            galleryId: existingPhoto.galleryId,
            id: { not: id }
          },
          data: { isMain: false }
        });
      } else if (existingPhoto.dogId) {
        await prisma.photo.updateMany({
          where: {
            dogId: existingPhoto.dogId,
            id: { not: id }
          },
          data: { isMain: false }
        });
      } else if (existingPhoto.puppyId) {
        await prisma.photo.updateMany({
          where: {
            puppyId: existingPhoto.puppyId,
            id: { not: id }
          },
          data: { isMain: false }
        });
      } else if (existingPhoto.newsId) {
        await prisma.photo.updateMany({
          where: {
            newsId: existingPhoto.newsId,
            id: { not: id }
          },
          data: { isMain: false }
        });
      }
    }
    
    return NextResponse.json(updatedPhoto);
  } catch (error) {
    console.error('Ошибка при обновлении информации о фотографии:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении информации о фотографии' },
      { status: 500 }
    );
  }
}

// DELETE /api/photos/[id] - Удаление фотографии (защищенный маршрут)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const id = params.id;
    
    // Проверяем, существует ли фотография
    const existingPhoto = await prisma.photo.findUnique({
      where: { id }
    });
    
    if (!existingPhoto) {
      return NextResponse.json(
        { error: 'Фотография не найдена' },
        { status: 404 }
      );
    }
    
    // Удаляем файл с сервера
    try {
      const filePath = path.join(process.cwd(), 'public', existingPhoto.url);
      await unlink(filePath);
    } catch (error) {
      console.error('Ошибка при удалении файла:', error);
      // Продолжаем удаление записи из базы данных, даже если файл не удалось удалить
    }
    
    // Удаляем запись из базы данных
    await prisma.photo.delete({
      where: { id }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении фотографии:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении фотографии' },
      { status: 500 }
    );
  }
}
