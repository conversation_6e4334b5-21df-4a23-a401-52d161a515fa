import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/news - Получение списка новостей
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Получаем общее количество новостей
    const total = await prisma.news.count({
      where: { isPublished: true }
    });

    // Проверяем, является ли запрос от админ-панели
    const isAdmin = request.headers.get('x-admin-request') === 'true';

    // Получаем новости с пагинацией
    const news = await prisma.news.findMany({
      where: isAdmin ? {} : { isPublished: true },
      include: {
        photos: {
          where: { isMain: true },
          take: 1
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    return NextResponse.json({
      news,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Ошибка при получении списка новостей:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении списка новостей' },
      { status: 500 }
    );
  }
}

// POST /api/news - Создание новой новости (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Создаем новую новость
    const news = await prisma.news.create({
      data: {
        title: data.title,
        content: data.content,
        excerpt: data.excerpt,
        slug: data.slug,
        isPublished: data.isPublished || true
      }
    });

    return NextResponse.json(news, { status: 201 });
  } catch (error) {
    console.error('Ошибка при создании новости:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании новости' },
      { status: 500 }
    );
  }
}
