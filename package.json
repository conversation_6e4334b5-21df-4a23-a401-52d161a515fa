{"name": "dogs", "version": "0.1.0", "private": true, "scripts": {"dev": "node scripts/dev-with-urls.js", "dev:original": "next dev", "build": "next build", "start": "node scripts/start-with-urls.js", "start:original": "next start", "lint": "next lint", "db:backup": "node scripts/export-data.js", "db:restore": "node scripts/import-data.js", "db:full-backup": "node scripts/full-backup.js", "db:test-restore": "node scripts/test-restore.js", "db:pull": "npx prisma db pull", "db:push": "npx prisma db push", "db:migrate": "npx prisma migrate dev", "db:reset": "npx prisma migrate reset", "check:duckdns": "node scripts/check-duckdns.js", "check:urls": "node scripts/check-duckdns.js"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@types/uuid": "^10.0.0", "bcrypt": "^6.0.0", "next": "15.3.3", "next-auth": "^4.24.11", "prisma": "^6.10.1", "react": "^19.1.0", "react-dom": "^19.0.0", "sharp": "^0.34.2", "ts-node": "^10.9.2", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^20.19.1", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}