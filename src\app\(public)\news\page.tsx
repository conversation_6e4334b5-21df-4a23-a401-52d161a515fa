import Image from 'next/image';
import Link from 'next/link';
import Layout from '@/components/layout/Layout';
import { prisma } from '@/lib/db';
import { formatDate } from '@/lib/utils';
import { PawPrint, Leaf, FloatingElements } from '@/components/ui/Decorations';

export const metadata = {
  title: 'Новости - Питомник собак',
  description: 'Последние новости и события нашего питомника собак.',
};

export default async function NewsPage() {
  // Получаем список новостей
  const news = await prisma.news.findMany({
    where: {
      isPublished: true,
    },
    include: {
      photos: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  return (
    <Layout>
      <section className="pt-16 pb-24 bg-forest-bg relative">
        <FloatingElements count={6} type="mixed" className="absolute inset-0 z-0 pointer-events-none" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex items-center justify-center mb-6">
            <PawPrint size="sm" className="mr-3 text-shiba-orange" />
            <span className="text-shiba-orange font-medium tracking-wider uppercase text-sm">Наш питомник</span>
          </div>

          <h1 className="text-3xl md:text-4xl font-bold text-center text-forest-dark mb-6">Новости</h1>

          <div className="max-w-3xl mx-auto text-center mb-12">
            <p className="text-lg text-forest-medium">
              Последние новости и события нашего питомника собак.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {news.length > 0 ? (
              news.map((item) => {
                // Находим главное фото или используем первое доступное
                const mainPhoto = item.photos.find(photo => photo.isMain) || item.photos[0];

                return (
                  <Link
                    key={item.id}
                    href={`/news/${item.slug}`}
                    className="group"
                  >
                    <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform group-hover:translate-y-[-5px]">
                      <div className="relative h-56">
                        {mainPhoto ? (
                          <>
                            <Image
                              src={mainPhoto.url}
                              alt={item.title}
                              fill
                              style={{ objectFit: 'cover' }}
                              className="transition-transform duration-500 group-hover:scale-105"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          </>
                        ) : (
                          <div className="w-full h-full bg-forest-bg flex items-center justify-center">
                            <div className="text-center">
                              <svg className="w-12 h-12 mx-auto text-forest-light mb-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
                              </svg>
                              <span className="text-forest-medium">Нет изображения</span>
                            </div>
                          </div>
                        )}
                        <div className="absolute top-3 left-3">
                          <span className="bg-white/80 text-forest-dark text-xs font-medium px-3 py-1 rounded-full shadow-sm">
                            {formatDate(item.createdAt)}
                          </span>
                        </div>
                      </div>
                      <div className="p-6">
                        <h2 className="text-xl font-bold text-forest-dark mb-3 group-hover:text-shiba-orange transition-colors">{item.title}</h2>
                        <p className="text-forest-medium mb-4 text-sm line-clamp-3">
                          {item.excerpt || (item.content && item.content.length > 150
                            ? `${item.content.substring(0, 150)}...`
                            : item.content)}
                        </p>
                        <div className="flex justify-end">
                          <span className="text-forest-dark font-medium text-sm group-hover:text-shiba-orange transition-colors flex items-center">
                            Читать далее
                            <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                );
              })
            ) : (
              <div className="col-span-full bg-white rounded-xl shadow-md p-12 text-center">
                <div className="w-20 h-20 mx-auto bg-forest-bg/20 rounded-full flex items-center justify-center mb-4">
                  <Leaf size="md" className="text-forest-light" />
                </div>
                <h3 className="text-xl font-bold text-forest-dark mb-2">Новости не найдены</h3>
                <p className="text-forest-medium">В данный момент нет опубликованных новостей</p>
                <p className="text-forest-medium mt-2">Пожалуйста, загляните позже</p>
              </div>
            )}
          </div>

          <div className="absolute left-0 bottom-0 w-32 h-32 opacity-10">
            <PawPrint size="lg" />
          </div>
          <div className="absolute right-10 top-40 w-16 h-16 opacity-10">
            <Leaf size="md" />
          </div>
        </div>
      </section>
    </Layout>
  );
}
