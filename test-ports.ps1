# Port Testing Script for Dogs Website
# Tests if required ports are open and accessible

param(
    [string]$Domain = "localhost",
    [switch]$External
)

Write-Host "Testing port accessibility for Dogs Website..." -ForegroundColor Green

# Ports to test
$Ports = @(
    @{ Port = 80; Name = "HTTP"; Required = $true },
    @{ Port = 443; Name = "HTTPS"; Required = $true },
    @{ Port = 3000; Name = "Next.js"; Required = $true },
    @{ Port = 5432; Name = "PostgreSQL"; Required = $false }
)

function Test-Port {
    param(
        [string]$ComputerName,
        [int]$Port,
        [int]$TimeoutMs = 3000
    )
    
    try {
        $TcpClient = New-Object System.Net.Sockets.TcpClient
        $Connect = $TcpClient.BeginConnect($ComputerName, $Port, $null, $null)
        $Wait = $Connect.AsyncWaitHandle.WaitOne($TimeoutMs, $false)
        
        if ($Wait) {
            $TcpClient.EndConnect($Connect)
            $TcpClient.Close()
            return $true
        } else {
            $TcpClient.Close()
            return $false
        }
    } catch {
        return $false
    }
}

function Test-HttpEndpoint {
    param(
        [string]$Url,
        [int]$TimeoutSec = 10
    )
    
    try {
        $Response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSec -UseBasicParsing -ErrorAction Stop
        return @{
            Success = $true
            StatusCode = $Response.StatusCode
            StatusDescription = $Response.StatusDescription
        }
    } catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
}

Write-Host "`n=== Local Port Testing ===" -ForegroundColor Cyan

foreach ($PortInfo in $Ports) {
    $Port = $PortInfo.Port
    $Name = $PortInfo.Name
    $Required = $PortInfo.Required
    
    Write-Host "Testing $Name (Port $Port)..." -NoNewline
    
    $IsOpen = Test-Port -ComputerName "localhost" -Port $Port
    
    if ($IsOpen) {
        Write-Host " OPEN" -ForegroundColor Green
    } else {
        $Color = if ($Required) { "Red" } else { "Yellow" }
        $Status = if ($Required) { " CLOSED (REQUIRED)" } else { " CLOSED (OPTIONAL)" }
        Write-Host $Status -ForegroundColor $Color
    }
}

# Test HTTP endpoints if ports are open
Write-Host "`n=== HTTP Endpoint Testing ===" -ForegroundColor Cyan

if (Test-Port -ComputerName "localhost" -Port 3000) {
    Write-Host "Testing Next.js application..." -NoNewline
    $Result = Test-HttpEndpoint -Url "http://localhost:3000"
    if ($Result.Success) {
        Write-Host " OK (Status: $($Result.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host " FAILED ($($Result.Error))" -ForegroundColor Red
    }
}

if (Test-Port -ComputerName "localhost" -Port 80) {
    Write-Host "Testing HTTP (nginx)..." -NoNewline
    $Result = Test-HttpEndpoint -Url "http://localhost"
    if ($Result.Success) {
        Write-Host " OK (Status: $($Result.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host " FAILED ($($Result.Error))" -ForegroundColor Red
    }
}

if (Test-Port -ComputerName "localhost" -Port 443) {
    Write-Host "Testing HTTPS (nginx)..." -NoNewline
    $Result = Test-HttpEndpoint -Url "https://localhost"
    if ($Result.Success) {
        Write-Host " OK (Status: $($Result.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host " FAILED ($($Result.Error))" -ForegroundColor Red
    }
}

# External testing if requested
if ($External -and $Domain -ne "localhost") {
    Write-Host "`n=== External Access Testing ===" -ForegroundColor Cyan
    
    Write-Host "Testing external HTTP access..." -NoNewline
    $Result = Test-HttpEndpoint -Url "http://$Domain"
    if ($Result.Success) {
        Write-Host " OK (Status: $($Result.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host " FAILED ($($Result.Error))" -ForegroundColor Red
    }
    
    Write-Host "Testing external HTTPS access..." -NoNewline
    $Result = Test-HttpEndpoint -Url "https://$Domain"
    if ($Result.Success) {
        Write-Host " OK (Status: $($Result.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host " FAILED ($($Result.Error))" -ForegroundColor Red
    }
}

# Firewall rules check
Write-Host "`n=== Firewall Rules Check ===" -ForegroundColor Cyan

$FirewallRules = Get-NetFirewallRule | Where-Object { $_.DisplayName -like "Dogs Website*" }
if ($FirewallRules) {
    Write-Host "Found Dogs Website firewall rules:" -ForegroundColor Green
    $FirewallRules | Select-Object DisplayName, Direction, Action, Enabled | Format-Table -AutoSize
} else {
    Write-Host "No Dogs Website firewall rules found!" -ForegroundColor Red
    Write-Host "Run setup-firewall.ps1 to create firewall rules." -ForegroundColor Yellow
}

Write-Host "`nPort testing completed!" -ForegroundColor Green
