'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Table from '@/components/ui/Table';
import Pagination from '@/components/ui/Pagination';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { formatDate } from '@/lib/utils';

interface Dog {
  id: string;
  name: string;
  breed: string;
  gender: 'MALE' | 'FEMALE';
  slug: string;
}

interface Breeding {
  id: string;
  date: string;
  description: string | null;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  mother: <PERSON>;
  father: Dog;
  puppyCount: number;
  createdAt: string;
  updatedAt: string;
}

export default function BreedingsPage() {
  const [breedings, setBreedings] = useState<Breeding[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const router = useRouter();

  const fetchBreedings = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/breedings');
      
      if (!response.ok) {
        throw new Error('Ошибка при загрузке данных');
      }
      
      const data = await response.json();
      setBreedings(data);
      setTotalPages(Math.ceil(data.length / 10)); // Предполагаем 10 элементов на странице
    } catch (err) {
      setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при загрузке вязок:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBreedings();
  }, []);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRowClick = (breeding: Breeding) => {
    router.push(`/admin/breedings/${breeding.id}`);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return 'bg-blue-100 text-blue-800';
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return 'Запланирована';
      case 'IN_PROGRESS':
        return 'В процессе';
      case 'COMPLETED':
        return 'Завершена';
      case 'CANCELLED':
        return 'Отменена';
      default:
        return 'Неизвестно';
    }
  };

  const columns = [
    {
      header: 'Дата',
      accessor: (breeding: Breeding) => formatDate(breeding.date),
    },
    {
      header: 'Мать',
      accessor: (breeding: Breeding) => (
        <div>
          <p className="font-medium">{breeding.mother.name}</p>
          <p className="text-sm text-gray-600">{breeding.mother.breed}</p>
        </div>
      ),
    },
    {
      header: 'Отец',
      accessor: (breeding: Breeding) => (
        <div>
          <p className="font-medium">{breeding.father.name}</p>
          <p className="text-sm text-gray-600">{breeding.father.breed}</p>
        </div>
      ),
    },
    {
      header: 'Статус',
      accessor: (breeding: Breeding) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(
            breeding.status
          )}`}
        >
          {getStatusText(breeding.status)}
        </span>
      ),
    },
    {
      header: 'Щенки',
      accessor: (breeding: Breeding) => breeding.puppyCount,
    },
    {
      header: 'Действия',
      accessor: (breeding: Breeding) => (
        <div className="flex space-x-2">
          <Link href={`/admin/breedings/${breeding.id}/edit`}>
            <Button variant="secondary" size="sm">
              Редактировать
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  // Пагинация на клиентской стороне
  const paginatedBreedings = breedings.slice((currentPage - 1) * 10, currentPage * 10);

  return (
    <AdminLayout title="Управление вязками">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            Всего вязок: {breedings.length}
          </p>
        </div>
        <Link href="/admin/breedings/create">
          <Button>Добавить вязку</Button>
        </Link>
      </div>

      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      <Table
        columns={columns}
        data={paginatedBreedings}
        keyExtractor={(breeding) => breeding.id}
        onRowClick={handleRowClick}
        isLoading={isLoading}
        emptyMessage="Вязки не найдены"
      />

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />
    </AdminLayout>
  );
}
