'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Table from '@/components/ui/Table';
import Pagination from '@/components/ui/Pagination';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { formatDate, getInquiryStatusText } from '@/lib/utils';

interface Inquiry {
  id: string;
  name: string;
  email: string;
  phone: string;
  message: string;
  status: 'NEW' | 'IN_PROGRESS' | 'COMPLETED' | 'REJECTED';
  createdAt: string;
  updatedAt: string;
  dogId?: string | null;
  puppyId?: string | null;
  dog?: {
    id: string;
    name: string;
    breed: string;
  } | null;
  puppy?: {
    id: string;
    name: string;
    breeding?: {
      mother: {
        breed: string;
      };
    } | null;
  } | null;
}

export default function InquiriesPage() {
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const router = useRouter();

  const fetchInquiries = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/inquiries');
      
      if (!response.ok) {
        throw new Error('Ошибка при загрузке данных');
      }
      
      const data = await response.json();
      setInquiries(data.inquiries || []);
      setTotalPages(Math.ceil((data.inquiries?.length || 0) / 10)); // Предполагаем 10 элементов на странице
    } catch (err) {
      setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при загрузке заявок:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInquiries();
  }, []);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRowClick = (inquiry: Inquiry) => {
    router.push(`/admin/inquiries/${inquiry.id}`);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-blue-100 text-blue-800';
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getSubjectText = (inquiry: Inquiry) => {
    if (inquiry.dog) {
      return `Собака: ${inquiry.dog.name} (${inquiry.dog.breed})`;
    } else if (inquiry.puppy) {
      const breed = inquiry.puppy.breeding?.mother.breed || 'Порода не указана';
      return `Щенок: ${inquiry.puppy.name} (${breed})`;
    } else {
      return 'Общий запрос';
    }
  };

  const columns = [
    {
      header: 'Имя',
      accessor: 'name',
    },
    {
      header: 'Контакты',
      accessor: (inquiry: Inquiry) => (
        <div>
          <p>{inquiry.email}</p>
          <p>{inquiry.phone}</p>
        </div>
      ),
    },
    {
      header: 'Тема',
      accessor: getSubjectText,
    },
    {
      header: 'Дата',
      accessor: (inquiry: Inquiry) => formatDate(inquiry.createdAt),
    },
    {
      header: 'Статус',
      accessor: (inquiry: Inquiry) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(
            inquiry.status
          )}`}
        >
          {getInquiryStatusText(inquiry.status)}
        </span>
      ),
    },
    {
      header: 'Действия',
      accessor: (inquiry: Inquiry) => (
        <Button
          variant="secondary"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            router.push(`/admin/inquiries/${inquiry.id}`);
          }}
        >
          Просмотреть
        </Button>
      ),
    },
  ];

  // Фильтрация заявок по статусу
  const filteredInquiries = statusFilter === 'ALL'
    ? inquiries
    : inquiries.filter(inquiry => inquiry.status === statusFilter);

  // Пагинация на клиентской стороне
  const paginatedInquiries = filteredInquiries.slice((currentPage - 1) * 10, currentPage * 10);

  return (
    <AdminLayout title="Управление заявками">
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
          <p className="text-gray-500">
            Всего заявок: {inquiries.length}
          </p>
          <p className="text-gray-500">
            Новых заявок: {inquiries.filter(inquiry => inquiry.status === 'NEW').length}
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <label htmlFor="statusFilter" className="text-sm font-medium text-gray-700">
            Фильтр по статусу:
          </label>
          <select
            id="statusFilter"
            value={statusFilter}
            onChange={(e) => {
              setStatusFilter(e.target.value);
              setCurrentPage(1);
            }}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="ALL">Все заявки</option>
            <option value="NEW">Новые</option>
            <option value="IN_PROGRESS">В обработке</option>
            <option value="COMPLETED">Завершенные</option>
            <option value="REJECTED">Отклоненные</option>
          </select>
        </div>
      </div>

      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      <Table
        columns={columns}
        data={paginatedInquiries}
        keyExtractor={(inquiry) => inquiry.id}
        onRowClick={handleRowClick}
        isLoading={isLoading}
        emptyMessage="Заявки не найдены"
      />

      <Pagination
        currentPage={currentPage}
        totalPages={Math.ceil(filteredInquiries.length / 10)}
        onPageChange={handlePageChange}
      />
    </AdminLayout>
  );
}
