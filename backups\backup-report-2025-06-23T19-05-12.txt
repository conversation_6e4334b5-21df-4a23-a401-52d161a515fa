
ОТЧЕТ О РЕЗЕРВНОМ КОПИРОВАНИИ
=============================

Дата создания: 24.06.2025, 00:05:14
Версия: 1.0

СТАТИСТИКА ДАННЫХ:
------------------
👥 Пользователи: 0
🐕 Собаки: 1
🐶 Щенки: 0
📰 Новости: 0
🎨 Hero-слайды: 2
⚙️ Настройки: 9
💕 Вязки: 0
📸 Галерея: 0
📧 Заявки: 1
🖼️ Фотографии: 1

СОЗДАННЫЕ ФАЙЛЫ:
----------------
📄 Данные: full-backup-2025-06-23T19-05-12.json.gz
📋 Схема: schema-2025-06-23T19-05-12.prisma
📁 Файлы: uploads-2025-06-23T19-05-12.tar.gz
🗄️ SQL: sql-dump-2025-06-23T19-05-12.sql.gz

ИНСТРУКЦИИ ПО ВОССТАНОВЛЕНИЮ:
-----------------------------
1. Восстановление данных:
   node scripts/import-data.js backups/full-backup-2025-06-23T19-05-12.json.gz

2. Восстановление схемы:
   cp backups/schema-2025-06-23T19-05-12.prisma prisma/schema.prisma
   npx prisma migrate dev

3. Восстановление файлов:
   cd public && tar -xzf ../backups/uploads-2025-06-23T19-05-12.tar.gz

4. Восстановление из SQL:
   gunzip -c backups/sql-dump-2025-06-23T19-05-12.sql.gz | psql DATABASE_URL
