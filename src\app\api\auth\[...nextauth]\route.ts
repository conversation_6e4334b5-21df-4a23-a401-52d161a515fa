import NextAuth from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Настройки для работы через HTTP
const handler = NextAuth({
  ...authOptions,
  // Переопределяем настройки для HTTP
  useSecureCookies: false,
  callbacks: {
    ...authOptions.callbacks,
    async redirect({ url, baseUrl }) {
      // Убеждаемся, что редирект использует правильный протокол
      if (url.startsWith('/')) {
        return `${process.env.NEXTAUTH_URL}${url}`;
      }
      if (url.startsWith(baseUrl)) {
        return url;
      }
      return process.env.NEXTAUTH_URL || baseUrl;
    },
  },
});

export { handler as GET, handler as POST };
