import NextAuth from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Настройки для работы через HTTP
const handler = NextAuth({
  ...authOptions,
  // Переопределяем настройки для HTTP
  useSecureCookies: false,
  callbacks: {
    ...authOptions.callbacks,
    async redirect({ url, baseUrl }) {
      // Если это относительный URL, используем baseUrl
      if (url.startsWith('/')) {
        return `${baseUrl}${url}`;
      }
      // Если URL начинается с baseUrl, возвращаем как есть
      if (url.startsWith(baseUrl)) {
        return url;
      }
      // По умолчанию возвращаем baseUrl
      return baseUrl;
    },
  },
});

export { handler as GET, handler as POST };
