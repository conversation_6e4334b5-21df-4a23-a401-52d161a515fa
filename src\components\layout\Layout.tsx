'use client';

import { ReactNode, useEffect } from 'react';
import Header from './Header';
import Footer from './Footer';

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  useEffect(() => {
    // Загружаем настройки сайта
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/settings');
        if (response.ok) {
          const data = await response.json();

          // Устанавливаем фавикон, если он есть
          if (data.favicon) {
            const linkElement = document.querySelector("link[rel*='icon']") as HTMLLinkElement || document.createElement('link');
            linkElement.type = 'image/x-icon';
            linkElement.rel = 'shortcut icon';
            linkElement.href = data.favicon;
            document.getElementsByTagName('head')[0].appendChild(linkElement);
          }
        }
      } catch (error) {
        console.error('Ошибка при загрузке настроек:', error);
      }
    };

    fetchSettings();
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow pt-24">{children}</main>
      <Footer />
    </div>
  );
}
