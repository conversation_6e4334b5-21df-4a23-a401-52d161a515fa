import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/inquiries - Получение списка заявок (защищенный маршрут)
export async function GET(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;
    
    // Формируем условия фильтрации
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    // Получаем общее количество заявок
    const total = await prisma.inquiry.count({
      where
    });
    
    // Получаем заявки с пагинацией
    const inquiries = await prisma.inquiry.findMany({
      where,
      include: {
        dog: true,
        puppy: true,
        comments: {
          include: {
            admin: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });
    
    return NextResponse.json({
      inquiries,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Ошибка при получении списка заявок:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении списка заявок' },
      { status: 500 }
    );
  }
}

// POST /api/inquiries - Создание новой заявки
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Создаем новую заявку
    const inquiry = await prisma.inquiry.create({
      data: {
        name: data.name,
        phone: data.phone,
        email: data.email,
        message: data.message,
        dogId: data.dogId || null,
        puppyId: data.puppyId || null
      }
    });
    
    return NextResponse.json(inquiry, { status: 201 });
  } catch (error) {
    console.error('Ошибка при создании заявки:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании заявки' },
      { status: 500 }
    );
  }
}
