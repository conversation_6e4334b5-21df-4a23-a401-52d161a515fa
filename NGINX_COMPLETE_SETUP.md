# Полная настройка nginx для ta-shiba.duckdns.org

## Быстрая установка

### 1. Остановите nginx (если запущен)

```powershell
# Остановите все процессы nginx
Get-Process | Where-Object { $_.Name -like "*nginx*" } | Stop-Process -Force
```

### 2. Замените nginx.conf

```powershell
# Создайте резервную копию текущего nginx.conf
Copy-Item "C:\nginx\conf\nginx.conf" "C:\nginx\conf\nginx.conf.backup"

# Скопируйте новую конфигурацию
Copy-Item "nginx-dogs-site.conf" "C:\nginx\conf\nginx.conf"
```

### 3. Проверьте конфигурацию

```powershell
# Проверьте синтаксис
C:\nginx\nginx.exe -t
```

Должно показать:
```
nginx: the configuration file C:\nginx\conf\nginx.conf syntax is ok
nginx: configuration file C:\nginx\conf\nginx.conf test is successful
```

### 4. Запустите nginx

```powershell
# Запустите nginx
C:\nginx\nginx.exe

# Проверьте, что nginx запущен
Get-Process | Where-Object { $_.Name -like "*nginx*" }
```

### 5. Протестируйте

```powershell
# Быстрый тест
.\test-ta-shiba.ps1

# Проверьте в браузере:
# http://localhost:8080
```

## Что включает эта конфигурация

✅ **Полная конфигурация nginx** - не нужно редактировать другие файлы
✅ **Поддержка порта 8080** - ваша текущая настройка
✅ **Проксирование к Next.js** - автоматическое перенаправление на порт 3000
✅ **Rate limiting** - защита от спама и атак
✅ **SSL готовность** - настроен для будущих SSL сертификатов
✅ **Статические файлы** - обслуживание uploads
✅ **Безопасность** - блокировка доступа к чувствительным файлам
✅ **Сжатие** - gzip для лучшей производительности

## Порты и доступ

После настройки ваш сайт будет доступен:

- **Локально**: http://localhost:8080
- **Через DuckDNS**: http://ta-shiba.duckdns.org:8080 (после настройки DuckDNS)
- **Стандартный HTTP**: http://ta-shiba.duckdns.org (редирект на порт 8080)

## Следующие шаги

1. **Настройте DuckDNS**:
   ```powershell
   # Отредактируйте duckdns-update.ps1 с вашим токеном
   .\duckdns-update.ps1
   ```

2. **Настройте брандмауэр**:
   ```powershell
   .\setup-firewall.ps1
   ```

3. **Настройте проброс портов в роутере**:
   - Порт 80 → 80
   - Порт 8080 → 8080
   - Порт 443 → 443 (для будущего SSL)

4. **Протестируйте внешний доступ**:
   ```powershell
   .\test-ta-shiba.ps1 -Verbose
   ```

## Управление nginx

```powershell
# Запуск
C:\nginx\nginx.exe

# Остановка
C:\nginx\nginx.exe -s quit

# Перезагрузка конфигурации
C:\nginx\nginx.exe -s reload

# Принудительная остановка
Get-Process | Where-Object { $_.Name -like "*nginx*" } | Stop-Process -Force

# Проверка синтаксиса
C:\nginx\nginx.exe -t

# Просмотр конфигурации
C:\nginx\nginx.exe -T
```

## Логи

```powershell
# Логи ошибок
Get-Content "C:\nginx\logs\error.log" -Tail 20

# Логи доступа
Get-Content "C:\nginx\logs\access.log" -Tail 20

# Мониторинг логов в реальном времени
Get-Content "C:\nginx\logs\error.log" -Wait -Tail 10
```

## Устранение проблем

### Ошибка "bind() failed"
Порт уже используется:
```powershell
netstat -ano | findstr :8080
# Остановите процесс с найденным PID
taskkill /PID <PID> /F
```

### Ошибка "permission denied"
Запустите PowerShell от имени администратора.

### Ошибка "file not found"
Проверьте пути к nginx:
```powershell
Test-Path "C:\nginx\nginx.exe"
Test-Path "C:\nginx\conf\nginx.conf"
```

## Восстановление

Если что-то пошло не так:

```powershell
# Восстановите оригинальный nginx.conf
Copy-Item "C:\nginx\conf\nginx.conf.backup" "C:\nginx\conf\nginx.conf"

# Перезапустите nginx
C:\nginx\nginx.exe -s quit
C:\nginx\nginx.exe
```

Готово! Ваш nginx настроен для работы с ta-shiba.duckdns.org на порту 8080.
