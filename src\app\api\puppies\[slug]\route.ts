import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/puppies/[slug] - Получение информации о конкретном щенке
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;
    
    const puppy = await prisma.puppy.findUnique({
      where: { slug },
      include: {
        photos: {
          orderBy: {
            order: 'asc'
          }
        },
        breeding: {
          include: {
            mother: true,
            father: true
          }
        }
      }
    });
    
    if (!puppy) {
      return NextResponse.json(
        { error: 'Щенок не найден' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(puppy);
  } catch (error) {
    console.error('Ошибка при получении информации о щенке:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении информации о щенке' },
      { status: 500 }
    );
  }
}

// PUT /api/puppies/[slug] - Обновление информации о щенке (защищенный маршрут)
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const slug = params.slug;
    const data = await request.json();
    
    // Проверяем, существует ли щенок
    const existingPuppy = await prisma.puppy.findUnique({
      where: { slug }
    });
    
    if (!existingPuppy) {
      return NextResponse.json(
        { error: 'Щенок не найден' },
        { status: 404 }
      );
    }
    
    // Обновляем информацию о щенке
    const updatedPuppy = await prisma.puppy.update({
      where: { slug },
      data: {
        name: data.name,
        gender: data.gender,
        birthDate: data.birthDate ? new Date(data.birthDate) : undefined,
        color: data.color,
        description: data.description,
        status: data.status,
        price: data.price ? parseFloat(data.price) : null,
        isPublished: data.isPublished,
        breedingId: data.breedingId || null
      }
    });
    
    return NextResponse.json(updatedPuppy);
  } catch (error) {
    console.error('Ошибка при обновлении информации о щенке:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении информации о щенке' },
      { status: 500 }
    );
  }
}

// DELETE /api/puppies/[slug] - Удаление щенка (защищенный маршрут)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const slug = params.slug;
    
    // Проверяем, существует ли щенок
    const existingPuppy = await prisma.puppy.findUnique({
      where: { slug }
    });
    
    if (!existingPuppy) {
      return NextResponse.json(
        { error: 'Щенок не найден' },
        { status: 404 }
      );
    }
    
    // Удаляем щенка
    await prisma.puppy.delete({
      where: { slug }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении щенка:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении щенка' },
      { status: 500 }
    );
  }
}
