import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/settings/test - Создание тестовых настроек
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const { key, value, description } = data;

    if (!key || !value) {
      return NextResponse.json(
        { error: 'Необходимо указать ключ и значение' },
        { status: 400 }
      );
    }

    // Проверяем, существует ли настройка
    const existingSetting = await prisma.settings.findUnique({
      where: { key }
    });

    if (existingSetting) {
      // Обновляем существующую настройку
      const updatedSetting = await prisma.settings.update({
        where: { key },
        data: { 
          value,
          ...(description && { description })
        }
      });
      
      return NextResponse.json(updatedSetting);
    } else {
      // Создаем новую настройку
      const newSetting = await prisma.settings.create({
        data: {
          key,
          value,
          description: description || `Настройка ${key}`
        }
      });
      
      return NextResponse.json(newSetting);
    }
  } catch (error) {
    console.error('Ошибка при создании тестовой настройки:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании тестовой настройки' },
      { status: 500 }
    );
  }
}
