import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Создаем настройки для hero-изображения
  const heroImage = await prisma.settings.upsert({
    where: { key: 'heroImage' },
    update: {
      value: '/images/hero-bg.jpg',
      description: 'Изображение для hero-секции',
    },
    create: {
      key: 'heroImage',
      value: '/images/hero-bg.jpg',
      description: 'Изображение для hero-секции',
    },
  });

  // Создаем настройки для изображения "О нас"
  const aboutImage = await prisma.settings.upsert({
    where: { key: 'aboutImage' },
    update: {
      value: '/images/about-dogs.jpg',
      description: 'Изображение для раздела "О нас"',
    },
    create: {
      key: 'aboutImage',
      value: '/images/about-dogs.jpg',
      description: 'Изображение для раздела "О нас"',
    },
  });

  console.log({ heroImage, aboutImage });
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
