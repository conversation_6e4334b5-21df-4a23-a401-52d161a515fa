'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Table from '@/components/ui/Table';
import Pagination from '@/components/ui/Pagination';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { formatDate, formatPrice, getGenderText, getPuppyStatusText } from '@/lib/utils';

interface Puppy {
  id: string;
  name: string;
  gender: 'MALE' | 'FEMALE';
  birthDate: string;
  color: string | null;
  status: 'AVAILABLE' | 'RESERVED' | 'SOLD';
  price: number | null;
  isPublished: boolean;
  slug: string;
  breedingId: string | null;
  breeding: {
    id: string;
    mother: {
      id: string;
      name: string;
      breed: string;
    };
    father: {
      id: string;
      name: string;
      breed: string;
    };
  } | null;
  photos: {
    id: string;
    url: string;
    isMain: boolean;
  }[];
}

export default function PuppiesPage() {
  const [puppies, setPuppies] = useState<Puppy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const router = useRouter();

  const fetchPuppies = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/puppies');
      
      if (!response.ok) {
        throw new Error('Ошибка при загрузке данных');
      }
      
      const data = await response.json();
      setPuppies(data);
      setTotalPages(Math.ceil(data.length / 10)); // Предполагаем 10 элементов на странице
    } catch (err) {
      setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при загрузке щенков:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPuppies();
  }, []);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRowClick = (puppy: Puppy) => {
    router.push(`/admin/puppies/${puppy.slug}`);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800';
      case 'RESERVED':
        return 'bg-yellow-100 text-yellow-800';
      case 'SOLD':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      header: 'Фото',
      accessor: (puppy: Puppy) => {
        const mainPhoto = puppy.photos?.find(photo => photo.isMain);
        return mainPhoto ? (
          <img
            src={mainPhoto.url}
            alt={puppy.name}
            className="h-12 w-12 object-cover rounded-full"
          />
        ) : (
          <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        );
      },
      className: 'w-20',
    },
    {
      header: 'Имя',
      accessor: 'name',
    },
    {
      header: 'Порода',
      accessor: (puppy: Puppy) => {
        if (puppy.breeding && puppy.breeding.mother) {
          return puppy.breeding.mother.breed;
        }
        return 'Не указана';
      },
    },
    {
      header: 'Пол',
      accessor: (puppy: Puppy) => getGenderText(puppy.gender),
    },
    {
      header: 'Дата рождения',
      accessor: (puppy: Puppy) => formatDate(puppy.birthDate),
    },
    {
      header: 'Статус',
      accessor: (puppy: Puppy) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(
            puppy.status
          )}`}
        >
          {getPuppyStatusText(puppy.status)}
        </span>
      ),
    },
    {
      header: 'Цена',
      accessor: (puppy: Puppy) => formatPrice(puppy.price),
    },
    {
      header: 'Опубликовано',
      accessor: (puppy: Puppy) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            puppy.isPublished
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {puppy.isPublished ? 'Да' : 'Нет'}
        </span>
      ),
    },
    {
      header: 'Действия',
      accessor: (puppy: Puppy) => (
        <div className="flex space-x-2">
          <Link href={`/admin/puppies/${puppy.slug}/edit`}>
            <Button variant="secondary" size="sm">
              Редактировать
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  // Пагинация на клиентской стороне
  const paginatedPuppies = puppies.slice((currentPage - 1) * 10, currentPage * 10);

  return (
    <AdminLayout title="Управление щенками">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            Всего щенков: {puppies.length}
          </p>
        </div>
        <Link href="/admin/puppies/create">
          <Button>Добавить щенка</Button>
        </Link>
      </div>

      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      <Table
        columns={columns}
        data={paginatedPuppies}
        keyExtractor={(puppy) => puppy.id}
        onRowClick={handleRowClick}
        isLoading={isLoading}
        emptyMessage="Щенки не найдены"
      />

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />
    </AdminLayout>
  );
}
