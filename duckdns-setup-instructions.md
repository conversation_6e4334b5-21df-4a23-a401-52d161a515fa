# Инструкция по настройке DuckDNS

## 1. Регистрация домена в DuckDNS

1. Перейдите на сайт [DuckDNS.org](https://www.duckdns.org)
2. Войдите через один из предложенных сервисов (Google, GitHub, Reddit и т.д.)
3. Создайте новый домен (например: `mydogs.duckdns.org`)
4. Скопируйте ваш токен (token) - он понадобится для обновления IP

## 2. Настройка автоматического обновления IP

### Вариант 1: Использование PowerShell скрипта

1. Откройте файл `duckdns-update.ps1`
2. Замените `YOUR_DOMAIN` на ваш домен (без .duckdns.org)
3. За<PERSON><PERSON>ните `YOUR_TOKEN` на ваш токен из DuckDNS
4. Сохраните файл

### Вариант 2: Использование batch файла

1. Откройте файл `duckdns-update.bat`
2. Замените `YOUR_DOMAIN` на ваш домен (без .duckdns.org)
3. Замените `YOUR_TOKEN` на ваш токен из DuckDNS
4. Сохраните файл

## 3. Настройка автоматического запуска

### Через Планировщик задач Windows:

1. Откройте "Планировщик задач" (Task Scheduler)
2. Создайте новую задачу:
   - Имя: "DuckDNS IP Update"
   - Триггер: При запуске системы + каждые 5 минут
   - Действие: Запуск программы
   - Программа: `powershell.exe`
   - Аргументы: `-ExecutionPolicy Bypass -File "C:\path\to\your\duckdns-update.ps1"`

### Через автозагрузку:

1. Нажмите Win+R, введите `shell:startup`
2. Скопируйте туда ярлык на ваш скрипт

## 4. Проверка работы

1. Запустите скрипт вручную:
   ```powershell
   .\duckdns-update.ps1
   ```

2. Проверьте, что ваш домен указывает на правильный IP:
   ```cmd
   nslookup yourdomain.duckdns.org
   ```

## 5. Обновление nginx конфигурации

После получения домена, обновите файл `nginx-dogs-site.conf`:
- Замените `yourdomain.duckdns.org` на ваш реальный домен

## Примечания

- IP адрес обновляется автоматически каждый раз при запуске скрипта
- Рекомендуется запускать обновление каждые 5-10 минут
- Логи сохраняются в файл `duckdns-update.log`
- Убедитесь, что ваш роутер настроен на проброс портов 80 и 443 на ваш компьютер
