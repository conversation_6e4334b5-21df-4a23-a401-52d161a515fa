import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/breedings - Получение списка вязок
export async function GET(request: NextRequest) {
  try {
    const breedings = await prisma.breeding.findMany({
      include: {
        mother: {
          include: {
            photos: {
              where: { isMain: true },
              take: 1,
            },
          },
        },
        father: {
          include: {
            photos: {
              where: { isMain: true },
              take: 1,
            },
          },
        },
        puppies: true,
      },
      orderBy: {
        date: 'desc',
      },
    });

    return NextResponse.json(breedings);
  } catch (error) {
    console.error('Ошибка при получении списка вязок:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении списка вязок' },
      { status: 500 }
    );
  }
}

// POST /api/breedings - Создание новой вязки (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Проверяем, существуют ли собаки
    const mother = await prisma.dog.findUnique({
      where: { id: data.motherId }
    });

    const father = await prisma.dog.findUnique({
      where: { id: data.fatherId }
    });

    if (!mother || !father) {
      return NextResponse.json(
        { error: 'Одна или обе собаки не найдены' },
        { status: 404 }
      );
    }

    // Проверяем, что мать - самка, а отец - самец
    if (mother.gender !== 'FEMALE' || father.gender !== 'MALE') {
      return NextResponse.json(
        { error: 'Неверный пол собак для вязки' },
        { status: 400 }
      );
    }

    // Создаем новую вязку
    const breeding = await prisma.breeding.create({
      data: {
        date: new Date(data.date),
        description: data.description,
        status: data.status || 'PLANNED',
        motherId: data.motherId,
        fatherId: data.fatherId,
      },
      include: {
        mother: {
          include: {
            photos: {
              where: { isMain: true },
              take: 1,
            },
          },
        },
        father: {
          include: {
            photos: {
              where: { isMain: true },
              take: 1,
            },
          },
        },
      },
    });

    return NextResponse.json(breeding, { status: 201 });
  } catch (error) {
    console.error('Ошибка при создании вязки:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании вязки' },
      { status: 500 }
    );
  }
}
