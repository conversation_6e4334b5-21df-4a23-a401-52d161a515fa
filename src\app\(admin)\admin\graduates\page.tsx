'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Table from '@/components/ui/Table';
import Pagination from '@/components/ui/Pagination';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ReactNode } from 'react';

interface Column<T> {
  header: string;
  accessor: keyof T | ((item: T) => ReactNode);
  className?: string;
}

interface Dog {
  id: string;
  name: string;
  breed: string;
  gender: 'MALE' | 'FEMALE';
  birthDate: string;
  isForSale: boolean;
  price: number | null;
  isPublished: boolean;
  isGraduate: boolean;
  slug: string;
  photos: {
    id: string;
    url: string;
    isMain: boolean;
  }[];
}

export default function GraduatesPage() {
  const [dogs, setDogs] = useState<Dog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const router = useRouter();

  const fetchGraduates = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/dogs?isGraduate=true&includeUnpublished=true');

      if (!response.ok) {
        throw new Error('Ошибка при загрузке данных');
      }

      const data = await response.json();
      setDogs(data);
      setTotalPages(Math.ceil(data.length / 10));
    } catch (err) {
      setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при загрузке выпускников:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchGraduates();
  }, []);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRowClick = (dog: Dog) => {
    router.push(`/admin/dogs/${dog.slug}`);
  };

  const handleToggleGraduate = async (dogSlug: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/dogs/${dogSlug}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isGraduate: !currentStatus,
        }),
      });

      if (!response.ok) {
        throw new Error('Ошибка при обновлении статуса');
      }

      // Обновляем локальное состояние
      setDogs(prevDogs =>
        prevDogs.filter(dog => dog.slug !== dogSlug)
      );
    } catch (err) {
      setError('Ошибка при изменении статуса выпускника');
      console.error('Ошибка при обновлении статуса:', err);
    }
  };

  const getGenderText = (gender: 'MALE' | 'FEMALE') => {
    return gender === 'MALE' ? 'Кобель' : 'Сука';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU');
  };

  const calculateAge = (birthDate: string) => {
    const birth = new Date(birthDate);
    const today = new Date();
    const ageInYears = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return ageInYears - 1;
    }
    
    return ageInYears;
  };

  // Фильтрация по поисковому запросу
  const filteredDogs = dogs.filter(dog =>
    dog.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dog.breed.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const columns: Column<Dog>[] = [
    {
      header: 'Фото',
      accessor: (dog: Dog) => {
        const mainPhoto = dog.photos?.find(photo => photo.isMain);
        return mainPhoto ? (
          <img
            src={mainPhoto.url}
            alt={dog.name}
            className="h-12 w-12 object-cover rounded-full"
          />
        ) : (
          <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        );
      },
      className: 'w-20',
    },
    {
      header: 'Имя',
      accessor: 'name',
    },
    {
      header: 'Порода',
      accessor: 'breed',
    },
    {
      header: 'Пол',
      accessor: (dog: Dog) => getGenderText(dog.gender),
    },
    {
      header: 'Возраст',
      accessor: (dog: Dog) => `${calculateAge(dog.birthDate)} лет`,
    },
    {
      header: 'Дата рождения',
      accessor: (dog: Dog) => formatDate(dog.birthDate),
    },
    {
      header: 'Статус',
      accessor: (dog: Dog) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          🎓 Выпускник
        </span>
      ),
    },
    {
      header: 'Опубликовано',
      accessor: (dog: Dog) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            dog.isPublished
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {dog.isPublished ? 'Да' : 'Нет'}
        </span>
      ),
    },
    {
      header: 'Действия',
      accessor: (dog: Dog) => (
        <div className="flex space-x-2">
          <Link href={`/admin/dogs/${dog.slug}/edit`}>
            <Button variant="secondary" size="sm">
              Редактировать
            </Button>
          </Link>
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleToggleGraduate(dog.slug, dog.isGraduate);
            }}
          >
            Убрать из выпускников
          </Button>
        </div>
      ),
    },
  ];

  // Пагинация на клиентской стороне
  const paginatedDogs = filteredDogs.slice((currentPage - 1) * 10, currentPage * 10);

  return (
    <AdminLayout title="Управление выпускниками">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <p className="text-gray-500">
              Всего выпускников: {filteredDogs.length}
            </p>
          </div>
          <div className="flex space-x-3">
            <Link href="/admin/dogs">
              <Button variant="secondary">
                Все собаки
              </Button>
            </Link>
            <Link href="/admin/dogs/create">
              <Button>
                Добавить собаку
              </Button>
            </Link>
          </div>
        </div>

        {/* Поиск */}
        <div className="max-w-md">
          <input
            type="text"
            placeholder="Поиск по имени или породе..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {/* Информационная карточка */}
      <div className="mb-6 bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-orange-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-orange-800">
              О выпускниках
            </h3>
            <div className="mt-2 text-sm text-orange-700">
              <p>
                Здесь отображаются собаки, которые отмечены как выпускники. 
                Выпускники - это собаки, которые нашли свои новые дома и семьи. 
                Они отображаются в публичном разделе "Выпускники" на сайте.
              </p>
            </div>
          </div>
        </div>
      </div>

      <Table
        columns={columns}
        data={paginatedDogs}
        keyExtractor={(dog) => dog.id}
        onRowClick={handleRowClick}
        isLoading={isLoading}
        emptyMessage="Выпускники не найдены"
      />

      {filteredDogs.length > 10 && (
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(filteredDogs.length / 10)}
          onPageChange={handlePageChange}
        />
      )}
    </AdminLayout>
  );
}
