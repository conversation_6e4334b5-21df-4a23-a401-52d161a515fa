# Create Windows Service for Dogs Website
# Run this script as Administrator

param(
    [string]$ServiceName = "DogsWebsite",
    [string]$DisplayName = "Dogs Website Service",
    [string]$Description = "Dogs Website Next.js Application Service",
    [string]$AppPath = $PSScriptRoot
)

Write-Host "Creating Windows Service for Dogs Website..." -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script must be run as Administrator!" -ForegroundColor Red
    exit 1
}

# Install Node.js Windows Service Helper (if not already installed)
$NSSMPath = "C:\nssm\nssm.exe"

if (-not (Test-Path $NSSMPath)) {
    Write-Host "NSSM (Non-Sucking Service Manager) not found." -ForegroundColor Yellow
    Write-Host "Please download NSSM from https://nssm.cc/download and extract to C:\nssm\" -ForegroundColor Yellow
    Write-Host "Or install via Chocolatey: choco install nssm" -ForegroundColor Yellow
    exit 1
}

# Stop existing service if it exists
$ExistingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
if ($ExistingService) {
    Write-Host "Stopping existing service..." -ForegroundColor Yellow
    Stop-Service -Name $ServiceName -Force
    & $NSSMPath remove $ServiceName confirm
}

# Create the service
Write-Host "Creating service..." -ForegroundColor Yellow

# Set service executable
& $NSSMPath install $ServiceName "npm"
& $NSSMPath set $ServiceName Parameters "run start"
& $NSSMPath set $ServiceName AppDirectory $AppPath

# Set service properties
& $NSSMPath set $ServiceName DisplayName $DisplayName
& $NSSMPath set $ServiceName Description $Description
& $NSSMPath set $ServiceName Start SERVICE_AUTO_START

# Set environment variables
& $NSSMPath set $ServiceName AppEnvironmentExtra "NODE_ENV=production"

# Set logging
$LogDir = Join-Path $AppPath "logs"
if (-not (Test-Path $LogDir)) {
    New-Item -ItemType Directory -Path $LogDir -Force
}

& $NSSMPath set $ServiceName AppStdout (Join-Path $LogDir "service-output.log")
& $NSSMPath set $ServiceName AppStderr (Join-Path $LogDir "service-error.log")

# Set service to restart on failure
& $NSSMPath set $ServiceName AppExit Default Restart
& $NSSMPath set $ServiceName AppRestartDelay 5000

Write-Host "Service created successfully!" -ForegroundColor Green
Write-Host "Starting service..." -ForegroundColor Yellow

# Start the service
Start-Service -Name $ServiceName

# Check service status
$Service = Get-Service -Name $ServiceName
Write-Host "Service Status: $($Service.Status)" -ForegroundColor $(if ($Service.Status -eq "Running") { "Green" } else { "Red" })

Write-Host "`nService management commands:" -ForegroundColor Cyan
Write-Host "Start service:   Start-Service -Name $ServiceName" -ForegroundColor White
Write-Host "Stop service:    Stop-Service -Name $ServiceName" -ForegroundColor White
Write-Host "Restart service: Restart-Service -Name $ServiceName" -ForegroundColor White
Write-Host "Remove service:  $NSSMPath remove $ServiceName confirm" -ForegroundColor White
