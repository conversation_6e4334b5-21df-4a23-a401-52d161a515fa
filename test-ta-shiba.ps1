# Quick test script for ta-shiba.duckdns.org
# Tests your current setup on port 8080

param(
    [switch]$Verbose,
    [switch]$SkipExternal
)

$Domain = "ta-shiba.duckdns.org"
$Port8080 = 8080

Write-Host "=== Testing ta-shiba.duckdns.org Setup ===" -ForegroundColor Cyan
Write-Host "Domain: $Domain" -ForegroundColor Yellow
Write-Host "Port: $Port8080" -ForegroundColor Yellow
Write-Host ""

function Test-Port {
    param([string]$Host, [int]$Port, [int]$Timeout = 3000)
    try {
        $TcpClient = New-Object System.Net.Sockets.TcpClient
        $Connect = $TcpClient.BeginConnect($Host, $Port, $null, $null)
        $Wait = $Connect.AsyncWaitHandle.WaitOne($Timeout, $false)
        if ($Wait) {
            $TcpClient.EndConnect($Connect)
            $TcpClient.Close()
            return $true
        }
        $TcpClient.Close()
        return $false
    } catch {
        return $false
    }
}

function Test-HttpEndpoint {
    param([string]$Url, [int]$Timeout = 10)
    try {
        $Response = Invoke-WebRequest -Uri $Url -TimeoutSec $Timeout -UseBasicParsing -ErrorAction Stop
        return @{ Success = $true; StatusCode = $Response.StatusCode; Content = $Response.Content }
    } catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Write-TestResult {
    param([string]$TestName, [bool]$Success, [string]$Message = "")
    $Color = if ($Success) { "Green" } else { "Red" }
    $Status = if ($Success) { "✅ PASS" } else { "❌ FAIL" }
    Write-Host "$Status $TestName" -ForegroundColor $Color
    if ($Message) {
        Write-Host "    $Message" -ForegroundColor Gray
    }
}

# Test 1: Next.js application on port 3000
Write-Host "1. Testing Next.js application..." -ForegroundColor Cyan
if (Test-Port -Host "localhost" -Port 3000) {
    $AppTest = Test-HttpEndpoint -Url "http://localhost:3000"
    if ($AppTest.Success) {
        Write-TestResult "Next.js App (Port 3000)" $true "Application responding"
    } else {
        Write-TestResult "Next.js App (Port 3000)" $false "App not responding: $($AppTest.Error)"
    }
} else {
    Write-TestResult "Next.js App (Port 3000)" $false "Port 3000 not accessible - is the app running?"
}

# Test 2: Nginx on port 8080 (your current setup)
Write-Host "`n2. Testing nginx on port 8080..." -ForegroundColor Cyan
if (Test-Port -Host "localhost" -Port $Port8080) {
    $NginxTest = Test-HttpEndpoint -Url "http://localhost:$Port8080"
    if ($NginxTest.Success) {
        Write-TestResult "Nginx (Port 8080)" $true "Nginx responding on port 8080"
    } else {
        Write-TestResult "Nginx (Port 8080)" $false "Nginx error: $($NginxTest.Error)"
    }
} else {
    Write-TestResult "Nginx (Port 8080)" $false "Port 8080 not accessible - is nginx running?"
}

# Test 3: Standard HTTP port 80
Write-Host "`n3. Testing standard HTTP port..." -ForegroundColor Cyan
if (Test-Port -Host "localhost" -Port 80) {
    $HttpTest = Test-HttpEndpoint -Url "http://localhost"
    if ($HttpTest.Success) {
        Write-TestResult "HTTP (Port 80)" $true "HTTP service responding"
    } else {
        Write-TestResult "HTTP (Port 80)" $false "HTTP service error: $($HttpTest.Error)"
    }
} else {
    Write-TestResult "HTTP (Port 80)" $false "Port 80 not accessible"
}

# Test 4: DuckDNS resolution
Write-Host "`n4. Testing DuckDNS resolution..." -ForegroundColor Cyan
try {
    $DnsResult = Resolve-DnsName -Name $Domain -ErrorAction Stop
    if ($DnsResult) {
        $ResolvedIP = $DnsResult | Where-Object { $_.Type -eq "A" } | Select-Object -First 1 -ExpandProperty IPAddress
        Write-TestResult "DuckDNS Resolution" $true "Domain resolves to $ResolvedIP"
    } else {
        Write-TestResult "DuckDNS Resolution" $false "Domain does not resolve"
    }
} catch {
    Write-TestResult "DuckDNS Resolution" $false "DNS resolution error: $($_.Exception.Message)"
}

# Test 5: External access (if not skipped)
if (-not $SkipExternal) {
    Write-Host "`n5. Testing external access..." -ForegroundColor Cyan
    
    # Test port 8080 externally
    $ExternalTest8080 = Test-HttpEndpoint -Url "http://$Domain`:$Port8080" -Timeout 15
    if ($ExternalTest8080.Success) {
        Write-TestResult "External Access (Port 8080)" $true "Site accessible from internet on port 8080"
    } else {
        Write-TestResult "External Access (Port 8080)" $false "Not accessible on port 8080: $($ExternalTest8080.Error)"
    }
    
    # Test standard HTTP port externally
    $ExternalTestHTTP = Test-HttpEndpoint -Url "http://$Domain" -Timeout 15
    if ($ExternalTestHTTP.Success) {
        Write-TestResult "External HTTP Access" $true "Site accessible via standard HTTP"
    } else {
        Write-TestResult "External HTTP Access" $false "Not accessible via HTTP: $($ExternalTestHTTP.Error)"
    }
} else {
    Write-Host "`n5. Skipping external tests..." -ForegroundColor Yellow
}

# Test 6: Firewall rules
Write-Host "`n6. Testing firewall rules..." -ForegroundColor Cyan
$FirewallRules = Get-NetFirewallRule | Where-Object { 
    ($_.DisplayName -like "Dogs Website*" -or $_.DisplayName -like "*8080*") -and $_.Enabled -eq "True" 
}
if ($FirewallRules.Count -gt 0) {
    Write-TestResult "Firewall Rules" $true "$($FirewallRules.Count) relevant firewall rules found"
    if ($Verbose) {
        $FirewallRules | ForEach-Object {
            Write-Host "    - $($_.DisplayName)" -ForegroundColor DarkGray
        }
    }
} else {
    Write-TestResult "Firewall Rules" $false "No relevant firewall rules found"
}

# Test 7: Process check
Write-Host "`n7. Testing running processes..." -ForegroundColor Cyan
$NginxProcess = Get-Process | Where-Object { $_.Name -like "*nginx*" }
$NodeProcess = Get-Process | Where-Object { $_.Name -like "*node*" }

if ($NginxProcess) {
    Write-TestResult "Nginx Process" $true "$($NginxProcess.Count) nginx process(es) running"
} else {
    Write-TestResult "Nginx Process" $false "No nginx processes found"
}

if ($NodeProcess) {
    Write-TestResult "Node.js Process" $true "$($NodeProcess.Count) node process(es) running"
} else {
    Write-TestResult "Node.js Process" $false "No node processes found"
}

# Summary
Write-Host "`n=== Summary ===" -ForegroundColor Cyan
Write-Host "Your current setup uses port 8080 for nginx." -ForegroundColor Yellow
Write-Host "After all tests pass, your site should be accessible at:" -ForegroundColor Yellow
Write-Host "  • Local: http://localhost:8080" -ForegroundColor White
Write-Host "  • Internet: http://ta-shiba.duckdns.org:8080" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "  1. Ensure DuckDNS token is configured" -ForegroundColor White
Write-Host "  2. Run duckdns-update.ps1 to update your IP" -ForegroundColor White
Write-Host "  3. Configure port forwarding in your router (port 8080)" -ForegroundColor White
Write-Host "  4. Optionally set up SSL for HTTPS access" -ForegroundColor White
