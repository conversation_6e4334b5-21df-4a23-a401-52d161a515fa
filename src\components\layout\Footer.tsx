'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const [settings, setSettings] = useState<Record<string, string>>({});

  useEffect(() => {
    // Загружаем настройки сайта
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/settings');
        if (response.ok) {
          const data = await response.json();
          setSettings(data);
        }
      } catch (error) {
        console.error('Ошибка при загрузке настроек:', error);
      }
    };

    fetchSettings();
  }, []);

  return (
    <footer className="bg-gray-800 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-xl font-semibold mb-4">{settings.siteName || 'Питомник Собак'}</h3>
            <p className="text-gray-300">
              {settings.siteDescription || 'Разведение и продажа породистых собак высшего качества.'}
            </p>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-4">Навигация</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-300 hover:text-white">
                  Главная
                </Link>
              </li>
              <li>
                <Link href="/dogs" className="text-gray-300 hover:text-white">
                  Собаки
                </Link>
              </li>
              <li>
                <Link href="/puppies" className="text-gray-300 hover:text-white">
                  Щенки
                </Link>
              </li>
              <li>
                <Link href="/gallery" className="text-gray-300 hover:text-white">
                  Галерея
                </Link>
              </li>
              <li>
                <Link href="/news" className="text-gray-300 hover:text-white">
                  Новости
                </Link>
              </li>
              <li>
                <Link href="/contacts" className="text-gray-300 hover:text-white">
                  Контакты
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-4">Контакты</h3>
            <ul className="space-y-2 text-gray-300">
              <li>Телефон: {settings.contactPhone || '+7 (XXX) XXX-XX-XX'}</li>
              <li>Email: {settings.contactEmail || '<EMAIL>'}</li>
              <li>Адрес: {settings.address || 'г. Москва, ул. Примерная, д. 123'}</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-6 text-center text-gray-400">
          <p>© {currentYear} {settings.siteName || 'Питомник Собак'}. Все права защищены.</p>
        </div>
      </div>
    </footer>
  );
}
