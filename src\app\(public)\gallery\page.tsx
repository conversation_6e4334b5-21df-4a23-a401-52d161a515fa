import Image from 'next/image';
import Link from 'next/link';
import Layout from '@/components/layout/Layout';
import { prisma } from '@/lib/db';
import { PawPrint, Leaf, FloatingElements } from '@/components/ui/Decorations';

export const metadata = {
  title: 'Галерея - Питомник собак',
  description: 'Фотогалерея нашего питомника собак. Фотографии наших собак, щенков и мероприятий.',
};

export default async function GalleryPage() {
  // Получаем список категорий
  const categories = await prisma.galleryCategory.findMany({
    orderBy: [
      { order: 'asc' },
      { name: 'asc' },
    ],
  });

  // Получаем список альбомов
  const albums = await prisma.gallery.findMany({
    where: {
      isPublished: true,
    },
    include: {
      category: true,
      photos: {
        where: {
          isMain: true,
        },
        take: 1,
      },
    },
    orderBy: [
      { order: 'asc' },
      { createdAt: 'desc' },
    ],
  });

  return (
    <Layout>
      <section className="pt-16 pb-24 bg-forest-bg relative">
        <FloatingElements count={6} type="mixed" className="absolute inset-0 z-0 pointer-events-none" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex items-center justify-center mb-6">
            <PawPrint size="sm" className="mr-3 text-shiba-orange" />
            <span className="text-shiba-orange font-medium tracking-wider uppercase text-sm">Наш питомник</span>
          </div>

          <h1 className="text-3xl md:text-4xl font-bold text-center text-forest-dark mb-6">Галерея</h1>

          <div className="max-w-3xl mx-auto text-center mb-12">
            <p className="text-lg text-forest-medium">
              Фотогалерея нашего питомника собак. Фотографии наших собак, щенков и мероприятий.
            </p>
          </div>

          {/* Категории альбомов */}
          {categories.length > 0 && (
            <div className="mb-10 flex flex-wrap justify-center gap-3">
              <Link
                href="/gallery"
                className={`px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all text-sm font-medium ${
                  true
                    ? 'bg-white text-forest-dark'
                    : 'bg-white/50 text-forest-medium hover:bg-white hover:text-forest-dark'
                }`}
              >
                Все альбомы
              </Link>

              {categories.map((category) => (
                <Link
                  key={category.id}
                  href={`/gallery/category/${category.slug}`}
                  className="bg-white/50 text-forest-medium px-4 py-2 rounded-full hover:bg-white hover:text-forest-dark transition-all text-sm font-medium shadow-sm hover:shadow-md"
                >
                  {category.name}
                </Link>
              ))}
            </div>
          )}

          {/* Альбомы */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {albums.length > 0 ? (
              albums.map((album) => (
                <Link
                  key={album.id}
                  href={`/gallery/${album.slug}`}
                  className="group"
                >
                  <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform group-hover:translate-y-[-5px]">
                    <div className="relative h-64">
                      {album.photos.length > 0 ? (
                        <>
                          <Image
                            src={album.photos[0].url}
                            alt={album.title}
                            fill
                            style={{ objectFit: 'cover' }}
                            className="transition-transform duration-500 group-hover:scale-105"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </>
                      ) : album.coverImage ? (
                        <>
                          <Image
                            src={album.coverImage}
                            alt={album.title}
                            fill
                            style={{ objectFit: 'cover' }}
                            className="transition-transform duration-500 group-hover:scale-105"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </>
                      ) : (
                        <div className="w-full h-full bg-forest-bg flex items-center justify-center">
                          <div className="text-center">
                            <svg className="w-12 h-12 mx-auto text-forest-light mb-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
                            </svg>
                            <span className="text-forest-medium">Нет фото</span>
                          </div>
                        </div>
                      )}

                      {album.category && (
                        <div className="absolute top-3 left-3">
                          <span className="bg-white/80 text-forest-dark text-xs font-medium px-3 py-1 rounded-full shadow-sm">
                            {album.category.name}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="p-6">
                      <h2 className="text-xl font-bold text-forest-dark mb-3 group-hover:text-shiba-orange transition-colors">{album.title}</h2>
                      {album.description && (
                        <p className="text-forest-medium mb-4 text-sm line-clamp-3">
                          {album.description.length > 100
                            ? `${album.description.substring(0, 100)}...`
                            : album.description}
                        </p>
                      )}
                      <div className="flex justify-end">
                        <span className="text-forest-dark font-medium text-sm group-hover:text-shiba-orange transition-colors flex items-center">
                          Смотреть альбом
                          <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                          </svg>
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))
            ) : (
              <div className="col-span-full bg-white rounded-xl shadow-md p-12 text-center">
                <div className="w-20 h-20 mx-auto bg-forest-bg/20 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-10 h-10 text-forest-light" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-forest-dark mb-2">Альбомы не найдены</h3>
                <p className="text-forest-medium">В данный момент нет доступных альбомов</p>
              </div>
            )}
          </div>

          <div className="absolute left-0 bottom-0 w-32 h-32 opacity-10">
            <PawPrint size="lg" />
          </div>
          <div className="absolute right-10 top-40 w-16 h-16 opacity-10">
            <Leaf size="md" />
          </div>
        </div>
      </section>
    </Layout>
  );
}
