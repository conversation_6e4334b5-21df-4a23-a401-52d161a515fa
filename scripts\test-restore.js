const fs = require('fs');
const path = require('path');
const zlib = require('zlib');

function testRestore() {
  console.log('🔍 Тестирование восстановления backup...');
  
  // Проверяем текущую директорию
  console.log(`📂 Текущая директория: ${process.cwd()}`);
  
  // Проверяем папку backups
  const backupDir = path.join(process.cwd(), 'backups');
  console.log(`📁 Папка backups: ${backupDir}`);
  
  if (!fs.existsSync(backupDir)) {
    console.error('❌ Папка backups не найдена!');
    return;
  }
  
  console.log('✅ Папка backups найдена');
  
  // Список всех файлов в папке backups
  const files = fs.readdirSync(backupDir);
  console.log('\n📋 Файлы в папке backups:');
  
  files.forEach(file => {
    const filePath = path.join(backupDir, file);
    const stats = fs.statSync(filePath);
    const size = (stats.size / 1024 / 1024).toFixed(2);
    console.log(`   📄 ${file} - ${size} MB`);
  });
  
  // Найдем JSON.GZ файлы
  const jsonFiles = files.filter(f => f.endsWith('.json.gz') || f.endsWith('.json'));
  
  if (jsonFiles.length === 0) {
    console.error('❌ Не найдено JSON файлов для восстановления');
    return;
  }
  
  console.log('\n🎯 Файлы для восстановления:');
  jsonFiles.forEach((file, index) => {
    console.log(`   ${index + 1}. ${file}`);
  });
  
  // Тестируем чтение первого файла
  const testFile = path.join(backupDir, jsonFiles[0]);
  console.log(`\n🧪 Тестируем чтение файла: ${testFile}`);
  
  try {
    let data;
    
    if (testFile.endsWith('.gz')) {
      console.log('📦 Распаковка сжатого файла...');
      const compressed = fs.readFileSync(testFile);
      const decompressed = zlib.gunzipSync(compressed);
      data = JSON.parse(decompressed.toString());
    } else {
      console.log('📖 Чтение обычного файла...');
      const fileContent = fs.readFileSync(testFile, 'utf8');
      data = JSON.parse(fileContent);
    }
    
    console.log('✅ Файл успешно прочитан!');
    console.log(`📅 Дата создания: ${data.timestamp}`);
    console.log(`🔢 Версия: ${data.version}`);
    
    if (data.data) {
      console.log('\n📊 Статистика данных:');
      Object.keys(data.data).forEach(key => {
        const count = Array.isArray(data.data[key]) ? data.data[key].length : 'N/A';
        console.log(`   ${key}: ${count}`);
      });
    }
    
    console.log('\n🎉 Тест прошел успешно! Файл можно использовать для восстановления.');
    console.log('\n💡 Команды для восстановления:');
    console.log(`   node scripts/import-data.js "${testFile}"`);
    console.log(`   или`);
    console.log(`   node scripts/import-data.js backups/${jsonFiles[0]}`);
    
  } catch (error) {
    console.error('❌ Ошибка при чтении файла:', error.message);
    console.log('\n🔧 Возможные решения:');
    console.log('1. Проверьте, что файл не поврежден');
    console.log('2. Убедитесь, что у вас есть права на чтение файла');
    console.log('3. Попробуйте создать новый backup');
  }
}

testRestore();
