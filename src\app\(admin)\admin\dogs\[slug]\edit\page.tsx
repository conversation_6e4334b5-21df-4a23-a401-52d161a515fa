'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import Select from '@/components/ui/Select';
import FileUpload from '@/components/ui/FileUpload';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface Dog {
  id: string;
  name: string;
  breed: string;
  gender: 'MALE' | 'FEMALE';
  birthDate: string;
  color: string | null;
  weight: number | null;
  height: number | null;
  pedigree: string | null;
  achievements: string | null;
  description: string | null;
  slug: string;
  isForSale: boolean;
  price: number | null;
  isPublished: boolean;
  isGraduate: boolean;
  photos: Photo[];
}

const dogSchema = z.object({
  name: z.string().min(1, 'Имя обязательно'),
  breed: z.string().min(1, 'Порода обязательна'),
  gender: z.enum(['MALE', 'FEMALE']),
  birthDate: z.string().min(1, 'Дата рождения обязательна'),
  color: z.string().optional(),
  weight: z.string().optional(),
  height: z.string().optional(),
  pedigree: z.string().optional(),
  achievements: z.string().optional(),
  description: z.string().optional(),
  isForSale: z.boolean().default(false),
  price: z.string().optional(),
  isPublished: z.boolean().default(true),
  isGraduate: z.boolean().default(false),
});

type DogFormData = z.infer<typeof dogSchema>;

export default function EditDogPage({ params }: { params: { slug: string } }) {
  const [dog, setDog] = useState<Dog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [photos, setPhotos] = useState<File[]>([]);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm<DogFormData>({
    resolver: zodResolver(dogSchema),
  });

  const isForSale = watch('isForSale');

  useEffect(() => {
    const fetchDog = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/dogs/${params.slug}`);
        
        if (!response.ok) {
          throw new Error('Ошибка при загрузке данных');
        }
        
        const data: Dog = await response.json();
        setDog(data);
        
        // Преобразуем данные для формы
        reset({
          name: data.name,
          breed: data.breed,
          gender: data.gender,
          birthDate: new Date(data.birthDate).toISOString().split('T')[0],
          color: data.color || '',
          weight: data.weight !== null ? data.weight.toString() : '',
          height: data.height !== null ? data.height.toString() : '',
          pedigree: data.pedigree || '',
          achievements: data.achievements || '',
          description: data.description || '',
          isForSale: data.isForSale,
          price: data.price !== null ? data.price.toString() : '',
          isPublished: data.isPublished,
          isGraduate: data.isGraduate || false,
        });
      } catch (err) {
        setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
        console.error('Ошибка при загрузке собаки:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDog();
  }, [params.slug, reset]);

  const onSubmit = async (data: DogFormData) => {
    if (!dog) return;
    
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Создаем объект с данными собаки
      const dogData = {
        ...data,
        weight: data.weight ? parseFloat(data.weight) : null,
        height: data.height ? parseFloat(data.height) : null,
        price: data.price ? parseFloat(data.price) : null,
      };

      // Отправляем запрос на обновление собаки
      const response = await fetch(`/api/dogs/${params.slug}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dogData),
      });

      if (!response.ok) {
        throw new Error('Ошибка при обновлении собаки');
      }

      // Если есть новые фотографии, загружаем их
      if (photos.length > 0) {
        for (let i = 0; i < photos.length; i++) {
          const formData = new FormData();
          formData.append('file', photos[i]);
          formData.append('dogId', dog.id);
          formData.append('isMain', i === 0 && dog.photos.length === 0 ? 'true' : 'false');
          formData.append('order', (dog.photos.length + i).toString());

          const photoResponse = await fetch('/api/photos', {
            method: 'POST',
            body: formData,
          });

          if (!photoResponse.ok) {
            console.error('Ошибка при загрузке фотографии:', await photoResponse.text());
          }
        }
      }

      setSuccess('Информация о собаке успешно обновлена');
      
      // Обновляем данные на странице
      const updatedDogResponse = await fetch(`/api/dogs/${params.slug}`);
      if (updatedDogResponse.ok) {
        const updatedDog = await updatedDogResponse.json();
        setDog(updatedDog);
      }
    } catch (err) {
      console.error('Ошибка при обновлении собаки:', err);
      setError('Произошла ошибка при обновлении информации о собаке. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePhotosChange = (files: File[]) => {
    setPhotos(files);
  };

  const handleDeletePhoto = async (photoId: string) => {
    if (!dog) return;
    
    try {
      const response = await fetch(`/api/photos/${photoId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при удалении фотографии');
      }
      
      // Обновляем данные на странице
      const updatedDogResponse = await fetch(`/api/dogs/${params.slug}`);
      if (updatedDogResponse.ok) {
        const updatedDog = await updatedDogResponse.json();
        setDog(updatedDog);
      }
    } catch (err) {
      console.error('Ошибка при удалении фотографии:', err);
      setError('Произошла ошибка при удалении фотографии. Пожалуйста, попробуйте позже.');
    }
  };

  const handleSetMainPhoto = async (photoId: string) => {
    if (!dog) return;
    
    try {
      const response = await fetch(`/api/photos/${photoId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isMain: true }),
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при установке главной фотографии');
      }
      
      // Обновляем данные на странице
      const updatedDogResponse = await fetch(`/api/dogs/${params.slug}`);
      if (updatedDogResponse.ok) {
        const updatedDog = await updatedDogResponse.json();
        setDog(updatedDog);
      }
    } catch (err) {
      console.error('Ошибка при установке главной фотографии:', err);
      setError('Произошла ошибка при установке главной фотографии. Пожалуйста, попробуйте позже.');
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error && !dog) {
    return (
      <AdminLayout title="Ошибка">
        <Alert type="error">{error}</Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/dogs')}>
            Вернуться к списку собак
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={`Редактирование: ${dog?.name || ''}`}>
      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Основная информация</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Имя собаки"
              {...register('name')}
              error={errors.name?.message}
              fullWidth
            />
            <Input
              label="Порода"
              {...register('breed')}
              error={errors.breed?.message}
              fullWidth
            />
            <Controller
              name="gender"
              control={control}
              render={({ field }) => (
                <Select
                  label="Пол"
                  options={[
                    { value: 'MALE', label: 'Кобель' },
                    { value: 'FEMALE', label: 'Сука' },
                  ]}
                  {...field}
                  error={errors.gender?.message}
                  fullWidth
                />
              )}
            />
            <Input
              label="Дата рождения"
              type="date"
              {...register('birthDate')}
              error={errors.birthDate?.message}
              fullWidth
            />
            <Input
              label="Окрас"
              {...register('color')}
              error={errors.color?.message}
              fullWidth
            />
            <Input
              label="Вес (кг)"
              type="number"
              step="0.1"
              {...register('weight')}
              error={errors.weight?.message}
              fullWidth
            />
            <Input
              label="Рост (см)"
              type="number"
              step="0.1"
              {...register('height')}
              error={errors.height?.message}
              fullWidth
            />
            <Input
              label="Родословная"
              {...register('pedigree')}
              error={errors.pedigree?.message}
              fullWidth
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Дополнительная информация</h2>
          <div className="space-y-6">
            <Textarea
              label="Достижения"
              {...register('achievements')}
              error={errors.achievements?.message}
              rows={3}
              fullWidth
            />
            <Textarea
              label="Описание"
              {...register('description')}
              error={errors.description?.message}
              rows={5}
              fullWidth
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Продажа</h2>
          <div className="space-y-6">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isForSale"
                {...register('isForSale')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isForSale" className="ml-2 block text-sm text-gray-900">
                Выставить на продажу
              </label>
            </div>

            {isForSale && (
              <Input
                label="Цена (руб.)"
                type="number"
                {...register('price')}
                error={errors.price?.message}
                fullWidth
              />
            )}

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPublished"
                {...register('isPublished')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
                Опубликовать на сайте
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isGraduate"
                {...register('isGraduate')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isGraduate" className="ml-2 block text-sm text-gray-900">
                Выпускник питомника
              </label>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Фотографии</h2>
          
          {dog && dog.photos.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Текущие фотографии</h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {dog.photos.map((photo) => (
                  <div key={photo.id} className="relative group">
                    <div className={`relative aspect-square rounded-md overflow-hidden ${
                      photo.isMain ? 'ring-2 ring-blue-500' : ''
                    }`}>
                      <img
                        src={photo.url}
                        alt={photo.title || dog.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <div className="flex space-x-2">
                        {!photo.isMain && (
                          <button
                            type="button"
                            onClick={() => handleSetMainPhoto(photo.id)}
                            className="bg-blue-500 text-white rounded-full p-1"
                            title="Сделать главной"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                          </button>
                        )}
                        <button
                          type="button"
                          onClick={() => handleDeletePhoto(photo.id)}
                          className="bg-red-500 text-white rounded-full p-1"
                          title="Удалить"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="space-y-6">
            <FileUpload
              label="Загрузите новые фотографии"
              accept="image/*"
              multiple
              onChange={handlePhotosChange}
              fullWidth
            />
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => router.push(`/admin/dogs/${params.slug}`)}
          >
            Отмена
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            Сохранить
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
