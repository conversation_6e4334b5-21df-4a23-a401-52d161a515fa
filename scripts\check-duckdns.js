#!/usr/bin/env node

const { exec } = require('child_process');
const dns = require('dns');
const https = require('https');

const DUCKDNS_DOMAIN = 'ta-shiba.duckdns.org';

// Функция для получения внешнего IP
function getExternalIP() {
  return new Promise((resolve, reject) => {
    https.get('https://api.ipify.org', (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => resolve(data.trim()));
    }).on('error', reject);
  });
}

// Функция для получения IP домена
function getDomainIP(domain) {
  return new Promise((resolve, reject) => {
    dns.lookup(domain, (err, address) => {
      if (err) reject(err);
      else resolve(address);
    });
  });
}

// Основная функция проверки
async function checkDuckDNS() {
  console.log('🔍 Проверка статуса DuckDNS...\n');
  
  try {
    // Получаем внешний IP
    console.log('📡 Получение внешнего IP адреса...');
    const externalIP = await getExternalIP();
    console.log(`   Ваш внешний IP: ${externalIP}`);
    
    // Получаем IP домена
    console.log('\n🌐 Проверка DNS записи...');
    const domainIP = await getDomainIP(DUCKDNS_DOMAIN);
    console.log(`   IP домена ${DUCKDNS_DOMAIN}: ${domainIP}`);
    
    // Сравниваем IP адреса
    console.log('\n📊 Результат:');
    if (externalIP === domainIP) {
      console.log('   ✅ DuckDNS настроен правильно!');
      console.log('   ✅ Домен указывает на ваш IP адрес');
      console.log(`\n🌍 Ваш сайт должен быть доступен по адресу:`);
      console.log(`   http://${DUCKDNS_DOMAIN}:8080`);
    } else {
      console.log('   ❌ DuckDNS настроен неправильно');
      console.log('   ❌ Домен указывает на другой IP адрес');
      console.log('\n🔧 Что нужно сделать:');
      console.log('   1. Обновите DuckDNS с помощью: .\\duckdns-update.ps1');
      console.log('   2. Подождите 1-2 минуты для обновления DNS');
      console.log('   3. Запустите эту проверку снова');
    }
    
    console.log('\n📋 Дополнительная информация:');
    console.log(`   • Домен: ${DUCKDNS_DOMAIN}`);
    console.log(`   • Ваш IP: ${externalIP}`);
    console.log(`   • IP домена: ${domainIP}`);
    console.log(`   • Nginx порт: 8080`);
    
  } catch (error) {
    console.error('\n❌ Ошибка при проверке:');
    console.error(`   ${error.message}`);
    
    if (error.code === 'ENOTFOUND') {
      console.log('\n💡 Возможные причины:');
      console.log('   • Домен не настроен в DuckDNS');
      console.log('   • Проблемы с интернет соединением');
      console.log('   • DNS сервер не обновился');
    }
  }
}

// Запуск проверки
checkDuckDNS();
