# 📖 Архитектура системы

## Обзор архитектуры

Сайт питомника построен на современной архитектуре с использованием Next.js 14 и App Router, что обеспечивает высокую производительность и SEO-оптимизацию.

## 🏗️ Общая архитектура

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Пользователь  │    │   Администратор │    │   Гость сайта   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      Next.js Frontend     │
                    │   (App Router + SSR)      │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │       API Routes          │
                    │   (Next.js API Layer)     │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Prisma ORM            │
                    │   (Database Layer)        │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │    PostgreSQL             │
                    │   (Database)              │
                    └───────────────────────────┘
```

## 🔧 Технологический стек

### Frontend
- **Next.js 14** - React фреймворк с App Router
- **React 18** - UI библиотека с Server Components
- **TypeScript** - Типизированный JavaScript
- **Tailwind CSS** - Utility-first CSS фреймворк
- **React Hook Form** - Управление формами
- **Zod** - Валидация схем

### Backend
- **Next.js API Routes** - Серверная логика
- **NextAuth.js** - Аутентификация и авторизация
- **Prisma ORM** - Работа с базой данных
- **PostgreSQL** - Реляционная база данных

### Инфраструктура
- **Vercel** - Хостинг и деплой (рекомендуется)
- **File System** - Локальное хранение файлов
- **Docker** - Контейнеризация (опционально)

## 📁 Структура приложения

### App Router (Next.js 14)
```
src/app/
├── (admin)/                 # Группа маршрутов для админки
│   └── admin/
│       ├── layout.tsx       # Макет админки
│       ├── page.tsx         # Дашборд
│       ├── dogs/           # Управление собаками
│       ├── puppies/        # Управление щенками
│       ├── news/           # Управление новостями
│       ├── users/          # Управление пользователями
│       ├── hero-slides/    # Управление слайдами
│       └── settings/       # Настройки сайта
├── api/                    # API маршруты
│   ├── auth/              # Аутентификация
│   ├── dogs/              # API собак
│   ├── puppies/           # API щенков
│   ├── news/              # API новостей
│   ├── hero-slides/       # API слайдов
│   └── settings/          # API настроек
├── dogs/                  # Публичные страницы собак
├── puppies/               # Публичные страницы щенков
├── news/                  # Публичные страницы новостей
├── graduates/             # Страница выпускников
├── contact/               # Страница контактов
├── layout.tsx             # Корневой макет
├── page.tsx               # Главная страница
└── globals.css            # Глобальные стили
```

### Компоненты
```
src/components/
├── admin/                 # Админские компоненты
│   ├── AdminLayout.tsx    # Макет админки
│   ├── AdminSidebar.tsx   # Боковая панель
│   ├── AdminHeader.tsx    # Шапка админки
│   └── AdminMobileNav.tsx # Мобильная навигация
├── layout/                # Компоненты макета
│   ├── Layout.tsx         # Основной макет
│   ├── Header.tsx         # Шапка сайта
│   ├── Footer.tsx         # Подвал сайта
│   └── Navigation.tsx     # Навигация
└── ui/                    # UI компоненты
    ├── Button.tsx         # Кнопки
    ├── Input.tsx          # Поля ввода
    ├── Modal.tsx          # Модальные окна
    ├── Alert.tsx          # Уведомления
    ├── HeroSlider.tsx     # Слайдер главной страницы
    └── Decorations.tsx    # Декоративные элементы
```

## 🔐 Система аутентификации

### NextAuth.js конфигурация
- **Провайдеры**: Credentials (email/password)
- **Сессии**: JWT токены
- **Роли**: USER, ADMIN
- **Защищенные маршруты**: Все `/admin/*` страницы

### Middleware
```typescript
// middleware.ts
export { default } from "next-auth/middleware"

export const config = {
  matcher: ["/admin/:path*"]
}
```

## 🗄️ Слой данных

### Prisma ORM
- **Схема**: Декларативное описание моделей
- **Миграции**: Версионирование изменений БД
- **Типизация**: Автогенерация TypeScript типов
- **Клиент**: Type-safe доступ к данным

### Основные модели
- **User** - Пользователи системы
- **Dog** - Собаки питомника
- **Puppy** - Щенки на продажу
- **News** - Новости и статьи
- **HeroSlide** - Слайды главной страницы
- **Settings** - Настройки сайта

## 🎨 UI/UX архитектура

### Дизайн-система
- **Цветовая палитра**: Природные оттенки (лесной, оранжевый сиба)
- **Типографика**: Системные шрифты с fallback
- **Компоненты**: Переиспользуемые UI элементы
- **Адаптивность**: Mobile-first подход

### Состояние приложения
- **Серверное состояние**: React Server Components
- **Клиентское состояние**: React useState/useEffect
- **Формы**: React Hook Form + Zod валидация
- **Кэширование**: Next.js автоматическое кэширование

## 🚀 Производительность

### Оптимизации Next.js
- **SSR/SSG**: Серверный рендеринг для SEO
- **Image Optimization**: Автоматическая оптимизация изображений
- **Code Splitting**: Автоматическое разделение кода
- **Prefetching**: Предзагрузка страниц

### Кэширование
- **Static Generation**: Статические страницы
- **Incremental Static Regeneration**: Обновление статики
- **API Route Caching**: Кэширование API ответов

## 🔒 Безопасность

### Меры безопасности
- **CSRF Protection**: Встроенная защита Next.js
- **XSS Prevention**: Автоматическое экранирование
- **SQL Injection**: Prisma ORM защита
- **File Upload**: Валидация типов и размеров файлов
- **Rate Limiting**: Ограничение запросов (рекомендуется)

### Авторизация
- **Role-based Access**: Контроль доступа по ролям
- **Route Protection**: Защита административных маршрутов
- **API Security**: Проверка аутентификации в API

## 📱 Адаптивность

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Подходы
- **Mobile-first**: Дизайн начинается с мобильных устройств
- **Progressive Enhancement**: Улучшение для больших экранов
- **Touch-friendly**: Удобство для сенсорных устройств

## 🔄 Жизненный цикл разработки

### Разработка
1. **Local Development**: `npm run dev`
2. **Database**: Локальный PostgreSQL
3. **Hot Reload**: Автоматическое обновление

### Тестирование
1. **Type Checking**: TypeScript компиляция
2. **Linting**: ESLint проверки
3. **Manual Testing**: Ручное тестирование функций

### Деплой
1. **Build**: `npm run build`
2. **Database Migration**: Prisma migrate
3. **Static Export**: Генерация статических файлов
4. **Deployment**: Vercel или Docker

## 🔧 Конфигурация

### Переменные окружения
```env
# База данных
DATABASE_URL="postgresql://..."

# Аутентификация
NEXTAUTH_SECRET="..."
NEXTAUTH_URL="http://localhost:3000"

# Опционально
UPLOAD_DIR="/uploads"
MAX_FILE_SIZE="10485760"
```

### Next.js конфигурация
```javascript
// next.config.js
module.exports = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['localhost'],
  },
}
```

Эта архитектура обеспечивает масштабируемость, производительность и удобство разработки для сайта питомника.
