'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { formatDate, formatPrice, getGenderText, getPuppyStatusText } from '@/lib/utils';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface Dog {
  id: string;
  name: string;
  breed: string;
  gender: 'MALE' | 'FEMALE';
  slug: string;
}

interface Breeding {
  id: string;
  mother: Dog;
  father: Dog;
  date: string;
}

interface Puppy {
  id: string;
  name: string;
  gender: 'MALE' | 'FEMALE';
  birthDate: string;
  color: string | null;
  description: string | null;
  status: 'AVAILABLE' | 'RESERVED' | 'SOLD';
  price: number | null;
  isPublished: boolean;
  slug: string;
  breedingId: string | null;
  breeding: Breeding | null;
  createdAt: string;
  updatedAt: string;
  photos: Photo[];
}

export default function PuppyDetailsPage({ params }: { params: { slug: string } }) {
  const [puppy, setPuppy] = useState<Puppy | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchPuppy = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/puppies/${params.slug}`);
        
        if (!response.ok) {
          throw new Error('Ошибка при загрузке данных');
        }
        
        const data = await response.json();
        setPuppy(data);
      } catch (err) {
        setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
        console.error('Ошибка при загрузке щенка:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPuppy();
  }, [params.slug]);

  const handleDelete = async () => {
    setIsDeleting(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/puppies/${params.slug}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при удалении щенка');
      }
      
      router.push('/admin/puppies');
    } catch (err) {
      setError('Произошла ошибка при удалении щенка. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при удалении щенка:', err);
      setIsDeleting(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800';
      case 'RESERVED':
        return 'bg-yellow-100 text-yellow-800';
      case 'SOLD':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error || !puppy) {
    return (
      <AdminLayout title="Ошибка">
        <Alert type="error">{error || 'Щенок не найден'}</Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/puppies')}>
            Вернуться к списку щенков
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={puppy.name}>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            {puppy.breeding?.mother.breed || 'Порода не указана'} • {getGenderText(puppy.gender)}
          </p>
        </div>
        <div className="flex space-x-4">
          <Link href={`/admin/puppies/${puppy.slug}/edit`}>
            <Button variant="secondary">Редактировать</Button>
          </Link>
          <Button
            variant="danger"
            onClick={() => setShowDeleteConfirm(true)}
          >
            Удалить
          </Button>
        </div>
      </div>

      {showDeleteConfirm && (
        <div className="mb-6">
          <Alert type="warning" title="Подтверждение удаления" onClose={() => setShowDeleteConfirm(false)}>
            <p className="mb-4">
              Вы уверены, что хотите удалить щенка "{puppy.name}"? Это действие нельзя отменить.
            </p>
            <div className="flex justify-end space-x-4">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowDeleteConfirm(false)}
              >
                Отмена
              </Button>
              <Button
                variant="danger"
                size="sm"
                isLoading={isDeleting}
                onClick={handleDelete}
              >
                Удалить
              </Button>
            </div>
          </Alert>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            {puppy.photos.length > 0 ? (
              <div className="relative aspect-square">
                <img
                  src={puppy.photos.find(photo => photo.isMain)?.url || puppy.photos[0].url}
                  alt={puppy.name}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="aspect-square bg-gray-200 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-24 w-24 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
            )}

            {puppy.photos.length > 1 && (
              <div className="p-4 grid grid-cols-4 gap-2">
                {puppy.photos.map((photo) => (
                  <div
                    key={photo.id}
                    className={`aspect-square rounded-md overflow-hidden ${
                      photo.isMain ? 'ring-2 ring-blue-500' : ''
                    }`}
                  >
                    <img
                      src={photo.url}
                      alt={photo.title || puppy.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="mt-6 bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Статус</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Опубликовано</p>
                <p>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      puppy.isPublished
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {puppy.isPublished ? 'Да' : 'Нет'}
                  </span>
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Статус</p>
                <p>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(
                      puppy.status
                    )}`}
                  >
                    {getPuppyStatusText(puppy.status)}
                  </span>
                </p>
              </div>
              {puppy.status === 'AVAILABLE' && puppy.price !== null && (
                <div>
                  <p className="text-sm text-gray-500">Цена</p>
                  <p className="font-medium">{formatPrice(puppy.price)}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="md:col-span-2 space-y-6">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Основная информация</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Имя</p>
                <p className="font-medium">{puppy.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Пол</p>
                <p className="font-medium">{getGenderText(puppy.gender)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Дата рождения</p>
                <p className="font-medium">{formatDate(puppy.birthDate)}</p>
              </div>
              {puppy.color && (
                <div>
                  <p className="text-sm text-gray-500">Окрас</p>
                  <p className="font-medium">{puppy.color}</p>
                </div>
              )}
            </div>
          </div>

          {puppy.breeding && (
            <div className="bg-white shadow-md rounded-lg p-6">
              <h2 className="text-lg font-medium mb-4">Родители</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm text-gray-500 mb-2">Мать</h3>
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <p className="font-medium">{puppy.breeding.mother.name}</p>
                    <p className="text-sm text-gray-600">{puppy.breeding.mother.breed}</p>
                    <Link 
                      href={`/admin/dogs/${puppy.breeding.mother.slug}`}
                      className="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block"
                    >
                      Просмотреть
                    </Link>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm text-gray-500 mb-2">Отец</h3>
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <p className="font-medium">{puppy.breeding.father.name}</p>
                    <p className="text-sm text-gray-600">{puppy.breeding.father.breed}</p>
                    <Link 
                      href={`/admin/dogs/${puppy.breeding.father.slug}`}
                      className="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block"
                    >
                      Просмотреть
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          )}

          {puppy.description && (
            <div className="bg-white shadow-md rounded-lg p-6">
              <h2 className="text-lg font-medium mb-4">Описание</h2>
              <p className="whitespace-pre-line">{puppy.description}</p>
            </div>
          )}

          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Системная информация</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">ID</p>
                <p className="font-mono text-sm">{puppy.id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Slug</p>
                <p className="font-mono text-sm">{puppy.slug}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Создано</p>
                <p className="font-mono text-sm">{formatDate(puppy.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Обновлено</p>
                <p className="font-mono text-sm">{formatDate(puppy.updatedAt)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
