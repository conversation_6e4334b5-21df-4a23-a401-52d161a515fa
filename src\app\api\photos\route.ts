import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { optimizeImage } from '@/lib/utils/image';

// POST /api/photos - Загрузка фотографии (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;
    const isMain = formData.get('isMain') === 'true';
    const order = parseInt(formData.get('order') as string || '0');
    const galleryId = formData.get('galleryId') as string;
    const dogId = formData.get('dogId') as string;
    const puppyId = formData.get('puppyId') as string;
    const newsId = formData.get('newsId') as string;

    // Получаем размеры изображения, если они указаны
    const width = formData.get('width') ? parseInt(formData.get('width') as string) : undefined;
    const height = formData.get('height') ? parseInt(formData.get('height') as string) : undefined;
    const quality = formData.get('quality') ? parseInt(formData.get('quality') as string) : 80;
    const format = (formData.get('format') as 'jpeg' | 'png' | 'webp') || 'webp';

    if (!file) {
      return NextResponse.json(
        { error: 'Файл не найден' },
        { status: 400 }
      );
    }

    // Проверяем тип файла
    const fileType = file.type;
    if (!fileType.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Загружаемый файл должен быть изображением' },
        { status: 400 }
      );
    }

    // Получаем буфер изображения
    const buffer = Buffer.from(await file.arrayBuffer());

    // Оптимизируем и сохраняем изображение
    const imageUrl = await optimizeImage(buffer, {
      width,
      height,
      quality,
      format
    });

    // Создаем запись в базе данных
    const photo = await prisma.photo.create({
      data: {
        url: imageUrl,
        title,
        description,
        order,
        isMain,
        galleryId: galleryId || null,
        dogId: dogId || null,
        puppyId: puppyId || null,
        newsId: newsId || null
      }
    });

    // Если фото отмечено как главное, обновляем другие фото
    if (isMain) {
      if (galleryId) {
        await prisma.photo.updateMany({
          where: {
            galleryId,
            id: { not: photo.id }
          },
          data: { isMain: false }
        });
      } else if (dogId) {
        await prisma.photo.updateMany({
          where: {
            dogId,
            id: { not: photo.id }
          },
          data: { isMain: false }
        });
      } else if (puppyId) {
        await prisma.photo.updateMany({
          where: {
            puppyId,
            id: { not: photo.id }
          },
          data: { isMain: false }
        });
      } else if (newsId) {
        await prisma.photo.updateMany({
          where: {
            newsId,
            id: { not: photo.id }
          },
          data: { isMain: false }
        });
      }
    }

    return NextResponse.json(photo, { status: 201 });
  } catch (error) {
    console.error('Ошибка при загрузке фотографии:', error);
    return NextResponse.json(
      { error: 'Ошибка при загрузке фотографии' },
      { status: 500 }
    );
  }
}
