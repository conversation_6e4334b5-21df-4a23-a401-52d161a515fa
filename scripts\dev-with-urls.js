#!/usr/bin/env node

const { spawn } = require('child_process');
const os = require('os');

// Конфигурация
const PORT = process.env.PORT || 3000;
const DUCKDNS_DOMAIN = 'ta-shiba.duckdns.org';
const NGINX_PORT = 8080;

// Функция для получения локального IP
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

// Функция для красивого вывода URL-адресов
function displayURLs() {
  const localIP = getLocalIP();
  
  console.log('\n🚀 Dogs Website (Development) запущен!\n');
  
  console.log('📍 Локальные адреса:');
  console.log(`   ├─ Локальный:     http://localhost:${PORT}`);
  console.log(`   └─ В сети:        http://${localIP}:${PORT}`);
  
  console.log('\n🌐 Внешние адреса (через nginx):');
  console.log(`   ├─ Локальный:     http://localhost:${NGINX_PORT}`);
  console.log(`   ├─ В сети:        http://${localIP}:${NGINX_PORT}`);
  console.log(`   └─ Интернет:      http://${DUCKDNS_DOMAIN}:${NGINX_PORT}`);
  
  console.log('\n🔒 HTTPS адреса (после настройки SSL):');
  console.log(`   └─ Интернет:      https://${DUCKDNS_DOMAIN}`);
  
  console.log('\n💡 Советы:');
  console.log('   • Для разработки используйте http://localhost:3000');
  console.log('   • Hot reload включен - изменения применяются автоматически');
  console.log('   • Для тестирования в продакшн режиме: npm run build && npm run start');
  
  console.log('\n📋 Управление:');
  console.log('   • Ctrl+C для остановки сервера');
  console.log('   • Изменения в коде применяются автоматически');
  
  console.log('\n' + '─'.repeat(60) + '\n');
}

// Запуск Next.js dev сервера
function startNextDevServer() {
  const nextProcess = spawn('npx', ['next', 'dev', '-p', PORT], {
    stdio: 'pipe',
    shell: true
  });

  let serverStarted = false;

  nextProcess.stdout.on('data', (data) => {
    const output = data.toString();
    
    // Показываем кастомные URL только при первом запуске
    if (!serverStarted && (output.includes('Ready on') || output.includes('started server'))) {
      serverStarted = true;
      displayURLs();
    } else {
      // Фильтруем стандартный вывод Next.js
      if (!output.includes('Ready on') && !output.includes('Local:') && !output.includes('Network:')) {
        process.stdout.write(output);
      }
    }
  });

  nextProcess.stderr.on('data', (data) => {
    process.stderr.write(data);
  });

  nextProcess.on('close', (code) => {
    console.log(`\n🛑 Dev сервер остановлен с кодом ${code}`);
    process.exit(code);
  });

  // Обработка Ctrl+C
  process.on('SIGINT', () => {
    console.log('\n🛑 Получен сигнал остановки...');
    nextProcess.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Получен сигнал завершения...');
    nextProcess.kill('SIGTERM');
  });
}

// Запуск
console.log('🔄 Запуск Dogs Website (Development)...');
startNextDevServer();
