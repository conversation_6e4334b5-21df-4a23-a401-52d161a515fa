import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// POST /api/inquiries/[id]/comments - Добавление комментария к заявке (защищенный маршрут)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const inquiryId = params.id;
    const data = await request.json();
    
    // Проверяем, существует ли заявка
    const existingInquiry = await prisma.inquiry.findUnique({
      where: { id: inquiryId }
    });
    
    if (!existingInquiry) {
      return NextResponse.json(
        { error: 'Заявка не найдена' },
        { status: 404 }
      );
    }
    
    // Получаем администратора
    const admin = await prisma.admin.findUnique({
      where: { email: session.user?.email as string }
    });
    
    if (!admin) {
      return NextResponse.json(
        { error: 'Администратор не найден' },
        { status: 404 }
      );
    }
    
    // Создаем комментарий
    const comment = await prisma.inquiryComment.create({
      data: {
        text: data.text,
        inquiryId,
        adminId: admin.id
      },
      include: {
        admin: true
      }
    });
    
    return NextResponse.json(comment, { status: 201 });
  } catch (error) {
    console.error('Ошибка при добавлении комментария:', error);
    return NextResponse.json(
      { error: 'Ошибка при добавлении комментария' },
      { status: 500 }
    );
  }
}
