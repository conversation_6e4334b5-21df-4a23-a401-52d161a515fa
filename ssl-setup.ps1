# SSL Certificate Setup Script for Windows/Nginx
# This script helps set up SSL certificates using Certbot

param(
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [string]$Email = "",
    [string]$NginxPath = "C:\nginx"
)

Write-Host "SSL Certificate Setup for $Domain" -ForegroundColor Green

# Check if Certbot is installed
$CertbotPath = Get-Command certbot -ErrorAction SilentlyContinue

if (-not $CertbotPath) {
    Write-Host "Certbot not found. Please install Certbot first." -ForegroundColor Red
    Write-Host "Download from: https://certbot.eff.org/instructions?ws=nginx&os=windows" -ForegroundColor Yellow
    exit 1
}

if ([string]::IsNullOrEmpty($Email)) {
    $Email = Read-Host "Enter your email address for Let's Encrypt notifications"
}

Write-Host "Stopping nginx..." -ForegroundColor Yellow
& "$NginxPath\nginx.exe" -s stop

Write-Host "Obtaining SSL certificate..." -ForegroundColor Yellow

# Use standalone mode since nginx is stopped
$CertbotArgs = @(
    "certonly",
    "--standalone",
    "--email", $Email,
    "--agree-tos",
    "--no-eff-email",
    "-d", $Domain
)

& certbot @CertbotArgs

if ($LASTEXITCODE -eq 0) {
    Write-Host "Certificate obtained successfully!" -ForegroundColor Green
    
    # Update nginx configuration
    $ConfigFile = "nginx-dogs-site.conf"
    if (Test-Path $ConfigFile) {
        Write-Host "Updating nginx configuration..." -ForegroundColor Yellow
        
        $CertPath = "C:\Certbot\live\$Domain\fullchain.pem"
        $KeyPath = "C:\Certbot\live\$Domain\privkey.pem"
        
        $Config = Get-Content $ConfigFile -Raw
        $Config = $Config -replace "ssl_certificate /path/to/your/certificate.crt;", "ssl_certificate $CertPath;"
        $Config = $Config -replace "ssl_certificate_key /path/to/your/private.key;", "ssl_certificate_key $KeyPath;"
        
        Set-Content -Path $ConfigFile -Value $Config
        
        Write-Host "Configuration updated!" -ForegroundColor Green
    }
    
    Write-Host "Starting nginx..." -ForegroundColor Yellow
    & "$NginxPath\nginx.exe"
    
    Write-Host "SSL setup completed! Your site should now be available at https://$Domain" -ForegroundColor Green
} else {
    Write-Host "Failed to obtain certificate. Please check the errors above." -ForegroundColor Red
    Write-Host "Starting nginx..." -ForegroundColor Yellow
    & "$NginxPath\nginx.exe"
}
