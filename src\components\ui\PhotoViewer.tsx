'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
}

interface PhotoViewerProps {
  photos: Photo[];
  initialPhotoIndex: number;
  isOpen: boolean;
  onClose: () => void;
}

export default function PhotoViewer({
  photos,
  initialPhotoIndex,
  isOpen,
  onClose,
}: PhotoViewerProps) {
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(initialPhotoIndex);
  const [isLoading, setIsLoading] = useState(true);

  // Сбрасываем индекс текущей фотографии при открытии модального окна
  useEffect(() => {
    if (isOpen) {
      setCurrentPhotoIndex(initialPhotoIndex);
    }
  }, [isOpen, initialPhotoIndex]);

  // Обработчик нажатия клавиш
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 'ArrowLeft') {
        showPreviousPhoto();
      } else if (e.key === 'ArrowRight') {
        showNextPhoto();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentPhotoIndex, photos.length]);

  // Блокируем прокрутку страницы при открытом модальном окне
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const currentPhoto = photos[currentPhotoIndex];

  const showPreviousPhoto = () => {
    setIsLoading(true);
    setCurrentPhotoIndex((prevIndex) =>
      prevIndex === 0 ? photos.length - 1 : prevIndex - 1
    );
  };

  const showNextPhoto = () => {
    setIsLoading(true);
    setCurrentPhotoIndex((prevIndex) =>
      prevIndex === photos.length - 1 ? 0 : prevIndex + 1
    );
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/95">
      {/* Кнопка закрытия */}
      <button
        className="absolute top-4 right-4 bg-white/20 hover:bg-white/40 text-white p-2 rounded-full transition-colors z-50"
        onClick={onClose}
        aria-label="Закрыть"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>

      {/* Кнопка "Предыдущая фотография" */}
      <button
        className="absolute left-4 md:left-8 top-1/2 transform -translate-y-1/2 bg-white/10 hover:bg-white/30 text-white p-3 rounded-full transition-all z-50 group"
        onClick={showPreviousPhoto}
        aria-label="Предыдущая фотография"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 md:h-8 md:w-8 transform group-hover:-translate-x-1 transition-transform"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
        </svg>
      </button>

      {/* Кнопка "Следующая фотография" */}
      <button
        className="absolute right-4 md:right-8 top-1/2 transform -translate-y-1/2 bg-white/10 hover:bg-white/30 text-white p-3 rounded-full transition-all z-50 group"
        onClick={showNextPhoto}
        aria-label="Следующая фотография"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 md:h-8 md:w-8 transform group-hover:translate-x-1 transition-transform"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>

      {/* Контейнер для фотографии */}
      <div className="relative w-full h-full max-w-5xl max-h-[85vh] mx-auto flex flex-col">
        <div className="flex-grow flex items-center justify-center p-4">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-shiba-orange"></div>
            </div>
          )}
          <img
            src={currentPhoto.url}
            alt={currentPhoto.title || 'Фотография'}
            className="max-h-full max-w-full object-contain rounded-lg shadow-2xl"
            onLoad={handleImageLoad}
          />
        </div>

        {/* Информация о фотографии */}
        {(currentPhoto.title || currentPhoto.description) && (
          <div className="bg-gradient-to-t from-black/80 to-black/40 p-6 text-white rounded-b-lg mx-4 mb-4">
            {currentPhoto.title && (
              <h3 className="text-xl font-bold mb-2">{currentPhoto.title}</h3>
            )}
            {currentPhoto.description && (
              <p className="text-white/90">{currentPhoto.description}</p>
            )}
          </div>
        )}

        {/* Миниатюры фотографий */}
        <div className="absolute bottom-4 left-0 right-0">
          <div className="flex justify-center space-x-2 px-4 overflow-x-auto py-2">
            {photos.map((photo, index) => (
              <button
                key={photo.id}
                onClick={() => setCurrentPhotoIndex(index)}
                className={`relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 transition-all duration-300 ${
                  currentPhotoIndex === index
                    ? 'ring-2 ring-shiba-orange scale-110'
                    : 'opacity-60 hover:opacity-100'
                }`}
              >
                <img
                  src={photo.url}
                  alt={photo.title || 'Миниатюра'}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        </div>

        {/* Индикатор текущей фотографии */}
        <div className="absolute top-4 left-0 right-0 flex justify-center">
          <div className="bg-black/60 px-3 py-1 rounded-full text-white text-sm font-medium">
            {currentPhotoIndex + 1} / {photos.length}
          </div>
        </div>
      </div>
    </div>
  );
}
