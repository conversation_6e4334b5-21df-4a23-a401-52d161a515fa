const https = require('https');
const http = require('http');

async function testAuth() {
  console.log('🔐 Тестирование аутентификации...\n');

  const testUrls = [
    'http://localhost:8080/admin/login',
    'http://ta-shiba.duckdns.org:8080/admin/login',
    'http://localhost:8080/api/auth/providers',
    'http://ta-shiba.duckdns.org:8080/api/auth/providers'
  ];

  for (const url of testUrls) {
    try {
      console.log(`📡 Тестируем: ${url}`);
      
      const response = await fetch(url);
      const status = response.status;
      
      if (status === 200) {
        console.log(`   ✅ Статус: ${status} - OK`);
      } else {
        console.log(`   ❌ Статус: ${status} - Ошибка`);
      }
      
      // Проверяем заголовки
      const contentType = response.headers.get('content-type');
      console.log(`   📄 Content-Type: ${contentType}`);
      
      // Для API endpoints показываем содержимое
      if (url.includes('/api/auth/providers')) {
        const text = await response.text();
        console.log(`   📋 Ответ: ${text.substring(0, 100)}...`);
      }
      
    } catch (error) {
      console.log(`   ❌ Ошибка: ${error.message}`);
    }
    
    console.log('');
  }
}

testAuth().catch(console.error);
