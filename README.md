# 🐕 Сайт питомника собак породы Сиба-ину

Современный веб-сайт для питомника собак, построенный на Next.js 14 с полноценной системой управления контентом.

## 📋 Содержание

- [Обзор проекта](#обзор-проекта)
- [Технологии](#технологии)
- [Быстрый старт](#быстрый-старт)
- [Документация](#документация)
- [Структура проекта](#структура-проекта)
- [Развертывание](#развертывание)

## 🎯 Обзор проекта

Сайт питомника включает в себя:

### Публичная часть:
- 🏠 **Главная страница** с hero-слайдером
- 🐕 **Каталог собак** с фильтрацией и поиском
- 🐶 **Щенки на продажу** с системой бронирования
- 🏆 **Выпускники** - галерея взрослых собак
- 📰 **Новости и статьи** 
- 📞 **Контакты** с формой обратной связи
- 📱 **Адаптивный дизайн** для всех устройств

### Административная панель:
- 👥 **Управление пользователями** и ролями
- 🐕 **Управление собаками** (добавление, редактирование, фото)
- 🐶 **Управление щенками** и бронированиями
- 📰 **Управление новостями** и статьями
- 🎨 **Hero-слайдер** с загрузкой изображений
- ⚙️ **Настройки сайта** (логотип, контакты, SEO)
- 📊 **Статистика** и аналитика

## 🛠 Технологии

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS
- **База данных**: PostgreSQL + Prisma ORM
- **Аутентификация**: NextAuth.js
- **Загрузка файлов**: Multer + локальное хранение
- **Валидация**: React Hook Form + Zod
- **UI компоненты**: Собственная библиотека компонентов

## 🚀 Быстрый старт

### Предварительные требования
- Node.js 18+
- PostgreSQL
- npm или yarn

### Установка

1. **Клонируйте репозиторий**
```bash
git clone <repository-url>
cd dogs
```

2. **Установите зависимости**
```bash
npm install
```

3. **Настройте переменные окружения**
```bash
cp .env.example .env.local
```

Заполните `.env.local`:
```env
DATABASE_URL="postgresql://username:password@localhost:5432/dogs_db"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

4. **Настройте базу данных**
```bash
npx prisma migrate dev
npx prisma db seed
```

5. **Запустите сервер разработки**
```bash
npm run dev
```

Откройте [http://localhost:3000](http://localhost:3000) в браузере.

## 📚 Документация

- [📖 Архитектура системы](docs/ARCHITECTURE.md)
- [🗄️ Схема базы данных](docs/DATABASE.md)
- [🧩 Компоненты](docs/COMPONENTS.md)
- [👤 Руководство пользователя](docs/USER_GUIDE.md)
- [⚙️ Руководство администратора](docs/ADMIN_GUIDE.md)
- [🚀 Развертывание](docs/DEPLOYMENT.md)

## 📁 Структура проекта

```
src/
├── app/                    # App Router (Next.js 14)
│   ├── (admin)/           # Административные страницы
│   ├── api/               # API маршруты
│   └── ...                # Публичные страницы
├── components/            # React компоненты
│   ├── admin/            # Админские компоненты
│   ├── layout/           # Компоненты макета
│   └── ui/               # UI компоненты
├── lib/                  # Утилиты и конфигурация
├── styles/               # Глобальные стили
└── types/                # TypeScript типы

prisma/
├── schema.prisma         # Схема базы данных
├── migrations/           # Миграции
└── seed.ts              # Начальные данные

public/
├── images/              # Статические изображения
└── uploads/             # Загруженные файлы
```

## 🔐 Первый вход в админку

После установки создайте администратора:

```bash
npm run create-admin
```

Или войдите с тестовыми данными:
- **Email**: <EMAIL>
- **Пароль**: admin123

## 🌐 Развертывание

### Vercel (рекомендуется)
1. Подключите репозиторий к Vercel
2. Настройте переменные окружения
3. Подключите PostgreSQL базу данных
4. Деплой произойдет автоматически

### Docker
```bash
docker-compose up -d
```

Подробнее в [руководстве по развертыванию](docs/DEPLOYMENT.md).

## 🤝 Вклад в проект

1. Форкните репозиторий
2. Создайте ветку для новой функции
3. Внесите изменения
4. Создайте Pull Request

## 📄 Лицензия

MIT License - подробности в файле [LICENSE](LICENSE).

## 📞 Поддержка

Если у вас есть вопросы или проблемы:
- Создайте Issue в репозитории
- Обратитесь к [документации](docs/)
- Проверьте [FAQ](docs/FAQ.md)
