import Image from 'next/image';
import Link from 'next/link';
import Layout from '@/components/layout/Layout';
import { prisma } from '@/lib/db';
import { formatDate } from '@/lib/utils';
import { notFound } from 'next/navigation';
import NewsGallery from './NewsGallery';
import { PawPrint, Leaf, FloatingElements } from '@/components/ui/Decorations';

// Генерируем метаданные для страницы
export async function generateMetadata({ params }: { params: { slug: string } }) {
  const { slug } = await params;
  const news = await prisma.news.findUnique({
    where: { slug },
  });

  if (!news) {
    return {
      title: 'Новость не найдена',
      description: 'Запрашиваемая новость не найдена',
    };
  }

  return {
    title: `${news.title} - Питомник собак`,
    description: news.excerpt || (news.content && news.content.length > 160
      ? `${news.content.substring(0, 160)}...`
      : news.content) || 'Новости питомника собак',
  };
}

export default async function NewsPage({ params }: { params: { slug: string } }) {
  const { slug } = await params;
  // Получаем информацию о новости
  const news = await prisma.news.findUnique({
    where: {
      slug: slug,
      isPublished: true,
    },
    include: {
      photos: {
        orderBy: {
          order: 'asc',
        },
      },
    },
  });

  // Если новость не найдена, возвращаем 404
  if (!news) {
    notFound();
  }

  // Получаем другие новости
  const otherNews = await prisma.news.findMany({
    where: {
      isPublished: true,
      id: {
        not: news.id,
      },
    },
    include: {
      photos: {
        where: {
          isMain: true,
        },
        take: 1,
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
    take: 3,
  });

  return (
    <Layout>
      <section className="pt-16 pb-24 bg-forest-bg relative">
        <FloatingElements count={4} type="mixed" className="absolute inset-0 z-0 pointer-events-none" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="mb-8">
            <Link href="/news" className="text-forest-dark hover:text-shiba-orange flex items-center transition-colors">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Назад к списку новостей
            </Link>
          </div>

          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            {/* Заголовок новости */}
            <div className="relative">
              {news.photos.length > 0 ? (
                <div className="relative h-64 md:h-96">
                  <Image
                    src={news.photos[0].url}
                    alt={news.title}
                    fill
                    style={{ objectFit: 'cover' }}
                    priority
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>

                  <div className="absolute bottom-0 left-0 right-0 p-6 md:p-8">
                    <div className="inline-block bg-white/80 text-forest-dark text-sm font-medium px-3 py-1 rounded-full shadow-sm mb-3">
                      {formatDate(news.createdAt)}
                    </div>
                    <h1 className="text-3xl md:text-4xl font-bold text-white">{news.title}</h1>
                  </div>
                </div>
              ) : (
                <div className="p-6 md:p-8 bg-forest-bg/10">
                  <div className="inline-block bg-white text-forest-dark text-sm font-medium px-3 py-1 rounded-full shadow-sm mb-3">
                    {formatDate(news.createdAt)}
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-forest-dark">{news.title}</h1>
                </div>
              )}
            </div>

            {/* Содержимое новости */}
            <div className="p-6 md:p-8">
              <div className="prose max-w-none text-forest-dark">
                <div dangerouslySetInnerHTML={{ __html: news.content.replace(/\n/g, '<br>') }} />
              </div>

              {/* Галерея фотографий */}
              {news.photos.length > 1 && (
                <NewsGallery photos={news.photos} title={news.title} />
              )}
            </div>
          </div>

          {/* Другие новости */}
          {otherNews.length > 0 && (
            <div className="mt-16">
              <div className="flex items-center mb-8">
                <div className="h-px flex-grow bg-forest-light/20"></div>
                <div className="flex items-center mx-4">
                  <Leaf size="sm" className="mr-3 text-shiba-orange" />
                  <h2 className="text-2xl font-bold text-forest-dark">Другие новости</h2>
                </div>
                <div className="h-px flex-grow bg-forest-light/20"></div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {otherNews.map((item: any) => (
                  <Link
                    key={item.id}
                    href={`/news/${item.slug}`}
                    className="group"
                  >
                    <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform group-hover:translate-y-[-5px]">
                      <div className="relative h-48">
                        {item.photos.length > 0 ? (
                          <>
                            <Image
                              src={item.photos[0].url}
                              alt={item.title}
                              fill
                              style={{ objectFit: 'cover' }}
                              className="transition-transform duration-500 group-hover:scale-105"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          </>
                        ) : (
                          <div className="w-full h-full bg-forest-bg flex items-center justify-center">
                            <div className="text-center">
                              <svg className="w-12 h-12 mx-auto text-forest-light mb-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
                              </svg>
                              <span className="text-forest-medium">Нет изображения</span>
                            </div>
                          </div>
                        )}
                        <div className="absolute top-3 left-3">
                          <span className="bg-white/80 text-forest-dark text-xs font-medium px-3 py-1 rounded-full shadow-sm">
                            {formatDate(item.createdAt)}
                          </span>
                        </div>
                      </div>
                      <div className="p-5">
                        <h3 className="text-lg font-bold text-forest-dark mb-2 group-hover:text-shiba-orange transition-colors line-clamp-2">{item.title}</h3>
                        <p className="text-forest-medium mb-3 text-sm line-clamp-2">
                          {item.excerpt || (item.content && item.content.length > 100
                            ? `${item.content.substring(0, 100)}...`
                            : item.content)}
                        </p>
                        <div className="flex justify-end">
                          <span className="text-forest-dark font-medium text-sm group-hover:text-shiba-orange transition-colors flex items-center">
                            Читать далее
                            <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}

          <div className="absolute left-0 bottom-0 w-32 h-32 opacity-10">
            <PawPrint size="lg" />
          </div>
          <div className="absolute right-10 top-40 w-16 h-16 opacity-10">
            <Leaf size="md" />
          </div>
        </div>
      </section>
    </Layout>
  );
}
