import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';

/**
 * Оптимизирует изображение и сохраняет его в указанную директорию
 * @param buffer Буфер изображения
 * @param options Опции оптимизации
 * @returns Путь к сохраненному файлу
 */
export async function optimizeImage(
  buffer: Buffer,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'jpeg' | 'png' | 'webp';
    outputDir?: string;
  } = {}
) {
  const {
    width,
    height,
    quality = 80,
    format = 'webp',
    outputDir = path.join(process.cwd(), 'public', 'uploads')
  } = options;
  
  // Создаем директорию, если она не существует
  try {
    await fs.mkdir(outputDir, { recursive: true });
  } catch (error) {
    console.error('Ошибка при создании директории:', error);
  }
  
  // Генерируем уникальное имя файла
  const fileName = `${uuidv4()}.${format}`;
  const outputPath = path.join(outputDir, fileName);
  
  // Оптимизируем изображение
  let sharpInstance = sharp(buffer);
  
  // Изменяем размер, если указаны width или height
  if (width || height) {
    sharpInstance = sharpInstance.resize({
      width,
      height,
      fit: 'inside',
      withoutEnlargement: true
    });
  }
  
  // Конвертируем в нужный формат
  switch (format) {
    case 'jpeg':
      sharpInstance = sharpInstance.jpeg({ quality });
      break;
    case 'png':
      sharpInstance = sharpInstance.png({ quality });
      break;
    case 'webp':
      sharpInstance = sharpInstance.webp({ quality });
      break;
  }
  
  // Сохраняем файл
  await sharpInstance.toFile(outputPath);
  
  // Возвращаем путь к файлу относительно public
  return `/uploads/${fileName}`;
}
