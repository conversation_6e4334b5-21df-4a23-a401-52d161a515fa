import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Проверяем, является ли путь защищенным (админ-панель)
  const isAdminPath = pathname.startsWith('/admin');
  
  // Исключаем страницу входа из защищенных путей
  const isLoginPath = pathname === '/admin/login';

  // Если это не админ-панель или это страница входа, пропускаем запрос
  if (!isAdminPath || isLoginPath) {
    return NextResponse.next();
  }

  // Получаем токен из запроса
  const token = await getToken({ req: request });

  // Если токена нет, перенаправляем на страницу входа
  if (!token) {
    const url = new URL('/admin/login', request.url);
    url.searchParams.set('callbackUrl', encodeURI(pathname));
    return NextResponse.redirect(url);
  }

  // Если токен есть, пропускаем запрос
  return NextResponse.next();
}

// Указываем, для каких путей должен срабатывать middleware
export const config = {
  matcher: ['/admin/:path*'],
};
