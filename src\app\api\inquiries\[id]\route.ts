import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/inquiries/[id] - Получение информации о заявке (защищенный маршрут)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const id = params.id;
    
    const inquiry = await prisma.inquiry.findUnique({
      where: { id },
      include: {
        dog: true,
        puppy: true,
        comments: {
          include: {
            admin: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });
    
    if (!inquiry) {
      return NextResponse.json(
        { error: 'Заявка не найдена' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(inquiry);
  } catch (error) {
    console.error('Ошибка при получении информации о заявке:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении информации о заявке' },
      { status: 500 }
    );
  }
}

// PUT /api/inquiries/[id] - Обновление статуса заявки (защищенный маршрут)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const id = params.id;
    const data = await request.json();
    
    // Проверяем, существует ли заявка
    const existingInquiry = await prisma.inquiry.findUnique({
      where: { id }
    });
    
    if (!existingInquiry) {
      return NextResponse.json(
        { error: 'Заявка не найдена' },
        { status: 404 }
      );
    }
    
    // Обновляем статус заявки
    const updatedInquiry = await prisma.inquiry.update({
      where: { id },
      data: {
        status: data.status
      }
    });
    
    return NextResponse.json(updatedInquiry);
  } catch (error) {
    console.error('Ошибка при обновлении статуса заявки:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении статуса заявки' },
      { status: 500 }
    );
  }
}

// DELETE /api/inquiries/[id] - Удаление заявки (защищенный маршрут)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const id = params.id;
    
    // Проверяем, существует ли заявка
    const existingInquiry = await prisma.inquiry.findUnique({
      where: { id }
    });
    
    if (!existingInquiry) {
      return NextResponse.json(
        { error: 'Заявка не найдена' },
        { status: 404 }
      );
    }
    
    // Удаляем заявку
    await prisma.inquiry.delete({
      where: { id }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении заявки:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении заявки' },
      { status: 500 }
    );
  }
}
