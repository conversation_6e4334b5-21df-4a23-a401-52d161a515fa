# PowerShell script to start Dogs Website in Production Mode

Write-Host "Starting Dogs Website in Production Mode..." -ForegroundColor Green

# Set environment to production
$env:NODE_ENV = "production"

# Build the application
Write-Host "Building application..." -ForegroundColor Yellow
npm run build

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build successful! Starting application on port 3000..." -ForegroundColor Green
    npm run start
} else {
    Write-Host "Build failed! Please check the errors above." -ForegroundColor Red
    exit 1
}
