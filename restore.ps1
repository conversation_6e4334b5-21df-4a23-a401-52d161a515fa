param(
    [Parameter(Mandatory=$false)]
    [string]$BackupFile
)

Write-Host "🚀 Восстановление backup базы данных..." -ForegroundColor Green

if (-not $BackupFile) {
    Write-Host "❌ Укажите файл для восстановления" -ForegroundColor Red
    Write-Host "Использование: .\restore.ps1 имя_файла.json.gz" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📁 Доступные файлы:" -ForegroundColor Cyan
    
    if (Test-Path "backups") {
        Get-ChildItem "backups\*.json.gz" | ForEach-Object {
            $size = [math]::Round($_.Length / 1MB, 2)
            Write-Host "   - $($_.Name) ($size MB)" -ForegroundColor White
        }
    } else {
        Write-Host "   Папка backups не найдена" -ForegroundColor Red
    }
    
    exit 1
}

$BackupPath = "backups\$BackupFile"

if (-not (Test-Path $BackupPath)) {
    Write-Host "❌ Файл не найден: $BackupPath" -ForegroundColor Red
    Write-Host ""
    Write-Host "📁 Доступные файлы:" -ForegroundColor Cyan
    
    if (Test-Path "backups") {
        Get-ChildItem "backups\*.json.gz" | ForEach-Object {
            $size = [math]::Round($_.Length / 1MB, 2)
            Write-Host "   - $($_.Name) ($size MB)" -ForegroundColor White
        }
    }
    
    exit 1
}

Write-Host "✅ Файл найден: $BackupPath" -ForegroundColor Green
Write-Host "🔄 Запускаем восстановление..." -ForegroundColor Yellow

try {
    & node scripts/import-data.js $BackupPath
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Восстановление завершено успешно!" -ForegroundColor Green
    } else {
        Write-Host "❌ Ошибка при восстановлении (код: $LASTEXITCODE)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Ошибка при выполнении: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Нажмите любую клавишу для продолжения..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
