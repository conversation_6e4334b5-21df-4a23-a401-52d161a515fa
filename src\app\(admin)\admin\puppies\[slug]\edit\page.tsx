'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import Select from '@/components/ui/Select';
import FileUpload from '@/components/ui/FileUpload';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface Dog {
  id: string;
  name: string;
  breed: string;
  gender: 'MALE' | 'FEMALE';
  slug: string;
}

interface Breeding {
  id: string;
  mother: Dog;
  father: Dog;
  date: string;
}

interface Puppy {
  id: string;
  name: string;
  gender: 'MALE' | 'FEMALE';
  birthDate: string;
  color: string | null;
  description: string | null;
  status: 'AVAILABLE' | 'RESERVED' | 'SOLD';
  price: number | null;
  isPublished: boolean;
  slug: string;
  breedingId: string | null;
  breeding: Breeding | null;
  photos: Photo[];
}

const puppySchema = z.object({
  name: z.string().min(1, 'Имя обязательно'),
  gender: z.enum(['MALE', 'FEMALE']),
  birthDate: z.string().min(1, 'Дата рождения обязательна'),
  color: z.string().optional(),
  description: z.string().optional(),
  status: z.enum(['AVAILABLE', 'RESERVED', 'SOLD']).default('AVAILABLE'),
  price: z.string().optional(),
  isPublished: z.boolean().default(true),
  breedingId: z.string().optional(),
});

type PuppyFormData = z.infer<typeof puppySchema>;

export default function EditPuppyPage({ params }: { params: { slug: string } }) {
  const [puppy, setPuppy] = useState<Puppy | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [photos, setPhotos] = useState<File[]>([]);
  const [breedings, setBreedings] = useState<Breeding[]>([]);
  const [isLoadingBreedings, setIsLoadingBreedings] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm<PuppyFormData>({
    resolver: zodResolver(puppySchema),
  });

  const status = watch('status');

  useEffect(() => {
    const fetchPuppy = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/puppies/${params.slug}`);

        if (!response.ok) {
          throw new Error('Ошибка при загрузке данных');
        }

        const data: Puppy = await response.json();
        setPuppy(data);

        // Преобразуем данные для формы
        reset({
          name: data.name,
          gender: data.gender,
          birthDate: new Date(data.birthDate).toISOString().split('T')[0],
          color: data.color || '',
          description: data.description || '',
          status: data.status,
          price: data.price !== null ? data.price.toString() : '',
          isPublished: data.isPublished,
          breedingId: data.breedingId || undefined,
        });
      } catch (err) {
        setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
        console.error('Ошибка при загрузке щенка:', err);
      } finally {
        setIsLoading(false);
      }
    };

    const fetchBreedings = async () => {
      setIsLoadingBreedings(true);
      try {
        const response = await fetch('/api/breedings');

        if (!response.ok) {
          throw new Error('Ошибка при загрузке вязок');
        }

        const data = await response.json();

        // Фильтруем только завершенные и активные вязки
        const filteredBreedings = data.filter(
          (breeding: Breeding) => breeding.status === 'COMPLETED' || breeding.status === 'IN_PROGRESS'
        );

        setBreedings(filteredBreedings);
      } catch (err) {
        console.error('Ошибка при загрузке вязок:', err);
        setError('Произошла ошибка при загрузке списка вязок');
      } finally {
        setIsLoadingBreedings(false);
      }
    };

    fetchPuppy();
    fetchBreedings();
  }, [params.slug, reset]);

  const onSubmit = async (data: PuppyFormData) => {
    if (!puppy) return;

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Создаем объект с данными щенка
      const puppyData = {
        ...data,
        price: data.price ? parseFloat(data.price) : null,
      };

      // Отправляем запрос на обновление щенка
      const response = await fetch(`/api/puppies/${params.slug}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(puppyData),
      });

      if (!response.ok) {
        throw new Error('Ошибка при обновлении щенка');
      }

      // Если есть новые фотографии, загружаем их
      if (photos.length > 0) {
        for (let i = 0; i < photos.length; i++) {
          const formData = new FormData();
          formData.append('file', photos[i]);
          formData.append('puppyId', puppy.id);
          formData.append('isMain', i === 0 && puppy.photos.length === 0 ? 'true' : 'false');
          formData.append('order', (puppy.photos.length + i).toString());

          const photoResponse = await fetch('/api/photos', {
            method: 'POST',
            body: formData,
          });

          if (!photoResponse.ok) {
            console.error('Ошибка при загрузке фотографии:', await photoResponse.text());
          }
        }
      }

      setSuccess('Информация о щенке успешно обновлена');

      // Обновляем данные на странице
      const updatedPuppyResponse = await fetch(`/api/puppies/${params.slug}`);
      if (updatedPuppyResponse.ok) {
        const updatedPuppy = await updatedPuppyResponse.json();
        setPuppy(updatedPuppy);
      }
    } catch (err) {
      console.error('Ошибка при обновлении щенка:', err);
      setError('Произошла ошибка при обновлении информации о щенке. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePhotosChange = (files: File[]) => {
    setPhotos(files);
  };

  const handleDeletePhoto = async (photoId: string) => {
    if (!puppy) return;

    try {
      const response = await fetch(`/api/photos/${photoId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Ошибка при удалении фотографии');
      }

      // Обновляем данные на странице
      const updatedPuppyResponse = await fetch(`/api/puppies/${params.slug}`);
      if (updatedPuppyResponse.ok) {
        const updatedPuppy = await updatedPuppyResponse.json();
        setPuppy(updatedPuppy);
      }
    } catch (err) {
      console.error('Ошибка при удалении фотографии:', err);
      setError('Произошла ошибка при удалении фотографии. Пожалуйста, попробуйте позже.');
    }
  };

  const handleSetMainPhoto = async (photoId: string) => {
    if (!puppy) return;

    try {
      const response = await fetch(`/api/photos/${photoId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isMain: true }),
      });

      if (!response.ok) {
        throw new Error('Ошибка при установке главной фотографии');
      }

      // Обновляем данные на странице
      const updatedPuppyResponse = await fetch(`/api/puppies/${params.slug}`);
      if (updatedPuppyResponse.ok) {
        const updatedPuppy = await updatedPuppyResponse.json();
        setPuppy(updatedPuppy);
      }
    } catch (err) {
      console.error('Ошибка при установке главной фотографии:', err);
      setError('Произошла ошибка при установке главной фотографии. Пожалуйста, попробуйте позже.');
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error && !puppy) {
    return (
      <AdminLayout title="Ошибка">
        <Alert type="error">{error}</Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/puppies')}>
            Вернуться к списку щенков
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={`Редактирование: ${puppy?.name || ''}`}>
      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Основная информация</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Имя щенка"
              {...register('name')}
              error={errors.name?.message}
              fullWidth
            />
            <Controller
              name="gender"
              control={control}
              render={({ field }) => (
                <Select
                  label="Пол"
                  options={[
                    { value: 'MALE', label: 'Кобель' },
                    { value: 'FEMALE', label: 'Сука' },
                  ]}
                  {...field}
                  error={errors.gender?.message}
                  fullWidth
                />
              )}
            />
            <Input
              label="Дата рождения"
              type="date"
              {...register('birthDate')}
              error={errors.birthDate?.message}
              fullWidth
            />
            <Input
              label="Окрас"
              {...register('color')}
              error={errors.color?.message}
              fullWidth
            />
            <Controller
              name="breedingId"
              control={control}
              render={({ field }) => (
                <Select
                  label="Вязка"
                  options={[
                    { value: '', label: 'Выберите вязку' },
                    ...breedings.map((breeding) => ({
                      value: breeding.id,
                      label: `${breeding.mother.name} (${breeding.mother.breed}) x ${breeding.father.name} (${breeding.father.breed})`,
                    })),
                  ]}
                  {...field}
                  error={errors.breedingId?.message}
                  fullWidth
                  disabled={isLoadingBreedings}
                />
              )}
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Дополнительная информация</h2>
          <div className="space-y-6">
            <Textarea
              label="Описание"
              {...register('description')}
              error={errors.description?.message}
              rows={5}
              fullWidth
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Статус и продажа</h2>
          <div className="space-y-6">
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select
                  label="Статус"
                  options={[
                    { value: 'AVAILABLE', label: 'Доступен' },
                    { value: 'RESERVED', label: 'Зарезервирован' },
                    { value: 'SOLD', label: 'Продан' },
                  ]}
                  {...field}
                  error={errors.status?.message}
                  fullWidth
                />
              )}
            />

            {status === 'AVAILABLE' && (
              <Input
                label="Цена (руб.)"
                type="number"
                {...register('price')}
                error={errors.price?.message}
                fullWidth
              />
            )}

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPublished"
                {...register('isPublished')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
                Опубликовать на сайте
              </label>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Фотографии</h2>

          {puppy && puppy.photos.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Текущие фотографии</h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {puppy.photos.map((photo) => (
                  <div key={photo.id} className="relative group">
                    <div className={`relative aspect-square rounded-md overflow-hidden ${
                      photo.isMain ? 'ring-2 ring-blue-500' : ''
                    }`}>
                      <img
                        src={photo.url}
                        alt={photo.title || puppy.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <div className="flex space-x-2">
                        {!photo.isMain && (
                          <button
                            type="button"
                            onClick={() => handleSetMainPhoto(photo.id)}
                            className="bg-blue-500 text-white rounded-full p-1"
                            title="Сделать главной"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                          </button>
                        )}
                        <button
                          type="button"
                          onClick={() => handleDeletePhoto(photo.id)}
                          className="bg-red-500 text-white rounded-full p-1"
                          title="Удалить"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="space-y-6">
            <FileUpload
              label="Загрузите новые фотографии"
              accept="image/*"
              multiple
              onChange={handlePhotosChange}
              fullWidth
            />
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => router.push(`/admin/puppies/${params.slug}`)}
          >
            Отмена
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            Сохранить
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
