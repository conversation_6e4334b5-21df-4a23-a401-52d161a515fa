import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { prisma } from '@/lib/db';
import { verifyPassword } from './password';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Пароль', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          console.log('Отсутствуют учетные данные');
          return null;
        }

        try {
          const admin = await prisma.admin.findUnique({
            where: { email: credentials.email }
          });

          if (!admin) {
            console.log('Администратор не найден');
            return null;
          }

          const isPasswordValid = await verifyPassword(
            credentials.password,
            admin.password
          );

          if (!isPasswordValid) {
            console.log('Неверный пароль');
            return null;
          }

          console.log('Успешная аутентификация');
          return {
            id: admin.id,
            name: admin.name,
            email: admin.email
          };
        } catch (error) {
          console.error('Ошибка при аутентификации:', error);
          return null;
        }
      }
    })
  ],
  pages: {
    signIn: '/admin/login',
    error: '/admin/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 дней
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: false, // Для HTTP
      },
    },
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
      }
      return session;
    },
  },
  debug: process.env.NODE_ENV === 'development',
  useSecureCookies: false, // Отключаем HTTPS для HTTP
};
