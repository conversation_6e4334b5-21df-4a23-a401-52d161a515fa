'use client';

import { useState } from 'react';
import Image from 'next/image';
import PhotoViewer from '@/components/ui/PhotoViewer';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface NewsGalleryProps {
  photos: Photo[];
  title: string;
}

export default function NewsGallery({ photos, title }: NewsGalleryProps) {
  const [isPhotoViewerOpen, setIsPhotoViewerOpen] = useState(false);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);

  const openPhotoViewer = (index: number) => {
    setSelectedPhotoIndex(index);
    setIsPhotoViewerOpen(true);
  };

  const closePhotoViewer = () => {
    setIsPhotoViewerOpen(false);
  };

  // Если нет фотографий, не отображаем галерею
  if (!photos || photos.length === 0) {
    return null;
  }

  // Если есть только одна фотография (обложка), не отображаем галерею
  if (photos.length === 1) {
    return null;
  }

  return (
    <>
      <div className="mt-10">
        <div className="flex items-center mb-6">
          <div className="h-px flex-grow bg-forest-light/20"></div>
          <div className="flex items-center mx-4">
            <svg className="w-5 h-5 mr-2 text-shiba-orange" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path>
            </svg>
            <h3 className="text-xl font-bold text-forest-dark">Фотогалерея</h3>
          </div>
          <div className="h-px flex-grow bg-forest-light/20"></div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {photos.map((photo, index) => (
            <div
              key={photo.id}
              className="relative aspect-square rounded-lg overflow-hidden cursor-pointer group"
              onClick={() => openPhotoViewer(index)}
            >
              <Image
                src={photo.url}
                alt={photo.title || title}
                fill
                style={{ objectFit: 'cover' }}
                className="transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                {photo.title && (
                  <div className="p-4 w-full">
                    <p className="text-white font-medium">{photo.title}</p>
                    {photo.description && (
                      <p className="text-white text-sm">{photo.description}</p>
                    )}
                  </div>
                )}
              </div>
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="bg-white/80 rounded-full p-2">
                  <svg className="w-6 h-6 text-forest-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                  </svg>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <PhotoViewer
        photos={photos}
        initialPhotoIndex={selectedPhotoIndex}
        isOpen={isPhotoViewerOpen}
        onClose={closePhotoViewer}
      />
    </>
  );
}
