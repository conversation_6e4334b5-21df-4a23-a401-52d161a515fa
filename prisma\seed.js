const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Начинаем заполнение базы данных...');
  
  // Создаем администратора
  const adminPassword = await bcrypt.hash('admin123', 10);
  
  const admin = await prisma.admin.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Администратор',
      email: '<EMAIL>',
      password: adminPassword,
    },
  });
  
  console.log('✅ Администратор создан:', { id: admin.id, email: admin.email });
  
  // Создаем тестовые данные для собак
  const dog1 = await prisma.dog.upsert({
    where: { slug: 'rex' },
    update: {},
    create: {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      breed: 'Немецкая овчарка',
      gender: 'MALE',
      birthDate: new Date('2020-05-15'),
      color: 'Черно-подпалый',
      weight: 35.5,
      height: 65.0,
      pedigree: 'RKF123456',
      achievements: 'Чемпион России',
      description: 'Рекс - прекрасный представитель породы немецкая овчарка. Обладает отличным характером и экстерьером.',
      slug: 'rex',
      isForSale: false,
    },
  });

  const dog2 = await prisma.dog.upsert({
    where: { slug: 'bella' },
    update: {},
    create: {
      name: 'Белла',
      breed: 'Лабрадор',
      gender: 'FEMALE',
      birthDate: new Date('2019-08-20'),
      color: 'Палевый',
      weight: 28.0,
      height: 58.0,
      pedigree: 'RKF789012',
      achievements: 'Юный чемпион России',
      description: 'Белла - дружелюбная и активная собака породы лабрадор. Отлично ладит с детьми.',
      slug: 'bella',
      isForSale: true,
      price: 80000,
    },
  });
  
  console.log('✅ Собаки созданы:', { dog1: dog1.name, dog2: dog2.name });
  
  // Создаем тестовые данные для щенков
  const puppy1 = await prisma.puppy.upsert({
    where: { slug: 'puppy-1' },
    update: {},
    create: {
      name: 'Щенок 1',
      gender: 'MALE',
      birthDate: new Date('2023-04-15'),
      color: 'Черно-подпалый',
      description: 'Красивый и здоровый щенок немецкой овчарки.',
      slug: 'puppy-1',
      status: 'AVAILABLE',
      price: 50000,
    },
  });
  
  const puppy2 = await prisma.puppy.upsert({
    where: { slug: 'puppy-2' },
    update: {},
    create: {
      name: 'Щенок 2',
      gender: 'FEMALE',
      birthDate: new Date('2023-04-15'),
      color: 'Палевый',
      description: 'Красивый и здоровый щенок лабрадора.',
      slug: 'puppy-2',
      status: 'AVAILABLE',
      price: 45000,
    },
  });
  
  console.log('✅ Щенки созданы:', { puppy1: puppy1.name, puppy2: puppy2.name });
  
  // Создаем тестовые данные для новостей
  const news1 = await prisma.news.upsert({
    where: { slug: 'success-at-international-exhibition' },
    update: {},
    create: {
      title: 'Успешное выступление на международной выставке',
      content: 'Наши собаки заняли призовые места на международной выставке в Москве. Рекс получил титул CAC, а Белла - CACIB.',
      excerpt: 'Наши собаки заняли призовые места на международной выставке в Москве.',
      slug: 'success-at-international-exhibition',
    },
  });

  const news2 = await prisma.news.upsert({
    where: { slug: 'new-litter-announcement' },
    update: {},
    create: {
      title: 'Объявление о новом помете',
      content: 'Рады сообщить о рождении нового помета от Рекса и Беллы. Все щенки здоровы и активны.',
      excerpt: 'Рады сообщить о рождении нового помета от Рекса и Беллы.',
      slug: 'new-litter-announcement',
    },
  });
  
  console.log('✅ Новости созданы:', { news1: news1.title, news2: news2.title });
  
  console.log('🎉 База данных успешно заполнена!');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ Ошибка при заполнении базы данных:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
