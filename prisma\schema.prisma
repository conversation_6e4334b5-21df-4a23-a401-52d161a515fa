generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Admin {
  id              String           @id @default(cuid())
  name            String
  email           String           @unique
  password        String
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  inquiryComments InquiryComment[]
}

model Dog {
  id           String     @id @default(cuid())
  name         String
  breed        String
  gender       Gender
  birthDate    DateTime
  color        String?
  weight       Float?
  height       Float?
  pedigree     String?
  achievements String?
  description  String?
  slug         String     @unique
  isForSale    Boolean    @default(false)
  price        Float?
  isPublished  Boolean    @default(true)
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  isGraduate   Boolean    @default(false)
  sireOf       Breeding[] @relation("Father")
  breedings    Breeding[] @relation("Mother")
  inquiries    Inquiry[]
  photos       Photo[]
}

model Breeding {
  id          String         @id @default(cuid())
  date        DateTime
  description String?
  status      BreedingStatus @default(PLANNED)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  motherId    String
  fatherId    String
  father      Dog            @relation("Father", fields: [fatherId], references: [id])
  mother      Dog            @relation("Mother", fields: [motherId], references: [id])
  puppies     Puppy[]
}

model Puppy {
  id          String      @id @default(cuid())
  name        String?
  gender      Gender
  birthDate   DateTime
  color       String?
  description String?
  slug        String      @unique
  status      PuppyStatus @default(AVAILABLE)
  price       Float?
  isPublished Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  breedingId  String?
  inquiries   Inquiry[]
  photos      Photo[]
  breeding    Breeding?   @relation(fields: [breedingId], references: [id])
}

model GalleryCategory {
  id          String    @id @default(cuid())
  name        String
  description String?
  slug        String    @unique
  order       Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  albums      Gallery[]
}

model Gallery {
  id          String           @id @default(cuid())
  title       String
  description String?
  slug        String           @unique
  coverImage  String?
  order       Int              @default(0)
  isPublished Boolean          @default(true)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  categoryId  String?
  category    GalleryCategory? @relation(fields: [categoryId], references: [id])
  photos      Photo[]
}

model Photo {
  id          String   @id @default(cuid())
  url         String
  title       String?
  description String?
  order       Int      @default(0)
  isMain      Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  galleryId   String?
  dogId       String?
  puppyId     String?
  newsId      String?
  dog         Dog?     @relation(fields: [dogId], references: [id], onDelete: Cascade)
  gallery     Gallery? @relation(fields: [galleryId], references: [id], onDelete: Cascade)
  news        News?    @relation(fields: [newsId], references: [id], onDelete: Cascade)
  puppy       Puppy?   @relation(fields: [puppyId], references: [id], onDelete: Cascade)
}

model News {
  id          String   @id @default(cuid())
  title       String
  content     String
  excerpt     String?
  slug        String   @unique
  isPublished Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  photos      Photo[]
}

model Inquiry {
  id        String           @id @default(cuid())
  name      String
  phone     String
  email     String
  message   String?
  status    InquiryStatus    @default(NEW)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  dogId     String?
  puppyId   String?
  dog       Dog?             @relation(fields: [dogId], references: [id])
  puppy     Puppy?           @relation(fields: [puppyId], references: [id])
  comments  InquiryComment[]
}

model InquiryComment {
  id        String   @id @default(cuid())
  text      String
  createdAt DateTime @default(now())
  inquiryId String
  adminId   String
  admin     Admin    @relation(fields: [adminId], references: [id])
  inquiry   Inquiry  @relation(fields: [inquiryId], references: [id], onDelete: Cascade)
}

model Settings {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model HeroSlide {
  id         String   @id @default(cuid())
  title      String?
  subtitle   String?
  imageUrl   String
  buttonText String?
  buttonLink String?
  order      Int      @default(0)
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model Contact {
  id          String      @id @default(cuid())
  type        ContactType
  value       String
  description String?
  isPublic    Boolean     @default(true)
  order       Int         @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

enum Gender {
  MALE
  FEMALE
}

enum BreedingStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum PuppyStatus {
  AVAILABLE
  RESERVED
  SOLD
}

enum InquiryStatus {
  NEW
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum ContactType {
  PHONE
  EMAIL
  ADDRESS
  SOCIAL
}
