# 🚀 Руководство по развертыванию

## Обзор

Сайт питомника может быть развернут несколькими способами. Рекомендуется использовать Vercel для простоты и производительности, но также доступны варианты с Docker и традиционным VPS.

## 🌟 Развертывание на Vercel (Рекомендуется)

### Преимущества Vercel
- ✅ Автоматический деплой из Git
- ✅ Встроенная оптимизация Next.js
- ✅ Глобальная CDN
- ✅ Автоматические SSL сертификаты
- ✅ Простое управление переменными окружения
- ✅ Бесплатный план для небольших проектов

### Пошаговая инструкция

#### 1. Подготовка репозитория
```bash
# Убедитесь, что код находится в Git репозитории
git add .
git commit -m "Готов к деплою"
git push origin main
```

#### 2. Создание проекта на Vercel
1. Зайдите на [vercel.com](https://vercel.com)
2. Войдите через GitHub/GitLab/Bitbucket
3. Нажмите **"New Project"**
4. Выберите ваш репозиторий
5. Настройте параметры:
   - **Framework Preset**: Next.js
   - **Root Directory**: `./` (если проект в корне)
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`

#### 3. Настройка базы данных
**Вариант A: Vercel Postgres (рекомендуется)**
1. В дашборде проекта перейдите в **Storage**
2. Создайте **Postgres Database**
3. Скопируйте `DATABASE_URL` из настроек

**Вариант B: Внешняя база данных**
- [Supabase](https://supabase.com) - бесплатный PostgreSQL
- [PlanetScale](https://planetscale.com) - MySQL совместимая
- [Railway](https://railway.app) - PostgreSQL

#### 4. Переменные окружения
В настройках проекта Vercel добавьте:

```env
# База данных
DATABASE_URL="postgresql://username:password@host:port/database"

# Аутентификация
NEXTAUTH_SECRET="your-super-secret-key-here"
NEXTAUTH_URL="https://your-domain.vercel.app"

# Опционально
UPLOAD_DIR="/tmp/uploads"
MAX_FILE_SIZE="10485760"
```

#### 5. Миграция базы данных
```bash
# Локально выполните миграции
npx prisma migrate deploy
npx prisma db seed
```

#### 6. Деплой
1. Нажмите **"Deploy"** в Vercel
2. Дождитесь завершения сборки
3. Проверьте работу сайта по предоставленному URL

### Автоматические деплои
После настройки каждый push в main ветку будет автоматически деплоить сайт.

## 🐳 Развертывание с Docker

### Создание Dockerfile
```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/dogs_db
      - NEXTAUTH_SECRET=your-secret-key
      - NEXTAUTH_URL=http://localhost:3000
    depends_on:
      - db
    volumes:
      - ./uploads:/app/public/uploads

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=dogs_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

### Запуск с Docker
```bash
# Сборка и запуск
docker-compose up -d

# Миграции базы данных
docker-compose exec app npx prisma migrate deploy
docker-compose exec app npx prisma db seed

# Просмотр логов
docker-compose logs -f app
```

## 🖥️ Развертывание на VPS

### Требования к серверу
- **OS**: Ubuntu 20.04+ или CentOS 8+
- **RAM**: Минимум 1GB, рекомендуется 2GB+
- **CPU**: 1 ядро минимум
- **Диск**: 20GB+ свободного места
- **Node.js**: 18+
- **PostgreSQL**: 13+

### Установка зависимостей
```bash
# Обновление системы
sudo apt update && sudo apt upgrade -y

# Установка Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Установка PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Установка PM2 для управления процессами
sudo npm install -g pm2

# Установка Nginx
sudo apt install nginx -y
```

### Настройка базы данных
```bash
# Переключение на пользователя postgres
sudo -u postgres psql

# Создание базы данных и пользователя
CREATE DATABASE dogs_db;
CREATE USER dogs_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE dogs_db TO dogs_user;
\q
```

### Клонирование и настройка проекта
```bash
# Клонирование репозитория
git clone <your-repository-url> /var/www/dogs
cd /var/www/dogs

# Установка зависимостей
npm install

# Создание .env файла
cp .env.example .env.local

# Редактирование переменных окружения
nano .env.local
```

### Переменные окружения для production
```env
DATABASE_URL="postgresql://dogs_user:secure_password@localhost:5432/dogs_db"
NEXTAUTH_SECRET="your-super-secret-production-key"
NEXTAUTH_URL="https://yourdomain.com"
NODE_ENV="production"
```

### Сборка и миграции
```bash
# Генерация Prisma клиента
npx prisma generate

# Миграции базы данных
npx prisma migrate deploy

# Заполнение начальными данными
npx prisma db seed

# Сборка приложения
npm run build
```

### Настройка PM2
```bash
# Создание ecosystem файла
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'dogs-website',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/dogs',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOF

# Запуск приложения
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Настройка Nginx
```bash
# Создание конфигурации Nginx
sudo cat > /etc/nginx/sites-available/dogs << EOF
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # Статические файлы
    location /_next/static {
        alias /var/www/dogs/.next/static;
        expires 365d;
        access_log off;
    }

    location /uploads {
        alias /var/www/dogs/public/uploads;
        expires 30d;
        access_log off;
    }
}
EOF

# Активация сайта
sudo ln -s /etc/nginx/sites-available/dogs /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### SSL сертификат (Let's Encrypt)
```bash
# Установка Certbot
sudo apt install certbot python3-certbot-nginx -y

# Получение сертификата
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Автоматическое обновление
sudo crontab -e
# Добавить строку:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔧 Настройка окружения

### Переменные окружения
```env
# Обязательные
DATABASE_URL="postgresql://..."
NEXTAUTH_SECRET="random-secret-key"
NEXTAUTH_URL="https://yourdomain.com"

# Опциональные
UPLOAD_DIR="/uploads"
MAX_FILE_SIZE="10485760"
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
```

### Генерация секретного ключа
```bash
# Генерация случайного ключа
openssl rand -base64 32
```

## 📊 Мониторинг и логи

### PM2 мониторинг
```bash
# Статус приложений
pm2 status

# Логи
pm2 logs dogs-website

# Мониторинг ресурсов
pm2 monit

# Перезапуск
pm2 restart dogs-website
```

### Nginx логи
```bash
# Логи доступа
sudo tail -f /var/log/nginx/access.log

# Логи ошибок
sudo tail -f /var/log/nginx/error.log
```

## 🔄 Обновление приложения

### Автоматическое обновление (Vercel)
Обновления происходят автоматически при push в репозиторий.

### Ручное обновление (VPS)
```bash
cd /var/www/dogs

# Получение обновлений
git pull origin main

# Установка новых зависимостей
npm install

# Миграции базы данных
npx prisma migrate deploy

# Сборка
npm run build

# Перезапуск
pm2 restart dogs-website
```

## 🛡️ Безопасность

### Рекомендации
- ✅ Используйте сильные пароли
- ✅ Регулярно обновляйте зависимости
- ✅ Настройте файрвол
- ✅ Используйте HTTPS
- ✅ Регулярно делайте резервные копии
- ✅ Мониторьте логи на подозрительную активность

### Файрвол (UFW)
```bash
# Включение файрвола
sudo ufw enable

# Разрешение SSH
sudo ufw allow ssh

# Разрешение HTTP/HTTPS
sudo ufw allow 'Nginx Full'

# Проверка статуса
sudo ufw status
```

## 💾 Резервное копирование

### База данных
```bash
# Создание бэкапа
pg_dump -h localhost -U dogs_user dogs_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Восстановление
psql -h localhost -U dogs_user dogs_db < backup_file.sql
```

### Файлы
```bash
# Бэкап загруженных файлов
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz /var/www/dogs/public/uploads

# Автоматический бэкап (cron)
0 2 * * * /path/to/backup_script.sh
```

## 🆘 Решение проблем

### Частые проблемы

**Ошибка подключения к базе данных:**
- Проверьте DATABASE_URL
- Убедитесь, что PostgreSQL запущен
- Проверьте права доступа пользователя

**Ошибки при сборке:**
- Очистите кэш: `npm run clean`
- Переустановите зависимости: `rm -rf node_modules && npm install`
- Проверьте версию Node.js

**Проблемы с загрузкой файлов:**
- Проверьте права доступа к папке uploads
- Убедитесь в наличии свободного места
- Проверьте настройки Nginx для больших файлов

### Полезные команды
```bash
# Проверка статуса сервисов
sudo systemctl status nginx
sudo systemctl status postgresql
pm2 status

# Просмотр использования ресурсов
htop
df -h
free -h

# Проверка портов
sudo netstat -tlnp | grep :3000
```

---

**Успешного развертывания! При возникновении проблем обращайтесь к документации или технической поддержке. 🚀**
