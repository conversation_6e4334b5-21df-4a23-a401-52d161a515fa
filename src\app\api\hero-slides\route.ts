import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/hero-slides - Получение всех hero-слайдов
export async function GET(request: NextRequest) {
  try {
    const slides = await prisma.heroSlide.findMany({
      orderBy: {
        order: 'asc'
      }
    });

    return NextResponse.json(slides);
  } catch (error) {
    console.error('Ошибка при получении hero-слайдов:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении hero-слайдов' },
      { status: 500 }
    );
  }
}

// POST /api/hero-slides - Создание нового hero-слайда (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Получаем максимальный порядок для нового слайда
    const maxOrder = await prisma.heroSlide.findFirst({
      orderBy: { order: 'desc' },
      select: { order: true }
    });

    const newSlide = await prisma.heroSlide.create({
      data: {
        title: data.title || null,
        subtitle: data.subtitle || null,
        imageUrl: data.imageUrl,
        buttonText: data.buttonText || null,
        buttonLink: data.buttonLink || null,
        order: (maxOrder?.order || 0) + 1,
        isActive: data.isActive ?? true,
      }
    });

    return NextResponse.json(newSlide);
  } catch (error) {
    console.error('Ошибка при создании hero-слайда:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании hero-слайда' },
      { status: 500 }
    );
  }
}

// PUT /api/hero-slides - Обновление порядка слайдов (защищенный маршрут)
export async function PUT(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const { slides } = await request.json();

    // Обновляем порядок слайдов
    const updatePromises = slides.map((slide: any, index: number) =>
      prisma.heroSlide.update({
        where: { id: slide.id },
        data: { order: index }
      })
    );

    await Promise.all(updatePromises);

    const updatedSlides = await prisma.heroSlide.findMany({
      orderBy: { order: 'asc' }
    });

    return NextResponse.json(updatedSlides);
  } catch (error) {
    console.error('Ошибка при обновлении порядка слайдов:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении порядка слайдов' },
      { status: 500 }
    );
  }
}
