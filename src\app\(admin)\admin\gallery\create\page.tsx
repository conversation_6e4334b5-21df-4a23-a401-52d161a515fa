'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import Select from '@/components/ui/Select';
import FileUpload from '@/components/ui/FileUpload';
import Alert from '@/components/ui/Alert';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { slugify } from '@/lib/utils';

interface Category {
  id: string;
  name: string;
  slug: string;
}

const albumSchema = z.object({
  title: z.string().min(1, 'Название альбома обязательно'),
  description: z.string().optional(),
  categoryId: z.string().optional(),
  isPublished: z.boolean().default(false),
});

type AlbumFormData = z.infer<typeof albumSchema>;

export default function CreateAlbumPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [photos, setPhotos] = useState<File[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<AlbumFormData>({
    resolver: zodResolver(albumSchema),
    defaultValues: {
      isPublished: false,
    },
  });

  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoadingCategories(true);
      try {
        const response = await fetch('/api/gallery/categories');
        
        if (!response.ok) {
          throw new Error('Ошибка при загрузке категорий');
        }
        
        const data = await response.json();
        setCategories(data);
      } catch (err) {
        console.error('Ошибка при загрузке категорий:', err);
        setError('Произошла ошибка при загрузке списка категорий');
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  const onSubmit = async (data: AlbumFormData) => {
    if (photos.length === 0) {
      setError('Добавьте хотя бы одну фотографию в альбом');
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Создаем slug из названия альбома
      const slug = slugify(data.title);

      // Создаем объект с данными альбома
      const albumData = {
        ...data,
        slug,
      };

      // Отправляем запрос на создание альбома
      const response = await fetch('/api/gallery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(albumData),
      });

      if (!response.ok) {
        throw new Error('Ошибка при создании альбома');
      }

      const createdAlbum = await response.json();

      // Загружаем фотографии
      for (let i = 0; i < photos.length; i++) {
        const formData = new FormData();
        formData.append('file', photos[i]);
        formData.append('albumId', createdAlbum.id);
        formData.append('isMain', i === 0 ? 'true' : 'false');
        formData.append('order', i.toString());

        const photoResponse = await fetch('/api/gallery/photos', {
          method: 'POST',
          body: formData,
        });

        if (!photoResponse.ok) {
          console.error('Ошибка при загрузке фотографии:', await photoResponse.text());
        }
      }

      setSuccess('Альбом успешно создан');
      
      // Перенаправляем на страницу со списком альбомов
      setTimeout(() => {
        router.push('/admin/gallery');
      }, 2000);
    } catch (err) {
      console.error('Ошибка при создании альбома:', err);
      setError('Произошла ошибка при создании альбома. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePhotosChange = (files: File[]) => {
    setPhotos(files);
  };

  return (
    <AdminLayout title="Создание нового альбома">
      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Основная информация</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Название альбома"
              {...register('title')}
              error={errors.title?.message}
              fullWidth
            />
            <Controller
              name="categoryId"
              control={control}
              render={({ field }) => (
                <Select
                  label="Категория"
                  options={[
                    { value: '', label: 'Без категории' },
                    ...categories.map((category) => ({
                      value: category.id,
                      label: category.name,
                    })),
                  ]}
                  {...field}
                  error={errors.categoryId?.message}
                  fullWidth
                  disabled={isLoadingCategories}
                />
              )}
            />
            <div className="md:col-span-2">
              <Textarea
                label="Описание"
                {...register('description')}
                error={errors.description?.message}
                rows={3}
                fullWidth
              />
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Фотографии</h2>
          <div className="space-y-6">
            <FileUpload
              label="Загрузите фотографии для альбома (первая фотография будет использоваться как обложка)"
              accept="image/*"
              multiple
              onChange={handlePhotosChange}
              fullWidth
            />
            {photos.length > 0 && (
              <p className="text-sm text-gray-500">
                Выбрано фотографий: {photos.length}
              </p>
            )}
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Публикация</h2>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isPublished"
              {...register('isPublished')}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
              Опубликовать на сайте
            </label>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => router.push('/admin/gallery')}
          >
            Отмена
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            Создать
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
