#!/usr/bin/env node

const { spawn } = require('child_process');
const os = require('os');

// Конфигурация
const PORT = process.env.PORT || 3000;
const DUCKDNS_DOMAIN = 'ta-shiba.duckdns.org';
const NGINX_PORT = 8080;

// Функция для получения локального IP
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

// Функция для красивого вывода URL-адресов
function displayURLs() {
  const localIP = getLocalIP();
  
  console.log('\n🚀 Dogs Website запущен!\n');
  
  console.log('📍 Локальные адреса:');
  console.log(`   ├─ Локальный:     http://localhost:${PORT}`);
  console.log(`   └─ В сети:        http://${localIP}:${PORT}`);
  
  console.log('\n🌐 Внешние адреса (через nginx):');
  console.log(`   ├─ Локальный:     http://localhost:${NGINX_PORT}`);
  console.log(`   ├─ В сети:        http://${localIP}:${NGINX_PORT}`);
  console.log(`   └─ Интернет:      http://${DUCKDNS_DOMAIN}:${NGINX_PORT}`);
  
  console.log('\n🔒 HTTPS адреса (после настройки SSL):');
  console.log(`   └─ Интернет:      https://${DUCKDNS_DOMAIN}`);
  
  console.log('\n💡 Советы:');
  console.log('   • Для локальной разработки используйте http://localhost:3000');
  console.log('   • Для тестирования в сети используйте nginx на порту 8080');
  console.log('   • Для доступа из интернета настройте DuckDNS и проброс портов');
  
  console.log('\n📋 Управление:');
  console.log('   • Ctrl+C для остановки сервера');
  console.log('   • npm run build для сборки продакшн версии');
  console.log('   • npm run start для запуска продакшн сервера');
  
  console.log('\n' + '─'.repeat(60) + '\n');
}

// Запуск Next.js сервера
function startNextServer() {
  const nextProcess = spawn('npx', ['next', 'start', '-p', PORT], {
    stdio: 'pipe',
    shell: true
  });

  let serverStarted = false;

  nextProcess.stdout.on('data', (data) => {
    const output = data.toString();
    
    // Показываем кастомные URL только при первом запуске
    if (!serverStarted && (output.includes('Ready on') || output.includes('started server'))) {
      serverStarted = true;
      displayURLs();
    } else {
      // Фильтруем стандартный вывод Next.js
      if (!output.includes('Ready on') && !output.includes('Local:') && !output.includes('Network:')) {
        process.stdout.write(output);
      }
    }
  });

  nextProcess.stderr.on('data', (data) => {
    process.stderr.write(data);
  });

  nextProcess.on('close', (code) => {
    console.log(`\n🛑 Сервер остановлен с кодом ${code}`);
    process.exit(code);
  });

  // Обработка Ctrl+C
  process.on('SIGINT', () => {
    console.log('\n🛑 Получен сигнал остановки...');
    nextProcess.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Получен сигнал завершения...');
    nextProcess.kill('SIGTERM');
  });
}

// Запуск
console.log('🔄 Запуск Dogs Website...');
startNextServer();
