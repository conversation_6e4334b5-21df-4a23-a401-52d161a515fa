'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import Select from '@/components/ui/Select';
import Alert from '@/components/ui/Alert';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

interface Dog {
  id: string;
  name: string;
  breed: string;
  gender: 'MALE' | 'FEMALE';
}

const breedingSchema = z.object({
  motherId: z.string().min(1, 'Выберите мать'),
  fatherId: z.string().min(1, 'Выберите отца'),
  date: z.string().min(1, 'Дата обязательна'),
  description: z.string().optional(),
  status: z.enum(['PLANNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).default('PLANNED'),
});

type BreedingFormData = z.infer<typeof breedingSchema>;

export default function CreateBreedingPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [females, setFemales] = useState<Dog[]>([]);
  const [males, setMales] = useState<Dog[]>([]);
  const [isLoadingDogs, setIsLoadingDogs] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors },
  } = useForm<BreedingFormData>({
    resolver: zodResolver(breedingSchema),
    defaultValues: {
      status: 'PLANNED',
    },
  });

  useEffect(() => {
    const fetchDogs = async () => {
      setIsLoadingDogs(true);
      try {
        const response = await fetch('/api/dogs');
        
        if (!response.ok) {
          throw new Error('Ошибка при загрузке собак');
        }
        
        const data = await response.json();
        
        // Разделяем собак по полу
        const femalesDogs = data.filter((dog: Dog) => dog.gender === 'FEMALE');
        const malesDogs = data.filter((dog: Dog) => dog.gender === 'MALE');
        
        setFemales(femalesDogs);
        setMales(malesDogs);
      } catch (err) {
        console.error('Ошибка при загрузке собак:', err);
        setError('Произошла ошибка при загрузке списка собак');
      } finally {
        setIsLoadingDogs(false);
      }
    };

    fetchDogs();
  }, []);

  const onSubmit = async (data: BreedingFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Отправляем запрос на создание вязки
      const response = await fetch('/api/breedings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Ошибка при создании вязки');
      }

      setSuccess('Вязка успешно создана');
      
      // Перенаправляем на страницу со списком вязок
      setTimeout(() => {
        router.push('/admin/breedings');
      }, 2000);
    } catch (err) {
      console.error('Ошибка при создании вязки:', err);
      setError('Произошла ошибка при создании вязки. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AdminLayout title="Добавление новой вязки">
      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Основная информация</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Controller
              name="motherId"
              control={control}
              render={({ field }) => (
                <Select
                  label="Мать"
                  options={[
                    { value: '', label: 'Выберите мать' },
                    ...females.map((dog) => ({
                      value: dog.id,
                      label: `${dog.name} (${dog.breed})`,
                    })),
                  ]}
                  {...field}
                  error={errors.motherId?.message}
                  fullWidth
                  disabled={isLoadingDogs}
                />
              )}
            />
            <Controller
              name="fatherId"
              control={control}
              render={({ field }) => (
                <Select
                  label="Отец"
                  options={[
                    { value: '', label: 'Выберите отца' },
                    ...males.map((dog) => ({
                      value: dog.id,
                      label: `${dog.name} (${dog.breed})`,
                    })),
                  ]}
                  {...field}
                  error={errors.fatherId?.message}
                  fullWidth
                  disabled={isLoadingDogs}
                />
              )}
            />
            <Input
              label="Дата вязки"
              type="date"
              {...register('date')}
              error={errors.date?.message}
              fullWidth
            />
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select
                  label="Статус"
                  options={[
                    { value: 'PLANNED', label: 'Запланирована' },
                    { value: 'IN_PROGRESS', label: 'В процессе' },
                    { value: 'COMPLETED', label: 'Завершена' },
                    { value: 'CANCELLED', label: 'Отменена' },
                  ]}
                  {...field}
                  error={errors.status?.message}
                  fullWidth
                />
              )}
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Дополнительная информация</h2>
          <div className="space-y-6">
            <Textarea
              label="Описание"
              {...register('description')}
              error={errors.description?.message}
              rows={5}
              fullWidth
            />
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => router.push('/admin/breedings')}
          >
            Отмена
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            Создать
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
