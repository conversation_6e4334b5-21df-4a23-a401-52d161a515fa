# Windows Firewall Setup for Dogs Website
# Run this script as Administrator

Write-Host "Configuring Windows Firewall for Dogs Website..." -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInR<PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script must be run as Administrator!" -ForegroundColor Red
    exit 1
}

# Function to create firewall rule
function New-FirewallRule {
    param(
        [string]$RuleName,
        [string]$Port,
        [string]$Protocol = "TCP",
        [string]$Direction = "Inbound",
        [string]$Action = "Allow"
    )
    
    # Remove existing rule if it exists
    $ExistingRule = Get-NetFirewallRule -DisplayName $RuleName -ErrorAction SilentlyContinue
    if ($ExistingRule) {
        Write-Host "Removing existing rule: $RuleName" -ForegroundColor Yellow
        Remove-NetFirewallRule -DisplayName $RuleName
    }
    
    # Create new rule
    Write-Host "Creating firewall rule: $RuleName (Port $Port)" -ForegroundColor Yellow
    New-NetFirewallRule -DisplayName $RuleName -Direction $Direction -Protocol $Protocol -LocalPort $Port -Action $Action
}

try {
    # HTTP (Port 80) - for Let's Encrypt certificate validation and HTTP to HTTPS redirect
    New-FirewallRule -RuleName "Dogs Website - HTTP (Port 80)" -Port "80"
    
    # HTTPS (Port 443) - for secure web traffic
    New-FirewallRule -RuleName "Dogs Website - HTTPS (Port 443)" -Port "443"
    
    # Next.js Development (Port 3000) - for direct access to Next.js app
    New-FirewallRule -RuleName "Dogs Website - Next.js (Port 3000)" -Port "3000"
    
    # PostgreSQL (Port 5432) - only if database is accessed remotely
    $PostgreSQLChoice = Read-Host "Do you want to allow remote access to PostgreSQL database? (y/N)"
    if ($PostgreSQLChoice -eq "y" -or $PostgreSQLChoice -eq "Y") {
        New-FirewallRule -RuleName "Dogs Website - PostgreSQL (Port 5432)" -Port "5432"
        Write-Host "WARNING: PostgreSQL port opened. Ensure your database is properly secured!" -ForegroundColor Red
    }
    
    Write-Host "`nFirewall rules created successfully!" -ForegroundColor Green
    
    # Display created rules
    Write-Host "`nCreated firewall rules:" -ForegroundColor Cyan
    Get-NetFirewallRule | Where-Object { $_.DisplayName -like "Dogs Website*" } | Select-Object DisplayName, Direction, Action, Enabled | Format-Table -AutoSize
    
    # Test ports
    Write-Host "`nTesting port accessibility..." -ForegroundColor Yellow
    
    $Ports = @(80, 443, 3000)
    foreach ($Port in $Ports) {
        $Listener = $null
        try {
            $Listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $Port)
            $Listener.Start()
            Write-Host "Port $Port is available" -ForegroundColor Green
            $Listener.Stop()
        } catch {
            Write-Host "Port $Port is in use or blocked" -ForegroundColor Yellow
        } finally {
            if ($Listener) { $Listener.Stop() }
        }
    }
    
} catch {
    Write-Host "Error configuring firewall: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nFirewall configuration completed!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Configure your router to forward ports 80 and 443 to this computer" -ForegroundColor White
Write-Host "2. Ensure your ISP doesn't block these ports" -ForegroundColor White
Write-Host "3. Test external access using your DuckDNS domain" -ForegroundColor White
