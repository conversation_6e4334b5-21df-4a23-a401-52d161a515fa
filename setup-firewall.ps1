# Скрипт для настройки Windows Firewall для Dogs Website
# Запустите от имени администратора

Write-Host "🔥 Настройка Windows Firewall для Dogs Website..." -ForegroundColor Yellow

# Проверяем права администратора
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Этот скрипт должен быть запущен от имени администратора!" -ForegroundColor Red
    Write-Host "Щелкните правой кнопкой мыши на PowerShell и выберите 'Запуск от имени администратора'" -ForegroundColor Yellow
    pause
    exit 1
}

try {
    # Удаляем старые правила если они есть
    Write-Host "🧹 Удаление старых правил firewall..." -ForegroundColor Cyan
    netsh advfirewall firewall delete rule name="Dogs Website HTTP" 2>$null
    netsh advfirewall firewall delete rule name="Dogs Website HTTPS" 2>$null
    
    # Добавляем правило для HTTP (порт 8080)
    Write-Host "➕ Добавление правила для HTTP (порт 8080)..." -ForegroundColor Cyan
    $result1 = netsh advfirewall firewall add rule name="Dogs Website HTTP" dir=in action=allow protocol=TCP localport=8080
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Правило для порта 8080 добавлено успешно" -ForegroundColor Green
    } else {
        Write-Host "❌ Ошибка при добавлении правила для порта 8080" -ForegroundColor Red
    }
    
    # Добавляем правило для HTTPS (порт 443) - на будущее
    Write-Host "➕ Добавление правила для HTTPS (порт 443)..." -ForegroundColor Cyan
    $result2 = netsh advfirewall firewall add rule name="Dogs Website HTTPS" dir=in action=allow protocol=TCP localport=443
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Правило для порта 443 добавлено успешно" -ForegroundColor Green
    } else {
        Write-Host "❌ Ошибка при добавлении правила для порта 443" -ForegroundColor Red
    }
    
    # Показываем созданные правила
    Write-Host "`n📋 Созданные правила firewall:" -ForegroundColor Yellow
    netsh advfirewall firewall show rule name="Dogs Website HTTP"
    netsh advfirewall firewall show rule name="Dogs Website HTTPS"
    
    Write-Host "`n✅ Настройка firewall завершена!" -ForegroundColor Green
    Write-Host "Теперь ваш сайт должен быть доступен из интернета по адресу:" -ForegroundColor Cyan
    Write-Host "http://ta-shiba.duckdns.org:8080" -ForegroundColor White
    
} catch {
    Write-Host "❌ Произошла ошибка: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nНажмите любую клавишу для выхода..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
