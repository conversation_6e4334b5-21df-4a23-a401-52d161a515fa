'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import Select from '@/components/ui/Select';
import FileUpload from '@/components/ui/FileUpload';
import Alert from '@/components/ui/Alert';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { slugify } from '@/lib/utils';

interface Dog {
  id: string;
  name: string;
  breed: string;
  gender: 'MALE' | 'FEMALE';
}

interface Breeding {
  id: string;
  mother: Dog;
  father: Dog;
  date: string;
}

const puppySchema = z.object({
  name: z.string().min(1, 'Имя обязательно'),
  gender: z.enum(['MALE', 'FEMALE']),
  birthDate: z.string().min(1, 'Дата рождения обязательна'),
  color: z.string().optional(),
  description: z.string().optional(),
  status: z.enum(['AVAILABLE', 'RESERVED', 'SOLD']).default('AVAILABLE'),
  price: z.string().optional(),
  isPublished: z.boolean().default(true),
  breedingId: z.string().optional(),
});

type PuppyFormData = z.infer<typeof puppySchema>;

export default function CreatePuppyPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [photos, setPhotos] = useState<File[]>([]);
  const [breedings, setBreedings] = useState<Breeding[]>([]);
  const [isLoadingBreedings, setIsLoadingBreedings] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors },
  } = useForm<PuppyFormData>({
    resolver: zodResolver(puppySchema),
    defaultValues: {
      gender: 'MALE',
      status: 'AVAILABLE',
      isPublished: true,
    },
  });

  const status = watch('status');

  useEffect(() => {
    const fetchBreedings = async () => {
      setIsLoadingBreedings(true);
      try {
        const response = await fetch('/api/breedings');

        if (!response.ok) {
          throw new Error('Ошибка при загрузке вязок');
        }

        const data = await response.json();

        // Фильтруем только завершенные и активные вязки
        const filteredBreedings = data.filter(
          (breeding: Breeding) => breeding.status === 'COMPLETED' || breeding.status === 'IN_PROGRESS'
        );

        setBreedings(filteredBreedings);
      } catch (err) {
        console.error('Ошибка при загрузке вязок:', err);
        setError('Произошла ошибка при загрузке списка вязок');
      } finally {
        setIsLoadingBreedings(false);
      }
    };

    fetchBreedings();
  }, []);

  const onSubmit = async (data: PuppyFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Создаем slug из имени щенка
      const slug = slugify(data.name);

      // Создаем объект с данными щенка
      const puppyData = {
        ...data,
        slug,
        price: data.price ? parseFloat(data.price) : null,
      };

      // Отправляем запрос на создание щенка
      const response = await fetch('/api/puppies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(puppyData),
      });

      if (!response.ok) {
        throw new Error('Ошибка при создании щенка');
      }

      const createdPuppy = await response.json();

      // Если есть фотографии, загружаем их
      if (photos.length > 0) {
        for (let i = 0; i < photos.length; i++) {
          const formData = new FormData();
          formData.append('file', photos[i]);
          formData.append('puppyId', createdPuppy.id);
          formData.append('isMain', i === 0 ? 'true' : 'false');
          formData.append('order', i.toString());

          const photoResponse = await fetch('/api/photos', {
            method: 'POST',
            body: formData,
          });

          if (!photoResponse.ok) {
            console.error('Ошибка при загрузке фотографии:', await photoResponse.text());
          }
        }
      }

      setSuccess('Щенок успешно создан');

      // Перенаправляем на страницу со списком щенков
      setTimeout(() => {
        router.push('/admin/puppies');
      }, 2000);
    } catch (err) {
      console.error('Ошибка при создании щенка:', err);
      setError('Произошла ошибка при создании щенка. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePhotosChange = (files: File[]) => {
    setPhotos(files);
  };

  return (
    <AdminLayout title="Добавление нового щенка">
      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Основная информация</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Имя щенка"
              {...register('name')}
              error={errors.name?.message}
              fullWidth
            />
            <Controller
              name="gender"
              control={control}
              render={({ field }) => (
                <Select
                  label="Пол"
                  options={[
                    { value: 'MALE', label: 'Кобель' },
                    { value: 'FEMALE', label: 'Сука' },
                  ]}
                  {...field}
                  error={errors.gender?.message}
                  fullWidth
                />
              )}
            />
            <Input
              label="Дата рождения"
              type="date"
              {...register('birthDate')}
              error={errors.birthDate?.message}
              fullWidth
            />
            <Input
              label="Окрас"
              {...register('color')}
              error={errors.color?.message}
              fullWidth
            />
            <Controller
              name="breedingId"
              control={control}
              render={({ field }) => (
                <Select
                  label="Вязка"
                  options={[
                    { value: '', label: 'Выберите вязку' },
                    ...breedings.map((breeding) => ({
                      value: breeding.id,
                      label: `${breeding.mother.name} (${breeding.mother.breed}) x ${breeding.father.name} (${breeding.father.breed})`,
                    })),
                  ]}
                  {...field}
                  error={errors.breedingId?.message}
                  fullWidth
                  disabled={isLoadingBreedings}
                />
              )}
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Дополнительная информация</h2>
          <div className="space-y-6">
            <Textarea
              label="Описание"
              {...register('description')}
              error={errors.description?.message}
              rows={5}
              fullWidth
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Статус и продажа</h2>
          <div className="space-y-6">
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select
                  label="Статус"
                  options={[
                    { value: 'AVAILABLE', label: 'Доступен' },
                    { value: 'RESERVED', label: 'Зарезервирован' },
                    { value: 'SOLD', label: 'Продан' },
                  ]}
                  {...field}
                  error={errors.status?.message}
                  fullWidth
                />
              )}
            />

            {status === 'AVAILABLE' && (
              <Input
                label="Цена (руб.)"
                type="number"
                {...register('price')}
                error={errors.price?.message}
                fullWidth
              />
            )}

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPublished"
                {...register('isPublished')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
                Опубликовать на сайте
              </label>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Фотографии</h2>
          <div className="space-y-6">
            <FileUpload
              label="Загрузите фотографии щенка (первая фотография будет использоваться как основная)"
              accept="image/*"
              multiple
              onChange={handlePhotosChange}
              fullWidth
            />
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => router.push('/admin/puppies')}
          >
            Отмена
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            Создать
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
