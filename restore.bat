@echo off
echo 🚀 Восстановление backup базы данных...

if "%1"=="" (
    echo ❌ Укажите файл для восстановления
    echo Использование: restore.bat имя_файла.json.gz
    echo.
    echo 📁 Доступные файлы:
    dir /b backups\*.json.gz
    pause
    exit /b 1
)

set BACKUP_FILE=%1

if not exist "backups\%BACKUP_FILE%" (
    echo ❌ Файл не найден: backups\%BACKUP_FILE%
    echo.
    echo 📁 Доступные файлы:
    dir /b backups\*.json.gz
    pause
    exit /b 1
)

echo ✅ Файл найден: backups\%BACKUP_FILE%
echo 🔄 Запускаем восстановление...

node scripts/import-data.js "backups\%BACKUP_FILE%"

if %ERRORLEVEL% EQU 0 (
    echo ✅ Восстановление завершено успешно!
) else (
    echo ❌ Ошибка при восстановлении
)

pause
