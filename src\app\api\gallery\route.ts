import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/gallery - Получение списка альбомов
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');

    // Формируем условия фильтрации
    const where: any = { isPublished: true };

    if (categoryId) {
      where.categoryId = categoryId;
    }

    const albums = await prisma.gallery.findMany({
      where,
      include: {
        category: true,
        photos: {
          where: { isMain: true },
          take: 1
        }
      },
      orderBy: [
        { order: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    return NextResponse.json(albums);
  } catch (error) {
    console.error('Ошибка при получении списка альбомов:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении списка альбомов' },
      { status: 500 }
    );
  }
}

// POST /api/gallery - Создание нового альбома (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Проверяем, существует ли категория, если она указана
    if (data.categoryId) {
      const category = await prisma.galleryCategory.findUnique({
        where: { id: data.categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'Указанная категория не найдена' },
          { status: 400 }
        );
      }
    }

    // Создаем новый альбом
    const album = await prisma.gallery.create({
      data: {
        title: data.title,
        description: data.description,
        slug: data.slug,
        coverImage: data.coverImage,
        order: data.order || 0,
        isPublished: data.isPublished !== undefined ? data.isPublished : true,
        categoryId: data.categoryId === '' ? null : data.categoryId
      }
    });

    return NextResponse.json(album, { status: 201 });
  } catch (error) {
    console.error('Ошибка при создании альбома:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании альбома' },
      { status: 500 }
    );
  }
}
