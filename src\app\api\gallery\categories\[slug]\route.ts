import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/gallery/categories/[slug] - Получение информации о конкретной категории
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;
    
    const category = await prisma.galleryCategory.findUnique({
      where: { slug },
      include: {
        albums: {
          where: { isPublished: true },
          include: {
            photos: {
              where: { isMain: true },
              take: 1
            }
          },
          orderBy: [
            { order: 'asc' },
            { createdAt: 'desc' }
          ]
        }
      }
    });
    
    if (!category) {
      return NextResponse.json(
        { error: 'Категория не найдена' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(category);
  } catch (error) {
    console.error('Ошибка при получении информации о категории:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении информации о категории' },
      { status: 500 }
    );
  }
}

// PUT /api/gallery/categories/[slug] - Обновление информации о категории (защищенный маршрут)
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const slug = params.slug;
    const data = await request.json();
    
    // Проверяем, существует ли категория
    const existingCategory = await prisma.galleryCategory.findUnique({
      where: { slug }
    });
    
    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Категория не найдена' },
        { status: 404 }
      );
    }
    
    // Обновляем информацию о категории
    const updatedCategory = await prisma.galleryCategory.update({
      where: { slug },
      data: {
        name: data.name,
        description: data.description,
        order: data.order
      }
    });
    
    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error('Ошибка при обновлении информации о категории:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении информации о категории' },
      { status: 500 }
    );
  }
}

// DELETE /api/gallery/categories/[slug] - Удаление категории (защищенный маршрут)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const slug = params.slug;
    
    // Проверяем, существует ли категория
    const existingCategory = await prisma.galleryCategory.findUnique({
      where: { slug }
    });
    
    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Категория не найдена' },
        { status: 404 }
      );
    }
    
    // Проверяем, есть ли альбомы в этой категории
    const albumsCount = await prisma.gallery.count({
      where: { categoryId: existingCategory.id }
    });
    
    if (albumsCount > 0) {
      return NextResponse.json(
        { error: 'Невозможно удалить категорию, так как в ней есть альбомы' },
        { status: 400 }
      );
    }
    
    // Удаляем категорию
    await prisma.galleryCategory.delete({
      where: { slug }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении категории:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении категории' },
      { status: 500 }
    );
  }
}
