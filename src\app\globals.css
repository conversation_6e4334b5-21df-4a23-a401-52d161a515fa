@tailwind base;
@tailwind components;
@tailwind utilities;

/* Импорт стилей для админ-панели */
@import '../styles/admin.css';

/* Импорт цветовой палитры */
@import '../styles/colors.css';

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary: #f3f4f6;
  --secondary-hover: #e5e7eb;
  --accent: #f59e0b;
  --accent-hover: #d97706;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

@layer base {
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }

  h1 {
    @apply text-4xl md:text-5xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  h5 {
    @apply text-lg md:text-xl;
  }

  h6 {
    @apply text-base md:text-lg;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-semibold transition-colors;
  }

  .btn-primary {
    @apply bg-forest-medium hover:bg-forest-dark text-white;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-100 text-gray-900 border border-gray-300;
  }

  .container {
    @apply px-4 mx-auto;
  }

  .section {
    @apply py-16;
  }

  .section-title {
    @apply text-3xl font-bold mb-4 text-center;
  }

  .section-divider {
    @apply w-20 h-1 bg-blue-600 mx-auto;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
