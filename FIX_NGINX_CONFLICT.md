# Исправление конфликта server name в nginx

## Проблема

Ошибка `nginx: [warn] conflicting server name "ta-shiba.duckdns.org" on 0.0.0.0:80, ignored` означает, что у вас есть два или более блока `server` с одинаковым `server_name` на одном порту.

## Быстрое решение

Используйте версию конфигурации без конфликтов:

```powershell
# Остановите nginx
Get-Process | Where-Object { $_.Name -like "*nginx*" } | Stop-Process -Force

# Используйте версию без конфликтов
Copy-Item "nginx-dogs-noconflict.conf" "C:\nginx\conf\nginx.conf"

# Проверьте конфигурацию
C:\nginx\nginx.exe -t

# Запустите nginx
C:\nginx\nginx.exe
```

## Что исправлено

1. **Уникальные server_name** - каждый блок server имеет уникальное имя
2. **Default server** - добавлен catch-all сервер для неизвестных доменов
3. **Wildcard поддержка** - добавлена поддержка поддоменов `*.ta-shiba.duckdns.org`

## Структура серверов

### Порт 8080 (основной)
```nginx
server {
    listen 8080;
    server_name localhost ta-shiba.duckdns.org *.ta-shiba.duckdns.org;
    # Основное приложение
}
```

### Порт 80 (редирект)
```nginx
server {
    listen 80;
    server_name ta-shiba.duckdns.org;
    # Редирект на порт 8080
}
```

### Порт 80 (default)
```nginx
server {
    listen 80 default_server;
    server_name _;
    # Catch-all для неизвестных доменов
}
```

### Порт 443 (HTTPS)
```nginx
server {
    listen 443 ssl http2;
    server_name ta-shiba.duckdns.org;
    # SSL конфигурация
}
```

## Проверка после исправления

```powershell
# Проверьте синтаксис
C:\nginx\nginx.exe -t

# Должно показать:
# nginx: the configuration file C:\nginx\conf\nginx.conf syntax is ok
# nginx: configuration file C:\nginx\conf\nginx.conf test is successful

# Проверьте процессы
Get-Process | Where-Object { $_.Name -like "*nginx*" }

# Протестируйте доступность
.\test-ta-shiba.ps1
```

## Диагностика конфликтов

Если проблема остается, проверьте:

### 1. Другие файлы конфигурации

```powershell
# Найдите все файлы .conf
Get-ChildItem "C:\nginx" -Recurse -Filter "*.conf"

# Проверьте содержимое на дубликаты server_name
Select-String -Path "C:\nginx\conf\*.conf" -Pattern "server_name.*ta-shiba"
```

### 2. Включенные файлы

Проверьте, есть ли в nginx.conf строки типа:
```nginx
include conf.d/*.conf;
include sites-enabled/*;
```

### 3. Полная конфигурация

```powershell
# Посмотрите полную конфигурацию nginx
C:\nginx\nginx.exe -T
```

## Альтернативное решение

Если конфликт остается, используйте только порт 8080:

```nginx
# Минимальная конфигурация только для порта 8080
server {
    listen 8080;
    server_name localhost ta-shiba.duckdns.org;
    
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## Тестирование

После исправления:

```powershell
# Локальный тест
Test-NetConnection -ComputerName localhost -Port 8080

# Веб-тест
Invoke-WebRequest -Uri "http://localhost:8080" -UseBasicParsing

# Полный тест
.\test-ta-shiba.ps1 -Verbose
```

## Доступ после исправления

Ваш сайт будет доступен:

- ✅ **http://localhost:8080** - основной доступ
- ✅ **http://ta-shiba.duckdns.org:8080** - через DuckDNS
- ✅ **http://ta-shiba.duckdns.org** - редирект на порт 8080

## Логи для диагностики

```powershell
# Логи ошибок
Get-Content "C:\nginx\logs\error.log" -Tail 20

# Поиск конфликтов в логах
Select-String -Path "C:\nginx\logs\error.log" -Pattern "conflicting"
```

Попробуйте новую конфигурацию `nginx-dogs-noconflict.conf` - она должна решить проблему с конфликтами!
